const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client with service role key for bypassing RLS
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function seedData() {
  try {
    console.log('🌱 Starting seed data creation...');

    // Get a real user ID from the database
    console.log('👤 Getting user ID...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (usersError || !users || users.length === 0) {
      console.error('❌ Error getting users or no users found:', usersError);
      console.log('ℹ️  Please create a user first or run this script with an existing user');
      return;
    }

    const userId = users[0].id;
    console.log('✅ Using user ID:', userId);

    // Sample clients data
    const clientsData = [
      {
        name: 'Acme Corporation',
        email: '<EMAIL>',
        phone: '+91-9876543210',
        address: '123 Business District, Mumbai, Maharashtra 400001',
        gst_number: '27AABCU9603R1ZX',
        has_gst: true,
        client_type: 'corporate',
        notes: 'Premium corporate client with multiple projects'
      },
      {
        name: 'Tech Innovations Pvt Ltd',
        email: '<EMAIL>',
        phone: '+91-9876543211',
        address: '456 Tech Park, Bangalore, Karnataka 560001',
        gst_number: '29AABCT1332L1ZY',
        has_gst: true,
        client_type: 'startup',
        notes: 'Growing startup in the tech sector'
      },
      {
        name: 'Green Earth NGO',
        email: '<EMAIL>',
        phone: '+91-9876543212',
        address: '789 Environmental Center, Delhi 110001',
        gst_number: null,
        has_gst: false,
        client_type: 'non-profit',
        notes: 'Environmental NGO focused on sustainability projects'
      }
    ];

    // Insert clients first
    console.log('📋 Creating clients...');
    const { data: clients, error: clientsError } = await supabase
      .from('clients')
      .insert(clientsData)
      .select();

    if (clientsError) {
      console.error('❌ Error creating clients:', clientsError);
      return;
    }

    console.log('✅ Created clients:', clients.length);

    // Sample projects data
    const projectsData = [
      {
        name: 'Corporate Video Production',
        description: 'Complete corporate video production for brand promotion',
        client_id: clients[0].id,
        location: 'Mumbai, Maharashtra',
        google_maps_link: 'https://maps.google.com/?q=Mumbai,Maharashtra',
        status: 'active',
        total_amount: 150000,
        gst_inclusive: true,
        amount_received: 75000,
        amount_pending: 75000
      },
      {
        name: 'Product Launch Event Coverage',
        description: 'Video coverage of product launch event',
        client_id: clients[1].id,
        location: 'Bangalore, Karnataka',
        google_maps_link: 'https://maps.google.com/?q=Bangalore,Karnataka',
        status: 'active',
        total_amount: 80000,
        gst_inclusive: true,
        amount_received: 40000,
        amount_pending: 40000
      },
      {
        name: 'Environmental Documentary',
        description: 'Documentary on environmental conservation efforts',
        client_id: clients[2].id,
        location: 'Delhi',
        google_maps_link: 'https://maps.google.com/?q=Delhi',
        status: 'active',
        total_amount: 120000,
        gst_inclusive: false,
        amount_received: 60000,
        amount_pending: 60000
      }
    ];

    // Insert projects
    console.log('🚀 Creating projects...');
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .insert(projectsData)
      .select();

    if (projectsError) {
      console.error('❌ Error creating projects:', projectsError);
      return;
    }

    console.log('✅ Created projects:', projects.length);

    // Sample payments data
    const paymentsData = [
      {
        project_id: projects[0].id,
        amount: 75000,
        payment_date: '2024-01-20',
        payment_method: 'bank_transfer',
        reference_number: 'TXN001234',
        notes: 'Initial payment for corporate video production'
      },
      {
        project_id: projects[1].id,
        amount: 40000,
        payment_date: '2024-02-15',
        payment_method: 'bank_transfer',
        reference_number: 'TXN001235',
        notes: 'Payment for product launch event coverage'
      },
      {
        project_id: projects[2].id,
        amount: 60000,
        payment_date: '2024-02-28',
        payment_method: 'cheque',
        reference_number: 'CHQ789012',
        notes: 'Payment for environmental documentary'
      }
    ];

    // Insert payments
    console.log('💰 Creating payments...');
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .insert(paymentsData)
      .select();

    if (paymentsError) {
      console.error('❌ Error creating payments:', paymentsError);
      return;
    }

    console.log('✅ Created payments:', payments.length);

    // Sample expenses data
    const expensesData = [
      {
        description: 'Camera equipment maintenance',
        amount: 5500,
        category: 'gadgets',
        date: '2024-01-10',
        project_id: projects[0].id,
        user_id: userId
      },
      {
        description: 'Fuel for location shoots',
        amount: 2200,
        category: 'fuel_travel',
        date: '2024-01-15',
        project_id: projects[0].id,
        user_id: userId
      },
      {
        description: 'Video editing software license',
        amount: 15000,
        category: 'cymatics',
        date: '2024-02-01',
        project_id: null, // General expense
        user_id: userId
      },
      {
        description: 'Outsourcing video editing',
        amount: 8000,
        category: 'outsourcing',
        date: '2024-02-05',
        project_id: projects[1].id,
        user_id: userId
      }
    ];

    // Insert expenses
    console.log('💸 Creating expenses...');
    const { data: expenses, error: expensesError } = await supabase
      .from('expenses')
      .insert(expensesData)
      .select();

    if (expensesError) {
      console.error('❌ Error creating expenses:', expensesError);
      return;
    }

    console.log('✅ Created expenses:', expenses.length);

    console.log('\n🎉 Sample data created successfully!');
    console.log('📊 Summary:');
    console.log(`   - Clients: ${clients.length}`);
    console.log(`   - Projects: ${projects.length}`);
    console.log(`   - Payments: ${payments.length}`);
    console.log(`   - Expenses: ${expenses.length}`);

  } catch (error) {
    console.error('💥 Seed data creation error:', error);
  }
}

// Run the seed function
seedData().then(() => {
  console.log('🏁 Seed script completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Seed script failed:', error);
  process.exit(1);
});