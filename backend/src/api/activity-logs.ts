import { Router } from 'express';
import { createClient } from '@supabase/supabase-js';

const router = Router();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  console.error('SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
  console.error('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'Set' : 'Missing');
}

const supabase = supabaseUrl && supabaseServiceKey 
  ? createClient(supabaseUrl, supabaseServiceKey)
  : null;

// Middleware to check if Supabase is properly initialized
const requireSupabase = (req: any, res: any, next: any) => {
  if (!supabase) {
    return res.status(500).json({ error: 'Database connection not available' });
  }
  next();
};

// Middleware to verify authentication
const requireAuth = async (req: any, res: any, next: any) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase!.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({ error: 'Authentication error' });
  }
};

// Get activity logs with pagination and filtering
router.get('/', requireSupabase, requireAuth, async (req: any, res): Promise<any> => {
  try {
    const { limit = 50, offset = 0, user_id } = req.query;
    const currentUser = req.user;

    // Get user profile to check role
    const { data: userProfile, error: profileError } = await supabase!
      .from('users')
      .select('role')
      .eq('id', currentUser.id)
      .single();

    if (profileError) {
      return res.status(500).json({ error: 'Failed to get user profile' });
    }

    let query = supabase!
      .from('activity_logs')
      .select(`
        id,
        user_id,
        action,
        entity_type,
        entity_id,
        details,
        created_at,
        user:users(id, name, email)
      `)
      .order('created_at', { ascending: false })
      .limit(parseInt(limit))
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    // If not admin, only show user's own logs
    if (userProfile.role !== 'admin') {
      query = query.eq('user_id', currentUser.id);
    } else if (user_id) {
      // Admin can filter by specific user
      query = query.eq('user_id', user_id);
    }

    const { data: activityLogs, error } = await query;

    if (error) {
      console.error('Error fetching activity logs:', error);
      return res.status(500).json({ error: 'Failed to fetch activity logs' });
    }

    res.json({
      success: true,
      data: activityLogs || [],
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: activityLogs?.length || 0
      }
    });
  } catch (error) {
    console.error('Activity logs API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Get activity logs for a specific user (admin only)
router.get('/user/:userId', requireSupabase, requireAuth, async (req: any, res): Promise<any> => {
  try {
    const { userId } = req.params;
    const { limit = 20, offset = 0 } = req.query;
    const currentUser = req.user;

    // Get user profile to check role
    const { data: userProfile, error: profileError } = await supabase!
      .from('users')
      .select('role')
      .eq('id', currentUser.id)
      .single();

    if (profileError) {
      return res.status(500).json({ error: 'Failed to get user profile' });
    }

    // Only admins can view other users' activity logs
    if (userProfile.role !== 'admin' && currentUser.id !== userId) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    const { data: activityLogs, error } = await supabase!
      .from('activity_logs')
      .select(`
        id,
        user_id,
        action,
        entity_type,
        entity_id,
        details,
        created_at,
        user:users(id, name, email)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(parseInt(limit))
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    if (error) {
      console.error('Error fetching user activity logs:', error);
      return res.status(500).json({ error: 'Failed to fetch user activity logs' });
    }

    res.json({
      success: true,
      data: activityLogs || [],
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: activityLogs?.length || 0
      }
    });
  } catch (error) {
    console.error('User activity logs API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// Create activity log entry (for system use)
router.post('/', requireSupabase, requireAuth, async (req: any, res): Promise<any> => {
  try {
    const { action, entity_type, entity_id, details } = req.body;
    const currentUser = req.user;

    if (!action || !entity_type) {
      return res.status(400).json({ error: 'Action and entity_type are required' });
    }

    const { data: activityLog, error } = await supabase!
      .from('activity_logs')
      .insert({
        user_id: currentUser.id,
        action,
        entity_type,
        entity_id,
        details
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating activity log:', error);
      return res.status(500).json({ error: 'Failed to create activity log' });
    }

    res.status(201).json({
      success: true,
      data: activityLog
    });
  } catch (error) {
    console.error('Create activity log API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;