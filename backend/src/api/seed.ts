import { Router } from 'express';
import { createClient } from '@supabase/supabase-js';

const router = Router();

// Initialize Supabase client with service role key for bypassing RLS
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  throw new Error('Missing required Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// GET /api/seed/test - Test endpoint
router.get('/test', (req, res) => {
  res.json({ message: 'Seed router is working!' });
});

// POST /api/seed/data - Create sample data for testing
router.post('/data', async (req, res): Promise<any> => {
  try {
    console.log('Starting seed data creation...');

    // Sample clients data
    const clientsData = [
      {
        name: 'TechCorp Solutions',
        email: '<EMAIL>',
        phone: '******-0123',
        address: '123 Tech Street, Silicon Valley, CA 94000',
        company: 'TechCorp Solutions Inc.',
        status: 'active'
      },
      {
        name: 'Green Energy Ltd',
        email: '<EMAIL>',
        phone: '******-0456',
        address: '456 Renewable Ave, Austin, TX 78701',
        company: 'Green Energy Ltd.',
        status: 'active'
      }
    ];

    // Insert clients first
    const { data: clients, error: clientsError } = await supabase
      .from('clients')
      .insert(clientsData)
      .select();

    if (clientsError) {
      console.error('Error creating clients:', clientsError);
      return res.status(500).json({ error: 'Failed to create clients', details: clientsError });
    }

    console.log('Created clients:', clients);

    // Sample projects data
    const projectsData = [
      {
        name: 'Aerial Survey Project Alpha',
        description: 'Comprehensive aerial survey for urban planning',
        client_id: clients[0].id,
        status: 'active',
        total_amount: 25000,
        start_date: '2024-01-15',
        end_date: '2024-03-15'
      },
      {
        name: 'Infrastructure Inspection Beta',
        description: 'Drone-based infrastructure inspection and monitoring',
        client_id: clients[1].id,
        status: 'completed',
        total_amount: 18500,
        start_date: '2024-02-01',
        end_date: '2024-02-28'
      }
    ];

    // Insert projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .insert(projectsData)
      .select();

    if (projectsError) {
      console.error('Error creating projects:', projectsError);
      return res.status(500).json({ error: 'Failed to create projects', details: projectsError });
    }

    console.log('Created projects:', projects);

    // Sample payments data
    const paymentsData = [
      {
        project_id: projects[0].id,
        amount: 12500,
        payment_method: 'bank_transfer',
        status: 'completed',
        payment_date: '2024-01-20',
        description: 'Initial payment for Aerial Survey Project Alpha'
      },
      {
        project_id: projects[0].id,
        amount: 12500,
        payment_method: 'credit_card',
        status: 'pending',
        payment_date: '2024-03-20',
        description: 'Final payment for Aerial Survey Project Alpha'
      },
      {
        project_id: projects[1].id,
        amount: 18500,
        payment_method: 'bank_transfer',
        status: 'completed',
        payment_date: '2024-03-01',
        description: 'Full payment for Infrastructure Inspection Beta'
      }
    ];

    // Insert payments
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .insert(paymentsData)
      .select();

    if (paymentsError) {
      console.error('Error creating payments:', paymentsError);
      return res.status(500).json({ error: 'Failed to create payments', details: paymentsError });
    }

    console.log('Created payments:', payments);

    // Sample expenses data
    const expensesData = [
      {
        project_id: projects[0].id,
        amount: 2500,
        category: 'equipment',
        description: 'Drone equipment rental for survey project',
        expense_date: '2024-01-16',
        status: 'approved'
      },
      {
        project_id: projects[0].id,
        amount: 800,
        category: 'travel',
        description: 'Travel expenses for site survey',
        expense_date: '2024-01-18',
        status: 'approved'
      },
      {
        project_id: projects[1].id,
        amount: 1200,
        category: 'equipment',
        description: 'Specialized inspection sensors',
        expense_date: '2024-02-02',
        status: 'approved'
      },
      {
        project_id: projects[1].id,
        amount: 450,
        category: 'software',
        description: 'Data analysis software license',
        expense_date: '2024-02-05',
        status: 'approved'
      }
    ];

    // Insert expenses
    const { data: expenses, error: expensesError } = await supabase
      .from('expenses')
      .insert(expensesData)
      .select();

    if (expensesError) {
      console.error('Error creating expenses:', expensesError);
      return res.status(500).json({ error: 'Failed to create expenses', details: expensesError });
    }

    console.log('Created expenses:', expenses);

    res.json({
      success: true,
      message: 'Sample data created successfully',
      data: {
        clients: clients.length,
        projects: projects.length,
        payments: payments.length,
        expenses: expenses.length
      }
    });

  } catch (error: any) {
    console.error('Seed data creation error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    });
  }
});

export default router;