"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_notification-triggers_ts";
exports.ids = ["_rsc_src_lib_notification-triggers_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/notification-triggers.ts":
/*!******************************************!*\
  !*** ./src/lib/notification-triggers.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTaskUpdateNotification: () => (/* binding */ createTaskUpdateNotification),\n/* harmony export */   processNotificationTriggers: () => (/* binding */ processNotificationTriggers),\n/* harmony export */   queueNotificationTriggers: () => (/* binding */ queueNotificationTriggers),\n/* harmony export */   scheduleNotificationTriggers: () => (/* binding */ scheduleNotificationTriggers)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Notification Triggers System\n * Handles automated notification generation for payments, schedules, and tasks\n */ \nconst supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Use service role key for server-side operations\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n/**\n * Main function to process notification triggers\n */ async function processNotificationTriggers(triggerType, options = {}) {\n    console.log(`🔔 Processing notification triggers: ${triggerType}`);\n    try {\n        let totalCount = 0;\n        const errors = [];\n        switch(triggerType){\n            case \"payment_overdue\":\n                const paymentResult = await processPaymentOverdueNotifications(options);\n                totalCount += paymentResult.count;\n                if (paymentResult.errors) errors.push(...paymentResult.errors);\n                break;\n            case \"schedule_reminders\":\n                const scheduleResult = await processScheduleReminderNotifications(options);\n                totalCount += scheduleResult.count;\n                if (scheduleResult.errors) errors.push(...scheduleResult.errors);\n                break;\n            case \"task_updates\":\n                // Task updates are handled in real-time via triggers, not batch processing\n                console.log(\"ℹ️ Task updates are handled in real-time, skipping batch processing\");\n                break;\n            case \"all\":\n                const allResults = await Promise.allSettled([\n                    processPaymentOverdueNotifications(options),\n                    processScheduleReminderNotifications(options)\n                ]);\n                allResults.forEach((result, index)=>{\n                    if (result.status === \"fulfilled\") {\n                        totalCount += result.value.count;\n                        if (result.value.errors) errors.push(...result.value.errors);\n                    } else {\n                        errors.push(`Trigger ${index} failed: ${result.reason}`);\n                    }\n                });\n                break;\n            default:\n                throw new Error(`Unknown trigger type: ${triggerType}`);\n        }\n        return {\n            success: errors.length === 0,\n            message: `Processed ${triggerType} triggers, created ${totalCount} notifications`,\n            count: totalCount,\n            errors: errors.length > 0 ? errors : undefined\n        };\n    } catch (error) {\n        console.error(\"❌ Error processing notification triggers:\", error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : \"Unknown error\",\n            count: 0,\n            errors: [\n                error instanceof Error ? error.message : \"Unknown error\"\n            ]\n        };\n    }\n}\n/**\n * Process payment overdue notifications\n */ async function processPaymentOverdueNotifications(options = {}) {\n    try {\n        console.log(\"\\uD83D\\uDCB0 Processing payment overdue notifications...\");\n        if (options.dryRun) {\n            console.log(\"\\uD83D\\uDD0D Dry run mode - no notifications will be created\");\n        }\n        // Call the database function to create payment overdue notifications\n        const { data, error } = await supabase.rpc(\"create_payment_overdue_notifications\");\n        if (error) {\n            throw error;\n        }\n        const count = data || 0;\n        console.log(`✅ Created ${count} payment overdue notifications`);\n        return {\n            success: true,\n            message: `Created ${count} payment overdue notifications`,\n            count\n        };\n    } catch (error) {\n        console.error(\"❌ Error processing payment overdue notifications:\", error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : \"Unknown error\",\n            count: 0,\n            errors: [\n                error instanceof Error ? error.message : \"Unknown error\"\n            ]\n        };\n    }\n}\n/**\n * Process schedule reminder notifications\n */ async function processScheduleReminderNotifications(options = {}) {\n    try {\n        console.log(\"\\uD83D\\uDCC5 Processing schedule reminder notifications...\");\n        if (options.dryRun) {\n            console.log(\"\\uD83D\\uDD0D Dry run mode - no notifications will be created\");\n        }\n        // Call the database function to create schedule reminder notifications\n        const { data, error } = await supabase.rpc(\"create_schedule_reminder_notifications\");\n        if (error) {\n            throw error;\n        }\n        const count = data || 0;\n        console.log(`✅ Created ${count} schedule reminder notifications`);\n        return {\n            success: true,\n            message: `Created ${count} schedule reminder notifications`,\n            count\n        };\n    } catch (error) {\n        console.error(\"❌ Error processing schedule reminder notifications:\", error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : \"Unknown error\",\n            count: 0,\n            errors: [\n                error instanceof Error ? error.message : \"Unknown error\"\n            ]\n        };\n    }\n}\n/**\n * Create task update notification (called from task update triggers)\n */ async function createTaskUpdateNotification(taskId, oldStatus, newStatus, updatedByUserId) {\n    try {\n        console.log(`📋 Creating task update notification for task: ${taskId}`);\n        // Call the database function to create task update notification\n        const { error } = await supabase.rpc(\"create_task_update_notification\", {\n            task_id_param: taskId,\n            old_status: oldStatus,\n            new_status: newStatus,\n            updated_by_user_id: updatedByUserId\n        });\n        if (error) {\n            throw error;\n        }\n        console.log(`✅ Created task update notification for task: ${taskId}`);\n        return {\n            success: true,\n            message: `Created task update notification`,\n            count: 1\n        };\n    } catch (error) {\n        console.error(\"❌ Error creating task update notification:\", error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : \"Unknown error\",\n            count: 0,\n            errors: [\n                error instanceof Error ? error.message : \"Unknown error\"\n            ]\n        };\n    }\n}\n/**\n * Queue notification triggers as background job\n */ function queueNotificationTriggers(triggerType, options = {}) {\n    const { getBackgroundJobQueue } = __webpack_require__(/*! ./background-jobs */ \"(rsc)/./src/lib/background-jobs.ts\");\n    const queue = getBackgroundJobQueue();\n    const jobId = queue.addJob(\"notification_triggers\", \"notification\", \"system\", {\n        triggerType,\n        options\n    });\n    console.log(`📋 Notification triggers queued: ${triggerType} with job ID: ${jobId}`);\n    return jobId;\n}\n/**\n * Schedule periodic notification triggers\n * This should be called from a cron job or scheduled task\n */ function scheduleNotificationTriggers() {\n    console.log(\"⏰ Scheduling periodic notification triggers...\");\n    // Schedule payment overdue checks (daily at 9 AM)\n    const paymentJobId = queueNotificationTriggers(\"payment_overdue\");\n    console.log(`📅 Scheduled payment overdue check: ${paymentJobId}`);\n    // Schedule schedule reminder checks (every 30 minutes)\n    const scheduleJobId = queueNotificationTriggers(\"schedule_reminders\");\n    console.log(`📅 Scheduled schedule reminder check: ${scheduleJobId}`);\n    return {\n        paymentJobId,\n        scheduleJobId\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/notification-triggers.ts\n");

/***/ })

};
;