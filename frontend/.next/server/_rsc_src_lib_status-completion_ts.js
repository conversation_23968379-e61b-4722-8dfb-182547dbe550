"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_status-completion_ts";
exports.ids = ["_rsc_src_lib_status-completion_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/status-completion.ts":
/*!**************************************!*\
  !*** ./src/lib/status-completion.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAndCompleteProject: () => (/* binding */ checkAndCompleteProject),\n/* harmony export */   checkAndCompleteShoot: () => (/* binding */ checkAndCompleteShoot),\n/* harmony export */   handleTaskStatusChange: () => (/* binding */ handleTaskStatusChange)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(rsc)/./src/lib/auth.ts\");\n\nconst supabase = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.createClientSupabaseClient)();\n/**\n * Check if all shoot-based tasks for a specific shoot are completed\n * and automatically complete the shoot if they are\n */ async function checkAndCompleteShoot(shootId) {\n    try {\n        console.log(\"Checking shoot completion for shoot:\", shootId);\n        // Get the shoot details\n        const { data: shoot, error: shootError } = await supabase.from(\"shoots\").select(\"id, status, project_id\").eq(\"id\", shootId).single();\n        if (shootError || !shoot) {\n            console.error(\"Error fetching shoot:\", shootError);\n            return;\n        }\n        // Skip if shoot is already completed\n        if (shoot.status === \"completed\") {\n            console.log(\"Shoot is already completed\");\n            return;\n        }\n        // Get all shoot-based tasks for this shoot\n        const { data: shootTasks, error: tasksError } = await supabase.from(\"tasks\").select(\"id, title, status\").eq(\"shoot_id\", shootId).neq(\"status\", \"cancelled\") // Exclude cancelled tasks\n        ;\n        if (tasksError) {\n            console.error(\"Error fetching shoot tasks:\", tasksError);\n            return;\n        }\n        if (!shootTasks || shootTasks.length === 0) {\n            console.log(\"No shoot-based tasks found for shoot:\", shootId);\n            return;\n        }\n        // Check if all shoot-based tasks are completed\n        const allCompleted = shootTasks.every((task)=>task.status === \"completed\");\n        console.log(\"Shoot tasks status:\", shootTasks.map((t)=>({\n                title: t.title,\n                status: t.status\n            })));\n        console.log(\"All shoot tasks completed:\", allCompleted);\n        if (allCompleted) {\n            // Automatically complete the shoot\n            const { error: updateError } = await supabase.from(\"shoots\").update({\n                status: \"completed\",\n                actual_date: new Date().toISOString()\n            }).eq(\"id\", shootId);\n            if (updateError) {\n                console.error(\"Error updating shoot status:\", updateError);\n                return;\n            }\n            console.log(\"Shoot automatically completed:\", shootId);\n            // Now check if the project should be completed\n            await checkAndCompleteProject(shoot.project_id);\n        }\n    } catch (error) {\n        console.error(\"Error in checkAndCompleteShoot:\", error);\n    }\n}\n/**\n * Check if all shoots and project-level tasks for a project are completed\n * and automatically complete the project if they are\n */ async function checkAndCompleteProject(projectId) {\n    try {\n        console.log(\"Checking project completion for project:\", projectId);\n        // Get the project details\n        const { data: project, error: projectError } = await supabase.from(\"projects\").select(\"id, status\").eq(\"id\", projectId).single();\n        if (projectError || !project) {\n            console.error(\"Error fetching project:\", projectError);\n            return;\n        }\n        // Skip if project is already completed\n        if (project.status === \"completed\") {\n            console.log(\"Project is already completed\");\n            return;\n        }\n        // Get all shoots for this project\n        const { data: shoots, error: shootsError } = await supabase.from(\"shoots\").select(\"id, status\").eq(\"project_id\", projectId).neq(\"status\", \"cancelled\") // Exclude cancelled shoots\n        ;\n        if (shootsError) {\n            console.error(\"Error fetching project shoots:\", shootsError);\n            return;\n        }\n        // Check if all shoots are completed\n        const allShootsCompleted = shoots?.every((shoot)=>shoot.status === \"completed\") ?? true;\n        console.log(\"Project shoots status:\", shoots?.map((s)=>({\n                id: s.id,\n                status: s.status\n            })));\n        console.log(\"All shoots completed:\", allShootsCompleted);\n        if (!allShootsCompleted) {\n            console.log(\"Not all shoots are completed yet\");\n            return;\n        }\n        // Get all project-level tasks (tasks without shoot_id)\n        const { data: projectTasks, error: projectTasksError } = await supabase.from(\"tasks\").select(\"id, title, status\").eq(\"project_id\", projectId).is(\"shoot_id\", null) // Project-level tasks have no shoot_id\n        .neq(\"status\", \"cancelled\") // Exclude cancelled tasks\n        ;\n        if (projectTasksError) {\n            console.error(\"Error fetching project tasks:\", projectTasksError);\n            return;\n        }\n        // Check if all project-level tasks are completed\n        const allProjectTasksCompleted = projectTasks?.every((task)=>task.status === \"completed\") ?? true;\n        console.log(\"Project tasks status:\", projectTasks?.map((t)=>({\n                title: t.title,\n                status: t.status\n            })));\n        console.log(\"All project tasks completed:\", allProjectTasksCompleted);\n        if (allShootsCompleted && allProjectTasksCompleted) {\n            // Automatically complete the project\n            const { error: updateError } = await supabase.from(\"projects\").update({\n                status: \"completed\"\n            }).eq(\"id\", projectId);\n            if (updateError) {\n                console.error(\"Error updating project status:\", updateError);\n                return;\n            }\n            console.log(\"Project automatically completed:\", projectId);\n        }\n    } catch (error) {\n        console.error(\"Error in checkAndCompleteProject:\", error);\n    }\n}\n/**\n * Handle task status change and trigger automatic completion checks\n */ async function handleTaskStatusChange(task, newStatus) {\n    try {\n        console.log(\"Handling task status change:\", {\n            taskId: task.id,\n            title: task.title,\n            newStatus\n        });\n        // If task is completed, check for automatic completion\n        if (newStatus === \"completed\") {\n            // If it's a shoot-based task, check shoot completion\n            if (task.shoot_id) {\n                await checkAndCompleteShoot(task.shoot_id);\n            } else if (task.project_id) {\n                await checkAndCompleteProject(task.project_id);\n            }\n        }\n    } catch (error) {\n        console.error(\"Error in handleTaskStatusChange:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/status-completion.ts\n");

/***/ })

};
;