"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_forms_ProjectForm_tsx";
exports.ids = ["_ssr_src_components_forms_ProjectForm_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/forms/ClientForm.tsx":
/*!*********************************************!*\
  !*** ./src/components/forms/ClientForm.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientForm: () => (/* binding */ ClientForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v4/classic/external.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useApi */ \"(ssr)/./src/hooks/useApi.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ ClientForm auto */ \n\n\n\n\n\n\n\n\n\n\nconst clientSchema = zod__WEBPACK_IMPORTED_MODULE_9__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(1, \"Name is required\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_9__.string().email(\"Invalid email\").optional().or(zod__WEBPACK_IMPORTED_MODULE_9__.literal(\"\")),\n    phone: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n    gst_number: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n    has_gst: zod__WEBPACK_IMPORTED_MODULE_9__.boolean(),\n    client_type: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional()\n});\nfunction ClientForm({ client, onSuccess, onCancel, initialName }) {\n    const isEditing = !!client;\n    const { createClient, loading: createLoading } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useCreateClient)();\n    const { updateClient, loading: updateLoading } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useUpdateClient)();\n    const loading = createLoading || updateLoading;\n    const [previewId, setPreviewId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load preview ID only in browser and only for create mode\n    // Avoids blocking submit and avoids refetching after submit\n    if (false) {}\n    const { register, handleSubmit, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(clientSchema),\n        defaultValues: client ? {\n            name: client.name,\n            email: client.email || \"\",\n            phone: client.phone || \"\",\n            address: client.address || \"\",\n            gst_number: client.gst_number || \"\",\n            has_gst: client.has_gst,\n            client_type: client.client_type || \"\",\n            notes: client.notes || \"\"\n        } : {\n            name: initialName || \"\",\n            email: \"\",\n            phone: \"\",\n            address: \"\",\n            gst_number: \"\",\n            has_gst: false,\n            client_type: \"\",\n            notes: \"\"\n        }\n    });\n    // Effect: fetch preview custom_id on mount for create mode\n    // Placed after form setup so it can run once on component render\n    // Ensures no changes to actual create/insert logic\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let mounted = true;\n        async function loadPreview() {\n            try {\n                const { peekNextId } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(ssr)/./src/lib/api.ts\"));\n                const id = await peekNextId(\"client\");\n                if (mounted) setPreviewId(id);\n            } catch (e) {\n                console.warn(\"ClientForm preview load failed:\", e);\n            }\n        }\n        if (false) {}\n        return ()=>{\n            mounted = false;\n        };\n    }, [\n        isEditing\n    ]);\n    const onSubmit = async (data)=>{\n        try {\n            // Convert empty strings to null for optional fields\n            const cleanData = {\n                ...data,\n                email: data.email || null,\n                phone: data.phone || null,\n                address: data.address || null,\n                gst_number: data.gst_number || null,\n                has_gst: data.has_gst,\n                client_type: data.client_type || null,\n                notes: data.notes || null\n            };\n            let result;\n            if (isEditing) {\n                result = await updateClient(client.id, cleanData);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Client updated successfully\");\n            } else {\n                result = await createClient(cleanData);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Client created successfully\");\n                reset();\n            }\n            onSuccess?.(result);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(error.message || \"Failed to save client\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        className: \"space-y-4\",\n        children: [\n            isEditing && client?.custom_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-muted-foreground -mb-2\",\n                children: [\n                    \"ID: \",\n                    client.custom_id\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this),\n            !isEditing && previewId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-muted-foreground -mb-2\",\n                children: [\n                    \"ID (preview): \",\n                    previewId\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"name\",\n                        children: \"Name *\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        id: \"name\",\n                        ...register(\"name\"),\n                        placeholder: \"Enter client name\",\n                        className: \"mt-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.name.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"email\",\n                        children: \"Email\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        id: \"email\",\n                        type: \"email\",\n                        ...register(\"email\"),\n                        placeholder: \"Enter email address\",\n                        className: \"mt-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.email.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"phone\",\n                        children: \"Phone\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        id: \"phone\",\n                        ...register(\"phone\"),\n                        placeholder: \"Enter phone number\",\n                        className: \"mt-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.phone.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"address\",\n                        children: \"Address\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        id: \"address\",\n                        ...register(\"address\"),\n                        placeholder: \"Enter address\",\n                        className: \"mt-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.address.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"client_type\",\n                        children: \"Client Type\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        id: \"client_type\",\n                        ...register(\"client_type\"),\n                        className: \"mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Select client type\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            _lib_constants__WEBPACK_IMPORTED_MODULE_8__.CLIENT_TYPES.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: type,\n                                    children: type\n                                }, type, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    errors.client_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.client_type.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"gst_number\",\n                        children: \"GST Number\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        id: \"gst_number\",\n                        ...register(\"gst_number\"),\n                        placeholder: \"Enter GST number (e.g., 22AAAAA0000A1Z5)\",\n                        className: \"mt-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    errors.gst_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.gst_number.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"has_gst\",\n                        children: \"GST Registration\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                id: \"has_gst\",\n                                type: \"checkbox\",\n                                ...register(\"has_gst\"),\n                                className: \"rounded border-border text-primary focus:ring-primary\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"has_gst\",\n                                className: \"text-sm text-foreground\",\n                                children: \"Client has GST registration\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    errors.has_gst && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.has_gst.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"notes\",\n                        children: \"Notes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        id: \"notes\",\n                        ...register(\"notes\"),\n                        placeholder: \"Enter any additional notes\",\n                        className: \"mt-1 flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                        rows: 3\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    errors.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.notes.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-3 pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        disabled: loading,\n                        className: \"flex-1\",\n                        children: loading ? \"Saving...\" : isEditing ? \"Update Client\" : \"Create Client\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        disabled: loading,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ClientForm.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/forms/ClientForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/forms/ContactPersonForm.tsx":
/*!****************************************************!*\
  !*** ./src/components/forms/ContactPersonForm.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactPersonForm: () => (/* binding */ ContactPersonForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v4/classic/external.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ ContactPersonForm auto */ \n\n\n\n\n\n\n\n\n\n\nconst contactPersonSchema = zod__WEBPACK_IMPORTED_MODULE_8__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_8__.string().min(1, \"Name is required\"),\n    phone: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_8__.string().email(\"Invalid email\").optional().or(zod__WEBPACK_IMPORTED_MODULE_8__.literal(\"\")),\n    designation: zod__WEBPACK_IMPORTED_MODULE_8__.string().optional(),\n    is_primary: zod__WEBPACK_IMPORTED_MODULE_8__.boolean().optional()\n});\nfunction ContactPersonForm({ clientId, contactPerson, onSuccess, onCancel }) {\n    const isEditing = !!contactPerson;\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(contactPersonSchema),\n        defaultValues: contactPerson ? {\n            name: contactPerson.name,\n            phone: contactPerson.phone || \"\",\n            email: contactPerson.email || \"\",\n            designation: contactPerson.designation || \"\",\n            is_primary: contactPerson.is_primary\n        } : {\n            name: \"\",\n            phone: \"\",\n            email: \"\",\n            designation: \"\",\n            is_primary: false\n        }\n    });\n    const onSubmit = async (data)=>{\n        try {\n            setLoading(true);\n            // Convert empty strings to null for optional fields\n            const cleanData = {\n                ...data,\n                phone: data.phone || null,\n                email: data.email || null,\n                designation: data.designation || null\n            };\n            let result;\n            if (isEditing) {\n                result = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.contactPersonsApi.update(contactPerson.id, cleanData);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Contact person updated successfully\");\n            } else {\n                result = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.contactPersonsApi.create({\n                    ...cleanData,\n                    client_id: clientId\n                });\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Contact person added successfully\");\n                reset();\n            }\n            // If this contact is set as primary, update the primary status\n            if (data.is_primary) {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_6__.contactPersonsApi.setPrimary(result.id, clientId);\n            }\n            onSuccess?.(result);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(error.message || \"Failed to save contact person\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"name\",\n                        children: \"Name *\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        id: \"name\",\n                        ...register(\"name\"),\n                        placeholder: \"Enter contact person name\",\n                        className: \"mt-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.name.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"phone\",\n                        children: \"Phone\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        id: \"phone\",\n                        ...register(\"phone\"),\n                        placeholder: \"Enter phone number\",\n                        className: \"mt-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.phone.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"email\",\n                        children: \"Email\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        id: \"email\",\n                        type: \"email\",\n                        ...register(\"email\"),\n                        placeholder: \"Enter email address\",\n                        className: \"mt-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.email.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"designation\",\n                        children: \"Designation\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        id: \"designation\",\n                        ...register(\"designation\"),\n                        placeholder: \"Enter job title or designation\",\n                        className: \"mt-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    errors.designation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600 dark:text-red-400 mt-1\",\n                        children: errors.designation.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"checkbox\",\n                        id: \"is_primary\",\n                        ...register(\"is_primary\"),\n                        className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                        htmlFor: \"is_primary\",\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Primary Contact\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-3 pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        disabled: loading,\n                        className: \"flex-1\",\n                        children: loading ? \"Saving...\" : isEditing ? \"Update Contact\" : \"Add Contact\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        disabled: loading,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ContactPersonForm.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/forms/ContactPersonForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/forms/ProjectForm.tsx":
/*!**********************************************!*\
  !*** ./src/components/forms/ProjectForm.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectForm: () => (/* binding */ ProjectForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v4/classic/external.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_location_search__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/location-search */ \"(ssr)/./src/components/ui/location-search.tsx\");\n/* harmony import */ var _components_ui_client_selector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/client-selector */ \"(ssr)/./src/components/ui/client-selector.tsx\");\n/* harmony import */ var _components_ui_contact_person_selector__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/contact-person-selector */ \"(ssr)/./src/components/ui/contact-person-selector.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useApi */ \"(ssr)/./src/hooks/useApi.ts\");\n/* harmony import */ var _lib_maps_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/maps-utils */ \"(ssr)/./src/lib/maps-utils.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ProjectForm auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nconst projectSchema = zod__WEBPACK_IMPORTED_MODULE_12__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, \"Project name is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    client_id: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, \"Client is required\"),\n    contact_person_id: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    location: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    google_maps_link: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional().refine((val)=>{\n        if (!val || val.trim() === \"\") return true;\n        try {\n            new URL(val);\n            return true;\n        } catch  {\n            return false;\n        }\n    }, {\n        message: \"Invalid URL\"\n    })\n});\nfunction ProjectForm({ project, onSuccess, onCancel, preselectedClientId }) {\n    const isEditing = !!project;\n    const { createProject, loading: createLoading } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_9__.useCreateProject)();\n    const { updateProject, loading: updateLoading } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_9__.useUpdateProject)();\n    const loading = createLoading || updateLoading;\n    const [extractingLocation, setExtractingLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewId, setPreviewId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { register, handleSubmit, formState: { errors }, reset, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(projectSchema),\n        defaultValues: project ? {\n            name: project.name,\n            description: project.description || \"\",\n            client_id: project.client_id,\n            contact_person_id: project.contact_person_id || \"\",\n            location: project.location || \"\",\n            google_maps_link: project.google_maps_link || \"\"\n        } : {\n            name: \"\",\n            description: \"\",\n            client_id: preselectedClientId || \"\",\n            contact_person_id: \"\",\n            location: \"\",\n            google_maps_link: \"\"\n        }\n    });\n    // Watch for changes in Google Maps link\n    const googleMapsLink = watch(\"google_maps_link\");\n    // Create debounced location extractor\n    const debouncedExtractLocation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((0,_lib_maps_utils__WEBPACK_IMPORTED_MODULE_10__.createDebouncedLocationExtractor)(1500), []);\n    // Extract location when Google Maps link changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (googleMapsLink && googleMapsLink.trim()) {\n            setExtractingLocation(true);\n            debouncedExtractLocation(googleMapsLink, (locationInfo)=>{\n                setExtractingLocation(false);\n                if (locationInfo && locationInfo.formattedLocation) {\n                    setValue(\"location\", locationInfo.formattedLocation);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(`Location extracted: ${locationInfo.formattedLocation}`);\n                } else {\n                    // Check if it's a shortened URL\n                    const isShortenedUrl = googleMapsLink.includes(\"goo.gl\") || googleMapsLink.includes(\"maps.app.goo.gl\");\n                    if (isShortenedUrl) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Unable to extract location from shortened URL. Please wait while we attempt to resolve it, or enter location manually.\");\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Could not extract location from the provided link. Please check the URL format or enter location manually.\");\n                    }\n                }\n            });\n        } else {\n            setExtractingLocation(false);\n        }\n    }, [\n        googleMapsLink,\n        debouncedExtractLocation,\n        setValue,\n        setExtractingLocation\n    ]);\n    // Handle manual location extraction\n    const handleExtractLocation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const link = watch(\"google_maps_link\");\n        if (!link || !link.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Please enter a Google Maps link first\");\n            return;\n        }\n        setExtractingLocation(true);\n        debouncedExtractLocation(link, (locationInfo)=>{\n            setExtractingLocation(false);\n            if (locationInfo && locationInfo.formattedLocation) {\n                setValue(\"location\", locationInfo.formattedLocation);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(`Location extracted: ${locationInfo.formattedLocation}`);\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Could not extract location from the provided link.\");\n            }\n        });\n    }, [\n        watch,\n        debouncedExtractLocation,\n        setValue\n    ]);\n    const onSubmit = async (data)=>{\n        try {\n            // Convert empty strings to null for optional fields\n            const cleanData = {\n                ...data,\n                description: data.description || null,\n                contact_person_id: data.contact_person_id || null,\n                location: data.location || null,\n                google_maps_link: data.google_maps_link || null,\n                total_amount: 0,\n                gst_inclusive: false,\n                amount_received: 0,\n                amount_pending: 0\n            };\n            let result;\n            if (isEditing) {\n                result = await updateProject(project.id, cleanData);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(\"Project updated successfully\");\n            } else {\n                result = await createProject(cleanData);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(\"Project created successfully\");\n                reset();\n            }\n            onSuccess?.(result);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(error.message || \"Failed to save project\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modern-card overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border-b border-gray-200 dark:border-gray-700 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-white\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-foreground\",\n                                    children: isEditing ? \"Edit Project\" : \"Create New Project\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: isEditing ? \"Update project information\" : \"Add a new project to your portfolio\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                isEditing && project?.custom_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground mt-1\",\n                                    children: [\n                                        \"ID: \",\n                                        project.custom_id\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                !isEditing && previewId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground mt-1\",\n                                    children: [\n                                        \"ID (preview): \",\n                                        previewId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"p-6 space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                htmlFor: \"name\",\n                                className: \"text-sm font-medium text-foreground\",\n                                children: \"Project Name *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                id: \"name\",\n                                ...register(\"name\"),\n                                placeholder: \"Enter project name\",\n                                className: \"modern-input\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-600 dark:text-red-400 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-1\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    errors.name.message\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-green-500/5 to-emerald-500/5 rounded-lg p-4 border border-green-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_client_selector__WEBPACK_IMPORTED_MODULE_7__.ClientSelector, {\n                            value: watch(\"client_id\"),\n                            onChange: (clientId)=>{\n                                setValue(\"client_id\", clientId);\n                                setValue(\"contact_person_id\", \"\") // Reset contact person when client changes\n                                ;\n                            },\n                            error: errors.client_id?.message,\n                            disabled: !!preselectedClientId,\n                            label: \"Client\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_contact_person_selector__WEBPACK_IMPORTED_MODULE_8__.ContactPersonSelector, {\n                            clientId: watch(\"client_id\"),\n                            value: watch(\"contact_person_id\"),\n                            onChange: (contactPersonId)=>setValue(\"contact_person_id\", contactPersonId),\n                            error: errors.contact_person_id?.message,\n                            label: \"Primary Contact\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                htmlFor: \"description\",\n                                className: \"text-sm font-medium text-foreground\",\n                                children: \"Description\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"description\",\n                                ...register(\"description\"),\n                                placeholder: \"Enter project description\",\n                                className: \"w-full px-4 py-3 bg-background border border-border rounded-lg focus:ring-2 focus:ring-ring/20 focus:border-primary/50 transition-all duration-200 resize-none min-h-[100px] text-sm placeholder:text-muted-foreground\",\n                                rows: 4\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-600 dark:text-red-400 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-1\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    errors.description.message\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-lg p-4 border border-purple-500/20 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-purple-600 dark:text-purple-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-foreground\",\n                                        children: \"Location Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_location_search__WEBPACK_IMPORTED_MODULE_6__.LocationSearch, {\n                                            value: watch(\"location\") || \"\",\n                                            onChange: (location)=>setValue(\"location\", location),\n                                            onLocationSelect: (locationInfo)=>{\n                                                setValue(\"location\", locationInfo.formattedLocation);\n                                                // Optionally store coordinates for future use\n                                                if (locationInfo.coordinates) {\n                                                    console.log(\"Selected project location coordinates:\", locationInfo.coordinates);\n                                                }\n                                            },\n                                            placeholder: \"Search for project location...\",\n                                            label: \"Location\",\n                                            error: errors.location?.message,\n                                            className: \"modern-input\",\n                                            showCurrentLocationToggle: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"google_maps_link\",\n                                                className: \"text-sm font-medium text-foreground\",\n                                                children: \"Or paste Google Maps link\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"google_maps_link\",\n                                                        ...register(\"google_maps_link\"),\n                                                        placeholder: \"https://maps.google.com/...\",\n                                                        className: \"modern-input flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: handleExtractLocation,\n                                                        disabled: extractingLocation || !watch(\"google_maps_link\"),\n                                                        className: \"px-3\",\n                                                        children: extractingLocation ? \"Extracting...\" : \"Extract\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.google_maps_link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600 dark:text-red-400 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 mr-1\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.google_maps_link.message\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-800\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-purple-700 dark:text-purple-300\",\n                                            children: [\n                                                \"\\uD83D\\uDCA1 \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Tip:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" Use the search above to find locations with autocomplete, or paste a Google Maps link for automatic extraction.\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"For best results with links, use the full URL from your browser's address bar instead of shortened links.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\",\n                        children: [\n                            onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                onClick: onCancel,\n                                disabled: loading,\n                                className: \"px-6 py-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"btn-gradient-blue px-8 py-2 min-w-[140px]\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Saving...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this) : isEditing ? \"Update Project\" : \"Create Project\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/forms/ProjectForm.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/forms/ProjectForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/client-selector.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/client-selector.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSelector: () => (/* binding */ ClientSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/modal */ \"(ssr)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _components_forms_ClientForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/forms/ClientForm */ \"(ssr)/./src/components/forms/ClientForm.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useApi */ \"(ssr)/./src/hooks/useApi.ts\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSelector auto */ \n\n\n\n\n\n\n\nfunction ClientSelector({ value, onChange, error, disabled = false, label = \"Client\", required = false }) {\n    const { data: clients, loading: clientsLoading, refetch } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__.useClients)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectedClient = clients?.find((client)=>client.id === value);\n    const filteredClients = clients?.filter((client)=>client.name.toLowerCase().includes(searchTerm.toLowerCase()) || client.email?.toLowerCase().includes(searchTerm.toLowerCase())) || [];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setIsOpen(false);\n                setSearchTerm(\"\");\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    const handleClientSelect = (client)=>{\n        onChange(client.id);\n        setIsOpen(false);\n        setSearchTerm(\"\");\n    };\n    const handleCreateSuccess = (newClient)=>{\n        setIsCreateModalOpen(false);\n        refetch();\n        onChange(newClient.id);\n        setIsOpen(false);\n        setSearchTerm(\"\");\n    };\n    const handleInputClick = ()=>{\n        if (!disabled) {\n            setIsOpen(true);\n            inputRef.current?.focus();\n        }\n    };\n    const handleInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n        if (!isOpen) setIsOpen(true);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Escape\") {\n            setIsOpen(false);\n            setSearchTerm(\"\");\n        }\n    };\n    if (clientsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                    children: [\n                        label,\n                        \" \",\n                        required && \"*\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-muted-foreground\",\n                            children: \"Loading clients...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                htmlFor: \"client-selector\",\n                children: [\n                    label,\n                    \" \",\n                    required && \"*\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 cursor-pointer ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                        onClick: handleInputClick,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: inputRef,\n                                id: \"client-selector\",\n                                type: \"text\",\n                                value: isOpen ? searchTerm : selectedClient?.name || \"\",\n                                onChange: handleInputChange,\n                                onKeyDown: handleKeyDown,\n                                placeholder: selectedClient ? selectedClient.name : \"Search clients...\",\n                                className: \"flex-1 bg-background outline-none placeholder:text-muted-foreground\",\n                                disabled: disabled\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: `w-4 h-4 text-muted-foreground transition-transform ${isOpen ? \"rotate-180\" : \"\"}`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute z-50 w-full mt-1 border border-border rounded-md shadow-lg max-h-60 overflow-auto bg-popover text-popover-foreground\",\n                        children: filteredClients.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-1\",\n                            children: filteredClients.map((client)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between px-3 py-2 text-sm cursor-pointer hover:bg-muted\",\n                                    onClick: ()=>handleClientSelect(client),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium truncate\",\n                                                    children: client.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, this),\n                                                client.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-muted-foreground truncate\",\n                                                    children: client.email\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 21\n                                        }, this),\n                                        value === client.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 text-primary flex-shrink-0 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, client.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 15\n                        }, this) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm text-muted-foreground\",\n                                    children: [\n                                        'No clients found for \"',\n                                        searchTerm,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>{\n                                            setIsCreateModalOpen(true);\n                                            setIsOpen(false);\n                                        },\n                                        className: \"w-full justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, this),\n                                            'Add new client \"',\n                                            searchTerm,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-sm text-muted-foreground\",\n                                    children: \"Start typing to search clients...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>{\n                                            setIsCreateModalOpen(true);\n                                            setIsOpen(false);\n                                        },\n                                        className: \"w-full justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Add new client\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600 mt-1\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                title: \"Add New Client\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_ClientForm__WEBPACK_IMPORTED_MODULE_5__.ClientForm, {\n                    initialName: searchTerm,\n                    onSuccess: handleCreateSuccess,\n                    onCancel: ()=>setIsCreateModalOpen(false)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/client-selector.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/client-selector.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/contact-person-selector.tsx":
/*!*******************************************************!*\
  !*** ./src/components/ui/contact-person-selector.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactPersonSelector: () => (/* binding */ ContactPersonSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/modal */ \"(ssr)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _components_forms_ContactPersonForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/forms/ContactPersonForm */ \"(ssr)/./src/components/forms/ContactPersonForm.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Check,ChevronDown,Mail,Phone,Plus,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Check,ChevronDown,Mail,Phone,Plus,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Check,ChevronDown,Mail,Phone,Plus,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Check,ChevronDown,Mail,Phone,Plus,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Check,ChevronDown,Mail,Phone,Plus,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Check,ChevronDown,Mail,Phone,Plus,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Check,ChevronDown,Mail,Phone,Plus,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Check,ChevronDown,Mail,Phone,Plus,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ContactPersonSelector auto */ \n\n\n\n\n\n\n\n\nfunction ContactPersonSelector({ clientId, value, onChange, error, disabled = false, label = \"Primary Contact\", required = false }) {\n    const [contactPersons, setContactPersons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectedContact = contactPersons.find((contact)=>contact.id === value);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (clientId) {\n            fetchContactPersons();\n        }\n    }, [\n        clientId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setIsOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    const fetchContactPersons = async ()=>{\n        try {\n            setLoading(true);\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.contactPersonsApi.getByClientId(clientId);\n            setContactPersons(data);\n            // Auto-select primary contact if no contact is selected\n            if (!value && data.length > 0) {\n                const primaryContact = data.find((contact)=>contact.is_primary) || data[0];\n                onChange(primaryContact.id);\n            }\n        } catch (error) {\n            console.error(\"Error fetching contact persons:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Failed to load contact persons\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleContactSelect = (contact)=>{\n        onChange(contact.id);\n        setIsOpen(false);\n    };\n    const handleCreateSuccess = (newContact)=>{\n        setIsCreateModalOpen(false);\n        fetchContactPersons();\n        onChange(newContact.id);\n        setIsOpen(false);\n    };\n    const handleInputClick = ()=>{\n        if (!disabled && contactPersons.length > 0) {\n            setIsOpen(true);\n        }\n    };\n    if (!clientId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                    className: \"text-muted-foreground\",\n                    children: [\n                        label,\n                        \" \",\n                        required && \"*\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground\",\n                    children: \"Select a client first\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                    children: [\n                        label,\n                        \" \",\n                        required && \"*\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-muted-foreground\",\n                            children: \"Loading contacts...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                htmlFor: \"contact-selector\",\n                children: [\n                    label,\n                    \" \",\n                    required && \"*\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mt-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 cursor-pointer ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                        onClick: handleInputClick,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center min-w-0\",\n                                children: selectedContact ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 text-muted-foreground flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-0 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium truncate\",\n                                                            children: selectedContact.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        selectedContact.is_primary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-3 h-3 text-yellow-500 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                selectedContact.designation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-muted-foreground truncate\",\n                                                    children: selectedContact.designation\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Select primary contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: `w-4 h-4 text-muted-foreground transition-transform flex-shrink-0 ${isOpen ? \"rotate-180\" : \"\"}`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    isOpen && contactPersons.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute z-50 w-full mt-1 border border-border rounded-md shadow-lg max-h-60 overflow-auto bg-popover text-popover-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-1\",\n                                children: contactPersons.map((contact)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between px-3 py-2 text-sm cursor-pointer hover:bg-muted\",\n                                        onClick: ()=>handleContactSelect(contact),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium truncate\",\n                                                                children: contact.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            contact.is_primary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-3 h-3 text-yellow-500 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            contact.designation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: contact.designation\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            contact.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                        lineNumber: 177,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: contact.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            contact.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: contact.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                        lineNumber: 184,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this),\n                                            value === contact.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4 text-primary flex-shrink-0 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, contact.id, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-border px-3 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        setIsCreateModalOpen(true);\n                                        setIsOpen(false);\n                                    },\n                                    className: \"w-full justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add new contact person\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    isOpen && contactPersons.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute z-50 w-full mt-1 border border-border rounded-md shadow-lg bg-popover text-popover-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-3 py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground mb-2\",\n                                    children: \"No contact persons found\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        setIsCreateModalOpen(true);\n                                        setIsOpen(false);\n                                    },\n                                    className: \"w-full justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Check_ChevronDown_Mail_Phone_Plus_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add first contact person\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600 mt-1\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                title: \"Add Contact Person\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_ContactPersonForm__WEBPACK_IMPORTED_MODULE_5__.ContactPersonForm, {\n                    clientId: clientId,\n                    onSuccess: handleCreateSuccess,\n                    onCancel: ()=>setIsCreateModalOpen(false)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/contact-person-selector.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/contact-person-selector.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/label.tsx\",\n        lineNumber: 15,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ3lCO0FBQ1U7QUFDakM7QUFFaEMsTUFBTUksZ0JBQWdCRiw2REFBR0EsQ0FDdkI7QUFHRixNQUFNRyxzQkFBUUwsNkNBQWdCLENBSTVCLENBQUMsRUFBRU8sU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDUix1REFBbUI7UUFDbEJRLEtBQUtBO1FBQ0xGLFdBQVdKLDhDQUFFQSxDQUFDQyxpQkFBaUJHO1FBQzlCLEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxNQUFNTSxXQUFXLEdBQUdWLHVEQUFtQixDQUFDVSxXQUFXO0FBRW5DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3ltYXRpY3MtZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3g/MTNlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXG4gIFwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwIHRleHQtZm9yZWdyb3VuZFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/location-search.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/location-search.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocationSearch: () => (/* binding */ LocationSearch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_maps_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/maps-utils */ \"(ssr)/./src/lib/maps-utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MapPin,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MapPin,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MapPin,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,MapPin,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ LocationSearch auto */ \n\n\n\n\n\n\n\nfunction LocationSearch({ value, onChange, onLocationSelect, placeholder = \"Search for a location...\", label, disabled = false, error, className, showCurrentLocationToggle = true }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [searchError, setSearchError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useCurrentLocation, setUseCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gettingLocation, setGettingLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const debouncedSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((0,_lib_maps_utils__WEBPACK_IMPORTED_MODULE_5__.createDebouncedPlaceSearch)(300));\n    // Handle search\n    const handleSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        if (!query || query.trim().length < 2 || useCurrentLocation) {\n            setSearchResults([]);\n            setIsOpen(false);\n            setSearchError(null);\n            return;\n        }\n        setIsSearching(true);\n        setSearchError(null);\n        debouncedSearch.current(query, (results, error)=>{\n            setSearchResults(results);\n            setIsSearching(false);\n            setIsOpen(true) // Always open to show results or error\n            ;\n            setSelectedIndex(-1);\n            if (error) {\n                setSearchError(error);\n            } else if (results.length === 0) {\n                setSearchError(\"No locations found. Try a different search term.\");\n            }\n        });\n    }, [\n        useCurrentLocation\n    ]);\n    // Handle input change\n    const handleInputChange = (e)=>{\n        const newValue = e.target.value;\n        onChange(newValue);\n        handleSearch(newValue);\n    };\n    // Handle place selection\n    const handlePlaceSelect = async (place)=>{\n        setIsSearching(true);\n        try {\n            const locationInfo = await (0,_lib_maps_utils__WEBPACK_IMPORTED_MODULE_5__.getPlaceDetails)(place.place_id);\n            if (locationInfo) {\n                onChange(locationInfo.formattedLocation);\n                onLocationSelect?.({\n                    formattedLocation: locationInfo.formattedLocation,\n                    coordinates: locationInfo.coordinates\n                });\n            } else {\n                onChange(place.description);\n                onLocationSelect?.({\n                    formattedLocation: place.description\n                });\n            }\n        } catch (error) {\n            console.error(\"Error getting place details:\", error);\n            onChange(place.description);\n            onLocationSelect?.({\n                formattedLocation: place.description\n            });\n        } finally{\n            setIsSearching(false);\n            setIsOpen(false);\n            setSearchResults([]);\n        }\n    };\n    // Handle keyboard navigation\n    const handleKeyDown = (e)=>{\n        if (!isOpen || searchResults.length === 0) {\n            if (e.key === \"ArrowDown\" && value.trim().length >= 2) {\n                handleSearch(value);\n            }\n            return;\n        }\n        switch(e.key){\n            case \"ArrowDown\":\n                e.preventDefault();\n                setSelectedIndex((prev)=>prev < searchResults.length - 1 ? prev + 1 : 0);\n                break;\n            case \"ArrowUp\":\n                e.preventDefault();\n                setSelectedIndex((prev)=>prev > 0 ? prev - 1 : searchResults.length - 1);\n                break;\n            case \"Enter\":\n                e.preventDefault();\n                if (selectedIndex >= 0 && selectedIndex < searchResults.length) {\n                    handlePlaceSelect(searchResults[selectedIndex]);\n                }\n                break;\n            case \"Escape\":\n                setIsOpen(false);\n                setSelectedIndex(-1);\n                inputRef.current?.blur();\n                break;\n        }\n    };\n    // Handle click outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setIsOpen(false);\n                setSelectedIndex(-1);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    // Get current location\n    const getCurrentLocation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        console.log(\"getCurrentLocation called\");\n        if (!navigator.geolocation) {\n            console.error(\"Geolocation not supported\");\n            setSearchError(\"Geolocation is not supported by this browser\");\n            return;\n        }\n        // Check if we're on HTTPS or localhost\n        const isSecure = window.location.protocol === \"https:\" || window.location.hostname === \"localhost\";\n        if (!isSecure) {\n            console.error(\"Geolocation requires HTTPS\");\n            setSearchError(\"Location access requires a secure connection (HTTPS)\");\n            return;\n        }\n        setGettingLocation(true);\n        setSearchError(null);\n        try {\n            console.log(\"Requesting geolocation...\");\n            const position = await new Promise((resolve, reject)=>{\n                navigator.geolocation.getCurrentPosition((pos)=>{\n                    console.log(\"Geolocation success:\", pos.coords);\n                    resolve(pos);\n                }, (err)=>{\n                    console.error(\"Geolocation error:\", err);\n                    reject(err);\n                }, {\n                    enableHighAccuracy: true,\n                    timeout: 15000,\n                    maximumAge: 300000 // 5 minutes\n                });\n            });\n            const { latitude, longitude } = position.coords;\n            console.log(\"Got coordinates:\", {\n                latitude,\n                longitude\n            });\n            // Reverse geocode to get address\n            console.log(\"Calling reverse geocode API...\");\n            const response = await fetch(`/api/places/reverse-geocode?lat=${latitude}&lng=${longitude}`);\n            console.log(\"Reverse geocode response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"Reverse geocode API error:\", response.status, errorText);\n                throw new Error(`Failed to get address for current location: ${response.status}`);\n            }\n            const data = await response.json();\n            console.log(\"Reverse geocode data:\", data);\n            if (data.formattedLocation) {\n                console.log(\"Setting location:\", data.formattedLocation);\n                onChange(data.formattedLocation);\n                onLocationSelect?.({\n                    formattedLocation: data.formattedLocation,\n                    coordinates: {\n                        lat: latitude,\n                        lng: longitude\n                    }\n                });\n            } else {\n                throw new Error(\"Could not determine address for current location\");\n            }\n        } catch (error) {\n            console.error(\"Error getting current location:\", error);\n            // Handle geolocation errors\n            if (error.code === 1) {\n                setSearchError(\"Location access denied. Please enable location permissions in your browser.\");\n            } else if (error.code === 2) {\n                setSearchError(\"Location unavailable. Please check your GPS/network connection.\");\n            } else if (error.code === 3) {\n                setSearchError(\"Location request timed out. Please try again.\");\n            } else if (error.message?.includes(\"Failed to get address\")) {\n                setSearchError(\"Got your location but could not determine the address. Please try searching manually.\");\n            } else {\n                setSearchError(`Failed to get current location: ${error.message || \"Unknown error\"}`);\n            }\n            setUseCurrentLocation(false);\n        } finally{\n            setGettingLocation(false);\n        }\n    }, [\n        onChange,\n        onLocationSelect\n    ]);\n    // Handle current location toggle\n    const handleCurrentLocationToggle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((checked)=>{\n        setUseCurrentLocation(checked);\n        if (checked) {\n            getCurrentLocation();\n        } else {\n            onChange(\"\");\n            setSearchError(null);\n        }\n    }, [\n        getCurrentLocation,\n        onChange\n    ]);\n    // Clear search\n    const handleClear = ()=>{\n        onChange(\"\");\n        setSearchResults([]);\n        setIsOpen(false);\n        setUseCurrentLocation(false);\n        setSearchError(null);\n        inputRef.current?.focus();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative z-10\", className),\n        ref: dropdownRef,\n        children: [\n            (label || showCurrentLocationToggle) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [\n                    label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                        htmlFor: \"location-search\",\n                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 13\n                    }, this),\n                    showCurrentLocationToggle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                id: \"useCurrentLocation\",\n                                checked: useCurrentLocation,\n                                onChange: (e)=>handleCurrentLocationToggle(e.target.checked),\n                                disabled: disabled || gettingLocation,\n                                className: \"w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                htmlFor: \"useCurrentLocation\",\n                                className: \"text-sm text-gray-600 dark:text-gray-400 flex items-center gap-1 cursor-pointer\",\n                                children: gettingLocation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-3 h-3 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Getting location...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Use current location\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                        children: isSearching ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-4 h-4 text-muted-foreground animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                        ref: inputRef,\n                        id: \"location-search\",\n                        type: \"text\",\n                        value: value,\n                        onChange: handleInputChange,\n                        onKeyDown: handleKeyDown,\n                        placeholder: useCurrentLocation ? \"Using current location...\" : placeholder,\n                        disabled: disabled || useCurrentLocation || gettingLocation,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"pl-10 pr-10\", error && \"border-red-500 focus:border-red-500 focus:ring-red-500\", (useCurrentLocation || gettingLocation) && \"bg-muted\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    value && !disabled && !gettingLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"button\",\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: handleClear,\n                        className: \"absolute inset-y-0 right-0 px-3 hover:bg-muted\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-4 h-4 text-muted-foreground hover:text-foreground\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600 mt-1 flex items-center gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, this),\n            isOpen && searchResults.length > 0 && !useCurrentLocation && !gettingLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-[9999] w-full mt-1 bg-popover text-popover-foreground border border-border rounded-lg shadow-xl max-h-60 overflow-y-auto\",\n                children: searchResults.map((place, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>handlePlaceSelect(place),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full px-4 py-3 text-left hover:bg-muted focus:bg-muted focus:outline-none border-b border-border last:border-b-0\", selectedIndex === index && \"bg-muted\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_MapPin_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-foreground truncate\",\n                                            children: place.structured_formatting.main_text\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground truncate\",\n                                            children: place.structured_formatting.secondary_text\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 15\n                        }, this)\n                    }, place.place_id, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this),\n            isOpen && !isSearching && searchResults.length === 0 && value.trim().length >= 2 && !useCurrentLocation && !gettingLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute z-[9999] w-full mt-1 border rounded-lg shadow-xl p-4 ${searchError ? \"bg-destructive/10 border-destructive\" : \"bg-popover text-popover-foreground border-border\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-sm text-center ${searchError ? \"text-destructive\" : \"text-muted-foreground\"}`,\n                        children: searchError || `No locations found for \"${value}\"`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-center mt-1 text-muted-foreground\",\n                        children: searchError ? \"You can still enter the location manually\" : \"Try a different search term or enter the location manually\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n                lineNumber: 383,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/location-search.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/location-search.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLIENT_TYPES: () => (/* binding */ CLIENT_TYPES),\n/* harmony export */   EXPENSE_CATEGORIES: () => (/* binding */ EXPENSE_CATEGORIES),\n/* harmony export */   OUTSOURCING_CATEGORIES: () => (/* binding */ OUTSOURCING_CATEGORIES)\n/* harmony export */ });\n// Expense Categories\nconst EXPENSE_CATEGORIES = [\n    {\n        value: \"cymatics\",\n        label: \"Cymatics\"\n    },\n    {\n        value: \"salary\",\n        label: \"Salary\"\n    },\n    {\n        value: \"gadgets\",\n        label: \"Gadgets\"\n    },\n    {\n        value: \"outsourcing\",\n        label: \"Outsourcing\"\n    },\n    {\n        value: \"asset\",\n        label: \"Asset\"\n    },\n    {\n        value: \"loan_repayment\",\n        label: \"Loan Repayment\"\n    },\n    {\n        value: \"investments\",\n        label: \"Investments\"\n    },\n    {\n        value: \"fuel_travel\",\n        label: \"Fuel & Travel\"\n    },\n    {\n        value: \"food_snacks\",\n        label: \"Food & Snacks\"\n    },\n    {\n        value: \"others\",\n        label: \"Others\"\n    },\n    {\n        value: \"entertainment\",\n        label: \"Entertainment\"\n    },\n    {\n        value: \"gopi\",\n        label: \"Gopi\"\n    },\n    {\n        value: \"yaso\",\n        label: \"Yaso\"\n    },\n    {\n        value: \"adithyan\",\n        label: \"Adithyan\"\n    }\n];\n// Outsourcing Categories\nconst OUTSOURCING_CATEGORIES = [\n    \"Photo\",\n    \"Video\",\n    \"Editor\",\n    \"Drone\",\n    \"Pilot\",\n    \"Post-processing\",\n    \"Softwares\"\n];\n// Client Types\nconst CLIENT_TYPES = [\n    \"Wedding\",\n    \"Corporate\",\n    \"Movie\",\n    \"Govt\",\n    \"NGO\",\n    \"Survey\",\n    \"Surveillance\",\n    \"Real estate\",\n    \"News\",\n    \"Event\",\n    \"Collaboration\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/maps-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/maps-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDebouncedLocationExtractor: () => (/* binding */ createDebouncedLocationExtractor),\n/* harmony export */   createDebouncedPlaceSearch: () => (/* binding */ createDebouncedPlaceSearch),\n/* harmony export */   extractLocationFromMapsUrl: () => (/* binding */ extractLocationFromMapsUrl),\n/* harmony export */   geocodeLocation: () => (/* binding */ geocodeLocation),\n/* harmony export */   getPlaceDetails: () => (/* binding */ getPlaceDetails),\n/* harmony export */   searchPlaces: () => (/* binding */ searchPlaces)\n/* harmony export */ });\n/**\n * Utility functions for extracting location information from Google Maps links\n */ /**\n * Extract location information from Google Maps URL\n */ async function extractLocationFromMapsUrl(url) {\n    try {\n        // Validate if it's a Google Maps URL\n        if (!isValidGoogleMapsUrl(url)) {\n            throw new Error(\"Invalid Google Maps URL\");\n        }\n        let finalUrl = url;\n        // If it's a shortened URL, try to resolve it first\n        if (isShortenedGoogleMapsUrl(url)) {\n            const resolvedUrl = await resolveShortenedUrl(url);\n            if (resolvedUrl && resolvedUrl !== url) {\n                finalUrl = resolvedUrl;\n            } else {\n                // If resolution failed, try to use Google Maps search as fallback\n                return await searchLocationFallback(url);\n            }\n        }\n        // First try to extract location from URL path (no API required)\n        const pathLocation = extractLocationFromUrlPath(finalUrl);\n        if (pathLocation) {\n            return pathLocation;\n        }\n        // If API key is available, try geocoding\n        if (true) {\n            const coordinates = extractCoordinatesFromUrl(finalUrl);\n            const placeId = extractPlaceIdFromUrl(finalUrl);\n            if (coordinates) {\n                return await getLocationFromCoordinates(coordinates.lat, coordinates.lng);\n            } else if (placeId) {\n                return await getLocationFromPlaceId(placeId);\n            }\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error extracting location from Maps URL:\", error);\n        return null;\n    }\n}\n/**\n * Check if URL is a valid Google Maps URL\n */ function isValidGoogleMapsUrl(url) {\n    const googleMapsPatterns = [\n        /^https?:\\/\\/(www\\.)?google\\.(com|co\\.in|[a-z]{2})\\/maps/,\n        /^https?:\\/\\/maps\\.google\\.(com|co\\.in|[a-z]{2})/,\n        /^https?:\\/\\/goo\\.gl\\/maps/,\n        /^https?:\\/\\/maps\\.app\\.goo\\.gl/\n    ];\n    return googleMapsPatterns.some((pattern)=>pattern.test(url));\n}\n/**\n * Check if URL is a shortened Google Maps URL\n */ function isShortenedGoogleMapsUrl(url) {\n    const shortenedPatterns = [\n        /^https?:\\/\\/goo\\.gl\\/maps/,\n        /^https?:\\/\\/maps\\.app\\.goo\\.gl/,\n        /^https?:\\/\\/g\\.page\\//\n    ];\n    return shortenedPatterns.some((pattern)=>pattern.test(url));\n}\n/**\n * Resolve shortened URL to get the full Google Maps URL\n */ async function resolveShortenedUrl(url) {\n    try {\n        // Use our API endpoint to resolve the URL server-side\n        const response = await fetch(\"/api/resolve-maps-url\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                url\n            })\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return data.resolvedUrl || null;\n        } else {\n            const errorData = await response.json();\n            console.error(\"Error resolving URL:\", errorData.error);\n            return null;\n        }\n    } catch (error) {\n        console.error(\"Error resolving shortened URL:\", error);\n        return null;\n    }\n}\n/**\n * Extract coordinates from Google Maps URL\n */ function extractCoordinatesFromUrl(url) {\n    // Pattern for coordinates in URL: @lat,lng,zoom\n    const coordPattern = /@(-?\\d+\\.?\\d*),(-?\\d+\\.?\\d*),/;\n    const match = url.match(coordPattern);\n    if (match) {\n        return {\n            lat: parseFloat(match[1]),\n            lng: parseFloat(match[2])\n        };\n    }\n    // Alternative pattern: !3d<lat>!4d<lng>\n    const altPattern = /!3d(-?\\d+\\.?\\d*)!4d(-?\\d+\\.?\\d*)/;\n    const altMatch = url.match(altPattern);\n    if (altMatch) {\n        return {\n            lat: parseFloat(altMatch[1]),\n            lng: parseFloat(altMatch[2])\n        };\n    }\n    return null;\n}\n/**\n * Extract place ID from Google Maps URL\n */ function extractPlaceIdFromUrl(url) {\n    const placeIdPattern = /place_id:([a-zA-Z0-9_-]+)/;\n    const match = url.match(placeIdPattern);\n    return match ? match[1] : null;\n}\n/**\n * Get location information from coordinates using reverse geocoding\n */ async function getLocationFromCoordinates(lat, lng) {\n    const apiKey = \"AIzaSyDmpk_NV3EKROQpR6aVJkid9rWuxF4O7G4\";\n    if (!apiKey) {\n        console.warn(\"Google Maps API key not found\");\n        return null;\n    }\n    try {\n        // First try with result_type filter to get more specific results\n        const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}&result_type=establishment|point_of_interest|premise`);\n        if (!response.ok) {\n            throw new Error(`Geocoding API error: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.status === \"OK\" && data.results.length > 0) {\n            // Try to find the most specific result (establishment, POI, or premise)\n            const specificResult = data.results.find((result)=>result.types.includes(\"establishment\") || result.types.includes(\"point_of_interest\") || result.types.includes(\"premise\")) || data.results[0];\n            return parseGeocodingResult(specificResult);\n        } else if (data.status === \"ZERO_RESULTS\") {\n            // Try again without result_type filter for broader results\n            const fallbackResponse = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`);\n            if (fallbackResponse.ok) {\n                const fallbackData = await fallbackResponse.json();\n                if (fallbackData.status === \"OK\" && fallbackData.results.length > 0) {\n                    return parseGeocodingResult(fallbackData.results[0]);\n                }\n            }\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error in reverse geocoding:\", error);\n        return null;\n    }\n}\n/**\n * Get location information from place ID\n */ async function getLocationFromPlaceId(placeId) {\n    const apiKey = \"AIzaSyDmpk_NV3EKROQpR6aVJkid9rWuxF4O7G4\";\n    if (!apiKey) {\n        console.warn(\"Google Maps API key not found\");\n        return null;\n    }\n    try {\n        const response = await fetch(`https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=name,formatted_address,address_components,types&key=${apiKey}`);\n        if (!response.ok) {\n            throw new Error(`Places API error: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.status === \"OK\" && data.result) {\n            return parsePlaceResult(data.result);\n        } else {\n            console.warn(`Places API returned status: ${data.status}`);\n            return null;\n        }\n    } catch (error) {\n        console.error(\"Error fetching place details:\", error);\n        return null;\n    }\n}\n/**\n * Parse geocoding API result to extract place name and city\n */ function parseGeocodingResult(result) {\n    const addressComponents = result.address_components || [];\n    let placeName = \"\";\n    let city = \"\";\n    // Extract place name (establishment, point_of_interest, or premise)\n    const establishment = addressComponents.find((comp)=>comp.types.includes(\"establishment\") || comp.types.includes(\"point_of_interest\") || comp.types.includes(\"premise\"));\n    if (establishment) {\n        placeName = establishment.long_name;\n    }\n    // Extract city (locality or administrative_area_level_2)\n    const locality = addressComponents.find((comp)=>comp.types.includes(\"locality\") || comp.types.includes(\"administrative_area_level_2\"));\n    if (locality) {\n        city = locality.long_name;\n    }\n    // If no specific place name found, use the first part of formatted address\n    if (!placeName && result.formatted_address) {\n        const addressParts = result.formatted_address.split(\",\");\n        placeName = addressParts[0].trim();\n    }\n    const formattedLocation = placeName && city ? `${placeName}, ${city}` : placeName || city || result.formatted_address;\n    return {\n        placeName,\n        city,\n        formattedLocation\n    };\n}\n/**\n * Parse Places API result to extract place name and city\n */ function parsePlaceResult(result) {\n    const placeName = result.name || \"\";\n    const addressComponents = result.address_components || [];\n    let city = \"\";\n    // Extract city from address components\n    const locality = addressComponents.find((comp)=>comp.types.includes(\"locality\") || comp.types.includes(\"administrative_area_level_2\"));\n    if (locality) {\n        city = locality.long_name;\n    }\n    const formattedLocation = placeName && city ? `${placeName}, ${city}` : placeName || city;\n    return {\n        placeName,\n        city,\n        formattedLocation\n    };\n}\n/**\n * Extract location from URL path as fallback\n */ function extractLocationFromUrlPath(url) {\n    try {\n        const urlObj = new URL(url);\n        const pathParts = urlObj.pathname.split(\"/\");\n        const searchParams = urlObj.searchParams;\n        // Special handling for shortened URLs\n        if (isShortenedGoogleMapsUrl(url)) {\n            return handleShortenedUrl(url);\n        }\n        // Method 1: Look for location in path like /maps/place/Location+Name/\n        const placeIndex = pathParts.findIndex((part)=>part === \"place\");\n        if (placeIndex !== -1 && pathParts[placeIndex + 1]) {\n            const locationPart = decodeURIComponent(pathParts[placeIndex + 1]);\n            const cleanLocation = locationPart.replace(/\\+/g, \" \");\n            return parseLocationString(cleanLocation);\n        }\n        // Method 2: Look for 'q' parameter in search\n        const qParam = searchParams.get(\"q\");\n        if (qParam) {\n            const cleanLocation = decodeURIComponent(qParam).replace(/\\+/g, \" \");\n            return parseLocationString(cleanLocation);\n        }\n        // Method 3: Look for 'query' parameter\n        const queryParam = searchParams.get(\"query\");\n        if (queryParam) {\n            const cleanLocation = decodeURIComponent(queryParam).replace(/\\+/g, \" \");\n            return parseLocationString(cleanLocation);\n        }\n        // Method 4: Extract from data parameter (for some Google Maps URLs)\n        const dataParam = searchParams.get(\"data\");\n        if (dataParam) {\n            // Try to extract location from data parameter\n            const locationMatch = dataParam.match(/!1s([^!]+)/);\n            if (locationMatch) {\n                const cleanLocation = decodeURIComponent(locationMatch[1]).replace(/\\+/g, \" \");\n                return parseLocationString(cleanLocation);\n            }\n        }\n        // Method 5: Look in URL fragment for location\n        if (urlObj.hash) {\n            const hashParts = urlObj.hash.split(\"/\");\n            for (const part of hashParts){\n                if (part.includes(\",\") && part.length > 5) {\n                    // Might be a location string\n                    const cleanLocation = decodeURIComponent(part).replace(/\\+/g, \" \");\n                    if (!cleanLocation.match(/^[\\d\\.,\\-]+$/)) {\n                        return parseLocationString(cleanLocation);\n                    }\n                }\n            }\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error extracting location from URL path:\", error);\n        return null;\n    }\n}\n/**\n * Handle shortened URLs by trying to resolve them or providing guidance\n */ function handleShortenedUrl(url) {\n    // This function is called when we detect a shortened URL\n    // The main extraction function will try to resolve it first\n    // If we reach here, resolution failed, so return null\n    return null;\n}\n/**\n * Fallback search when URL resolution fails\n */ async function searchLocationFallback(url) {\n    const apiKey = \"AIzaSyDmpk_NV3EKROQpR6aVJkid9rWuxF4O7G4\";\n    if (!apiKey) {\n        return null;\n    }\n    try {\n        // For shortened URLs, we can try to extract any text that might be a location\n        // This is a last resort when URL resolution fails\n        // Extract the short code from the URL (e.g., \"v6dC7M7DmDXF2PoN8\" from \"https://maps.app.goo.gl/v6dC7M7DmDXF2PoN8\")\n        const shortCode = url.split(\"/\").pop();\n        if (shortCode && shortCode.length > 5) {\n            // We can't really search by short code, so return null\n            // The user will need to enter location manually\n            return null;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error in search fallback:\", error);\n        return null;\n    }\n}\n/**\n * Parse a location string into place name and city\n */ function parseLocationString(locationString) {\n    // Clean up the location string\n    let cleanLocation = locationString.replace(/\\+/g, \" \").replace(/@.*$/, \"\") // Remove coordinates at the end\n    .trim();\n    // Try to split into place name and city\n    const parts = cleanLocation.split(\",\").map((part)=>part.trim()).filter((part)=>part.length > 0);\n    if (parts.length >= 2) {\n        // If we have multiple parts, assume first is place name, second is city\n        const placeName = parts[0];\n        const city = parts[1];\n        return {\n            placeName,\n            city,\n            formattedLocation: `${placeName}, ${city}`\n        };\n    } else if (parts.length === 1) {\n        // Single part - could be just place name or just city\n        const singlePart = parts[0];\n        // If it looks like a business name (contains common business words), treat as place name\n        const businessKeywords = [\n            \"restaurant\",\n            \"hotel\",\n            \"mall\",\n            \"center\",\n            \"centre\",\n            \"store\",\n            \"shop\",\n            \"cafe\",\n            \"bar\",\n            \"hospital\",\n            \"school\",\n            \"university\",\n            \"park\",\n            \"temple\",\n            \"church\",\n            \"mosque\",\n            \"station\",\n            \"airport\"\n        ];\n        const isLikelyBusiness = businessKeywords.some((keyword)=>singlePart.toLowerCase().includes(keyword));\n        if (isLikelyBusiness) {\n            return {\n                placeName: singlePart,\n                city: \"\",\n                formattedLocation: singlePart\n            };\n        } else {\n            // Treat as city\n            return {\n                placeName: \"\",\n                city: singlePart,\n                formattedLocation: singlePart\n            };\n        }\n    }\n    return {\n        placeName: \"\",\n        city: \"\",\n        formattedLocation: cleanLocation\n    };\n}\n/**\n * Search for places using our API route\n */ async function searchPlaces(query) {\n    if (!query || query.trim().length < 2) {\n        return [];\n    }\n    try {\n        console.log(\"Searching places for query:\", query);\n        const response = await fetch(`/api/places/search?query=${encodeURIComponent(query)}`);\n        console.log(\"Places search response status:\", response.status);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    error: \"Unknown error\"\n                }));\n            console.error(\"Places API error:\", response.status, errorData);\n            throw new Error(`Places API error: ${response.status} - ${errorData.error || \"Unknown error\"}`);\n        }\n        const data = await response.json();\n        console.log(\"Places search results:\", data.predictions?.length || 0, \"predictions\");\n        if (data.predictions) {\n            return data.predictions;\n        }\n        return [];\n    } catch (error) {\n        console.error(\"Error searching places:\", error);\n        // Re-throw the error so the component can handle it\n        throw error;\n    }\n}\n/**\n * Get place details by place ID using our API route\n */ async function getPlaceDetails(placeId) {\n    try {\n        const response = await fetch(`/api/places/details?place_id=${encodeURIComponent(placeId)}`);\n        if (!response.ok) {\n            throw new Error(`Places API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            placeName: data.placeName,\n            city: data.city,\n            formattedLocation: data.formattedLocation,\n            coordinates: data.coordinates\n        };\n    } catch (error) {\n        console.error(\"Error fetching place details:\", error);\n        return null;\n    }\n}\n/**\n * Debounced function to extract location (to avoid too many API calls)\n */ function createDebouncedLocationExtractor(delay = 1000) {\n    let timeoutId = null;\n    return (url, callback)=>{\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(async ()=>{\n            const location = await extractLocationFromMapsUrl(url);\n            callback(location);\n        }, delay);\n    };\n}\n/**\n * Debounced function to search places (to avoid too many API calls)\n */ function createDebouncedPlaceSearch(delay = 300) {\n    let timeoutId = null;\n    return (query, callback)=>{\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(async ()=>{\n            try {\n                const results = await searchPlaces(query);\n                callback(results);\n            } catch (error) {\n                console.error(\"Debounced search error:\", error);\n                callback([], error instanceof Error ? error.message : \"Search failed\");\n            }\n        }, delay);\n    };\n}\n/**\n * Geocode a location string to get coordinates\n */ async function geocodeLocation(locationString) {\n    try {\n        console.log(`🌍 Geocoding location: \"${locationString}\"`);\n        // Use the existing searchPlaces function to find the location\n        const places = await searchPlaces(locationString);\n        if (places.length === 0) {\n            console.log(`❌ No places found for: \"${locationString}\"`);\n            return null;\n        }\n        // Get details for the first (most relevant) place\n        const firstPlace = places[0];\n        console.log(`📍 Found place: ${firstPlace.description}`);\n        const placeDetails = await getPlaceDetails(firstPlace.place_id);\n        if (placeDetails?.coordinates) {\n            console.log(`✅ Coordinates found: ${placeDetails.coordinates.lat}, ${placeDetails.coordinates.lng}`);\n            return placeDetails.coordinates;\n        }\n        console.log(`❌ No coordinates found for place: ${firstPlace.description}`);\n        return null;\n    } catch (error) {\n        console.error(`❌ Error geocoding location \"${locationString}\":`, error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/maps-utils.ts\n");

/***/ })

};
;