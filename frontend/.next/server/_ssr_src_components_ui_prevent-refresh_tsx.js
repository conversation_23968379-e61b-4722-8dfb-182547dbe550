"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_ui_prevent-refresh_tsx";
exports.ids = ["_ssr_src_components_ui_prevent-refresh_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/ui/prevent-refresh.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/prevent-refresh.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreventRefresh: () => (/* binding */ PreventRefresh),\n/* harmony export */   useUnsavedChanges: () => (/* binding */ useUnsavedChanges)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Wifi,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Wifi,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Wifi,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _components_ui_realtime_indicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/realtime-indicator */ \"(ssr)/./src/components/ui/realtime-indicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ PreventRefresh,useUnsavedChanges auto */ \n\n\n\n\nfunction PreventRefresh() {\n    const [showWarning, setShowWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isConnected, isOnline } = (0,_components_ui_realtime_indicator__WEBPACK_IMPORTED_MODULE_3__.useRealtimeStatus)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Prevent accidental refresh when there are unsaved changes\n        const handleBeforeUnload = (e)=>{\n            if (hasUnsavedChanges) {\n                e.preventDefault();\n                e.returnValue = \"You have unsaved changes. Are you sure you want to leave?\";\n                return e.returnValue;\n            }\n        };\n        // Show warning when user tries to refresh\n        const handleKeyDown = (e)=>{\n            // Detect Ctrl+R, F5, or Cmd+R\n            if (e.ctrlKey && e.key === \"r\" || e.metaKey && e.key === \"r\" || e.key === \"F5\") {\n                e.preventDefault();\n                setShowWarning(true);\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>{\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n            window.removeEventListener(\"keydown\", handleKeyDown);\n        };\n    }, [\n        hasUnsavedChanges\n    ]);\n    // Auto-hide warning after 5 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (showWarning) {\n            const timer = setTimeout(()=>{\n                setShowWarning(false);\n            }, 5000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        showWarning\n    ]);\n    // Listen for form changes to set unsaved changes flag\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleFormChange = ()=>{\n            setHasUnsavedChanges(true);\n        };\n        const handleFormSubmit = ()=>{\n            setHasUnsavedChanges(false);\n        };\n        // Listen for form inputs\n        document.addEventListener(\"input\", handleFormChange);\n        document.addEventListener(\"submit\", handleFormSubmit);\n        return ()=>{\n            document.removeEventListener(\"input\", handleFormChange);\n            document.removeEventListener(\"submit\", handleFormSubmit);\n        };\n    }, []);\n    const handleForceRefresh = ()=>{\n        window.location.reload();\n    };\n    const handleDismiss = ()=>{\n        setShowWarning(false);\n    };\n    if (!showWarning) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-sm\",\n                                    children: \"Refresh Not Needed\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleDismiss,\n                            className: \"h-6 w-6 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"This app updates automatically in real-time. No need to refresh!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: `w-3 h-3 ${isConnected && isOnline ? \"text-green-500\" : \"text-red-500\"}`\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: isConnected && isOnline ? \"text-green-600\" : \"text-red-600\",\n                                    children: isConnected && isOnline ? \"Real-time updates active\" : \"Connection issues detected\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleDismiss,\n                                    className: \"flex-1 text-xs\",\n                                    children: \"Got it\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                (!isConnected || !isOnline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleForceRefresh,\n                                    className: \"flex-1 text-xs text-orange-600 hover:text-orange-700\",\n                                    children: \"Force Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/prevent-refresh.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n// Hook to manage unsaved changes state\nfunction useUnsavedChanges() {\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const markAsChanged = ()=>setHasUnsavedChanges(true);\n    const markAsSaved = ()=>setHasUnsavedChanges(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if (hasUnsavedChanges) {\n                e.preventDefault();\n                e.returnValue = \"You have unsaved changes. Are you sure you want to leave?\";\n                return e.returnValue;\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    }, [\n        hasUnsavedChanges\n    ]);\n    return {\n        hasUnsavedChanges,\n        markAsChanged,\n        markAsSaved\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/prevent-refresh.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/realtime-indicator.tsx":
/*!**************************************************!*\
  !*** ./src/components/ui/realtime-indicator.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeIndicator: () => (/* binding */ RealtimeIndicator),\n/* harmony export */   useRealtimeStatus: () => (/* binding */ useRealtimeStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RealtimeIndicator,useRealtimeStatus auto */ \n\n\n\n\nfunction RealtimeIndicator({ className, showText = false, size = \"sm\" }) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check initial online status\n        setIsOnline(navigator.onLine);\n        // Listen for online/offline events\n        const handleOnline = ()=>setIsOnline(true);\n        const handleOffline = ()=>setIsOnline(false);\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        // Set up Supabase connection monitoring\n        const channel = supabase.channel(\"connection-monitor\").on(\"presence\", {\n            event: \"sync\"\n        }, ()=>{\n            setIsConnected(true);\n            setLastActivity(new Date());\n        }).on(\"presence\", {\n            event: \"join\"\n        }, ()=>{\n            setIsConnected(true);\n            setLastActivity(new Date());\n        }).on(\"presence\", {\n            event: \"leave\"\n        }, ()=>{\n            setIsConnected(false);\n        }).subscribe((status)=>{\n            if (status === \"SUBSCRIBED\") {\n                setIsConnected(true);\n                setLastActivity(new Date());\n            } else if (status === \"CHANNEL_ERROR\" || status === \"TIMED_OUT\") {\n                setIsConnected(false);\n            }\n        });\n        // Monitor real-time activity with a test channel\n        const activityChannel = supabase.channel(\"activity-monitor\").on(\"broadcast\", {\n            event: \"ping\"\n        }, ()=>{\n            setLastActivity(new Date());\n        }).subscribe();\n        // Send periodic pings to test connectivity\n        const pingInterval = setInterval(()=>{\n            if (isOnline) {\n                activityChannel.send({\n                    type: \"broadcast\",\n                    event: \"ping\",\n                    payload: {\n                        timestamp: new Date().toISOString()\n                    }\n                });\n            }\n        }, 30000) // Ping every 30 seconds\n        ;\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n            clearInterval(pingInterval);\n            supabase.removeChannel(channel);\n            supabase.removeChannel(activityChannel);\n        };\n    }, [\n        supabase,\n        isOnline\n    ]);\n    const getStatusColor = ()=>{\n        if (!isOnline) return \"text-red-500\";\n        if (!isConnected) return \"text-yellow-500\";\n        return \"text-green-500\";\n    };\n    const getStatusText = ()=>{\n        if (!isOnline) return \"Offline\";\n        if (!isConnected) return \"Connecting...\";\n        return \"Real-time\";\n    };\n    const getIcon = ()=>{\n        if (!isOnline) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        if (!isConnected) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n    };\n    const getSizeClasses = ()=>{\n        switch(size){\n            case \"sm\":\n                return \"w-3 h-3\";\n            case \"md\":\n                return \"w-4 h-4\";\n            case \"lg\":\n                return \"w-5 h-5\";\n            default:\n                return \"w-3 h-3\";\n        }\n    };\n    const Icon = getIcon();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center space-x-1\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(getSizeClasses(), getStatusColor(), isConnected && isOnline && \"animate-pulse\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute inset-0 rounded-full animate-ping\", getSizeClasses(), \"bg-green-400 opacity-20\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-xs font-medium\", getStatusColor()),\n                children: getStatusText()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            lastActivity && isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-2 h-2 text-green-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: lastActivity.toLocaleTimeString([], {\n                            hour: \"2-digit\",\n                            minute: \"2-digit\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n// Hook to get real-time connection status\nfunction useRealtimeStatus() {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsOnline(navigator.onLine);\n        const handleOnline = ()=>setIsOnline(true);\n        const handleOffline = ()=>setIsOnline(false);\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        const channel = supabase.channel(\"status-monitor\").on(\"presence\", {\n            event: \"sync\"\n        }, ()=>{\n            setIsConnected(true);\n            setLastActivity(new Date());\n        }).subscribe((status)=>{\n            setIsConnected(status === \"SUBSCRIBED\");\n            if (status === \"SUBSCRIBED\") {\n                setLastActivity(new Date());\n            }\n        });\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n            supabase.removeChannel(channel);\n        };\n    }, [\n        supabase\n    ]);\n    return {\n        isConnected,\n        isOnline,\n        lastActivity,\n        status: !isOnline ? \"offline\" : !isConnected ? \"connecting\" : \"connected\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/realtime-indicator.tsx\n");

/***/ })

};
;