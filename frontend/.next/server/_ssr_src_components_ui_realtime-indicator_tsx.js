"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_ui_realtime-indicator_tsx";
exports.ids = ["_ssr_src_components_ui_realtime-indicator_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/ui/realtime-indicator.tsx":
/*!**************************************************!*\
  !*** ./src/components/ui/realtime-indicator.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeIndicator: () => (/* binding */ RealtimeIndicator),\n/* harmony export */   useRealtimeStatus: () => (/* binding */ useRealtimeStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Wifi,WifiOff,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RealtimeIndicator,useRealtimeStatus auto */ \n\n\n\n\nfunction RealtimeIndicator({ className, showText = false, size = \"sm\" }) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check initial online status\n        setIsOnline(navigator.onLine);\n        // Listen for online/offline events\n        const handleOnline = ()=>setIsOnline(true);\n        const handleOffline = ()=>setIsOnline(false);\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        // Set up Supabase connection monitoring\n        const channel = supabase.channel(\"connection-monitor\").on(\"presence\", {\n            event: \"sync\"\n        }, ()=>{\n            setIsConnected(true);\n            setLastActivity(new Date());\n        }).on(\"presence\", {\n            event: \"join\"\n        }, ()=>{\n            setIsConnected(true);\n            setLastActivity(new Date());\n        }).on(\"presence\", {\n            event: \"leave\"\n        }, ()=>{\n            setIsConnected(false);\n        }).subscribe((status)=>{\n            if (status === \"SUBSCRIBED\") {\n                setIsConnected(true);\n                setLastActivity(new Date());\n            } else if (status === \"CHANNEL_ERROR\" || status === \"TIMED_OUT\") {\n                setIsConnected(false);\n            }\n        });\n        // Monitor real-time activity with a test channel\n        const activityChannel = supabase.channel(\"activity-monitor\").on(\"broadcast\", {\n            event: \"ping\"\n        }, ()=>{\n            setLastActivity(new Date());\n        }).subscribe();\n        // Send periodic pings to test connectivity\n        const pingInterval = setInterval(()=>{\n            if (isOnline) {\n                activityChannel.send({\n                    type: \"broadcast\",\n                    event: \"ping\",\n                    payload: {\n                        timestamp: new Date().toISOString()\n                    }\n                });\n            }\n        }, 30000) // Ping every 30 seconds\n        ;\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n            clearInterval(pingInterval);\n            supabase.removeChannel(channel);\n            supabase.removeChannel(activityChannel);\n        };\n    }, [\n        supabase,\n        isOnline\n    ]);\n    const getStatusColor = ()=>{\n        if (!isOnline) return \"text-red-500\";\n        if (!isConnected) return \"text-yellow-500\";\n        return \"text-green-500\";\n    };\n    const getStatusText = ()=>{\n        if (!isOnline) return \"Offline\";\n        if (!isConnected) return \"Connecting...\";\n        return \"Real-time\";\n    };\n    const getIcon = ()=>{\n        if (!isOnline) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        if (!isConnected) return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        return _barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n    };\n    const getSizeClasses = ()=>{\n        switch(size){\n            case \"sm\":\n                return \"w-3 h-3\";\n            case \"md\":\n                return \"w-4 h-4\";\n            case \"lg\":\n                return \"w-5 h-5\";\n            default:\n                return \"w-3 h-3\";\n        }\n    };\n    const Icon = getIcon();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center space-x-1\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(getSizeClasses(), getStatusColor(), isConnected && isOnline && \"animate-pulse\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute inset-0 rounded-full animate-ping\", getSizeClasses(), \"bg-green-400 opacity-20\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-xs font-medium\", getStatusColor()),\n                children: getStatusText()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            lastActivity && isConnected && isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-2 h-2 text-green-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: lastActivity.toLocaleTimeString([], {\n                            hour: \"2-digit\",\n                            minute: \"2-digit\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/realtime-indicator.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n// Hook to get real-time connection status\nfunction useRealtimeStatus() {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastActivity, setLastActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsOnline(navigator.onLine);\n        const handleOnline = ()=>setIsOnline(true);\n        const handleOffline = ()=>setIsOnline(false);\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        const channel = supabase.channel(\"status-monitor\").on(\"presence\", {\n            event: \"sync\"\n        }, ()=>{\n            setIsConnected(true);\n            setLastActivity(new Date());\n        }).subscribe((status)=>{\n            setIsConnected(status === \"SUBSCRIBED\");\n            if (status === \"SUBSCRIBED\") {\n                setLastActivity(new Date());\n            }\n        });\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n            supabase.removeChannel(channel);\n        };\n    }, [\n        supabase\n    ]);\n    return {\n        isConnected,\n        isOnline,\n        lastActivity,\n        status: !isOnline ? \"offline\" : !isConnected ? \"connecting\" : \"connected\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/realtime-indicator.tsx\n");

/***/ })

};
;