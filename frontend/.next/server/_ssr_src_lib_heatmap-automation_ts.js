"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_heatmap-automation_ts";
exports.ids = ["_ssr_src_lib_heatmap-automation_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/heatmap-automation.ts":
/*!***************************************!*\
  !*** ./src/lib/heatmap-automation.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   processHeatmapAutomation: () => (/* binding */ processHeatmapAutomation),\n/* harmony export */   queueHeatmapAutomation: () => (/* binding */ queueHeatmapAutomation)\n/* harmony export */ });\n/* harmony import */ var _lib_microsoft_graph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/microsoft-graph */ \"(ssr)/./src/lib/microsoft-graph.ts\");\n\n// Constants for SharePoint configuration\nconst SHAREPOINT_SITE_URL = \"https://zn6bn.sharepoint.com/sites/files\";\nconst DRIVE_ID = \"b!6BliBHB7rkqGeijn571F56igGQK5UQ9GkI_AIFbTSvvooGrFp94hQr6gPCGK1yYV\";\n// File type mappings\nconst FILE_TYPE_MAPPINGS = {\n    photos: [\n        \".jpg\",\n        \".jpeg\",\n        \".png\"\n    ],\n    videos: [\n        \".mp4\",\n        \".mov\"\n    ],\n    srt: [\n        \".srt\"\n    ],\n    lrf: [\n        \".lrf\"\n    ]\n};\n/**\n * Get the Site ID from SharePoint site URL\n */ async function getSiteIdInternal(accessToken) {\n    try {\n        const url = new URL(SHAREPOINT_SITE_URL);\n        const hostname = url.hostname;\n        const sitePath = url.pathname;\n        const siteIdentifier = `${hostname}:${sitePath}:`;\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteIdentifier}`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.id;\n    } catch (error) {\n        console.error(\"Error getting Site ID:\", error);\n        throw new Error(\"Failed to get SharePoint Site ID\");\n    }\n}\n/**\n * List all files in a SharePoint folder\n */ async function listFilesInFolder(accessToken, folderId) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${folderId}/children`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.value || [];\n    } catch (error) {\n        console.error(\"Error listing files in folder:\", error);\n        throw error;\n    }\n}\n/**\n * Create a folder in SharePoint\n */ async function createFolder(accessToken, parentFolderId, folderName) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${parentFolderId}/children`, {\n            method: \"POST\",\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                name: folderName,\n                folder: {}\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Create folder error:\", response.status, errorText);\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.id;\n    } catch (error) {\n        console.error(`Error creating folder \"${folderName}\":`, error);\n        throw error;\n    }\n}\n/**\n * Move a file to a different folder\n */ async function moveFile(accessToken, fileId, targetFolderId, newName) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const updateData = {\n            parentReference: {\n                id: targetFolderId\n            }\n        };\n        if (newName) {\n            updateData.name = newName;\n        }\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${fileId}`, {\n            method: \"PATCH\",\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(updateData)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Move file error:\", response.status, errorText);\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n    } catch (error) {\n        console.error(\"Error moving file:\", error);\n        throw error;\n    }\n}\n/**\n * Get file type based on extension\n */ function getFileType(filename) {\n    const extension = \".\" + filename.split(\".\").pop()?.toLowerCase();\n    for (const [type, extensions] of Object.entries(FILE_TYPE_MAPPINGS)){\n        if (extensions.includes(extension)) {\n            return type;\n        }\n    }\n    return null;\n}\n/**\n * Generate a unique filename if duplicate exists\n */ function generateUniqueFilename(originalName, existingFiles) {\n    if (!existingFiles.includes(originalName)) {\n        return originalName;\n    }\n    const nameParts = originalName.split(\".\");\n    const extension = nameParts.pop();\n    const baseName = nameParts.join(\".\");\n    let counter = 1;\n    let newName;\n    do {\n        newName = `${baseName}_(${counter}).${extension}`;\n        counter++;\n    }while (existingFiles.includes(newName));\n    return newName;\n}\n/**\n * Create or get folder structure for file organization\n */ async function ensureFolderStructure(accessToken, scheduleFolderId) {\n    try {\n        // List existing folders\n        const existingItems = await listFilesInFolder(accessToken, scheduleFolderId);\n        const existingFolders = existingItems.filter((item)=>item.folder).map((item)=>item.name);\n        const folderStructure = {\n            photos: \"\",\n            videos: \"\",\n            srt: \"\",\n            lrf: \"\"\n        };\n        // Create Raw folder if it doesn't exist\n        let rawFolderId = existingItems.find((item)=>item.folder && item.name === \"Raw\")?.id;\n        if (!rawFolderId) {\n            rawFolderId = await createFolder(accessToken, scheduleFolderId, \"Raw\");\n        }\n        // Create subfolders under Raw\n        const rawItems = await listFilesInFolder(accessToken, rawFolderId);\n        const rawFolders = rawItems.filter((item)=>item.folder).map((item)=>item.name);\n        const subfolders = [\n            \"Photos\",\n            \"Videos\",\n            \"SRT\",\n            \"LRF\"\n        ];\n        for (const subfolder of subfolders){\n            let subfolderId = rawItems.find((item)=>item.folder && item.name === subfolder)?.id;\n            if (!subfolderId) {\n                subfolderId = await createFolder(accessToken, rawFolderId, subfolder);\n            }\n            const key = subfolder.toLowerCase();\n            folderStructure[key] = subfolderId;\n        }\n        return folderStructure;\n    } catch (error) {\n        console.error(\"Error ensuring folder structure:\", error);\n        throw error;\n    }\n}\n/**\n * Download file content as buffer for metadata extraction\n */ async function downloadFileContent(accessToken, fileId) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${fileId}/content`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const arrayBuffer = await response.arrayBuffer();\n        return Buffer.from(arrayBuffer);\n    } catch (error) {\n        console.error(\"Error downloading file content:\", error);\n        throw error;\n    }\n}\n/**\n * Extract EXIF GPS data from image buffer\n */ async function extractImageGPS(buffer) {\n    try {\n        // Use exifr library for EXIF extraction\n        const exifr = await __webpack_require__.e(/*! import() */ \"vendor-chunks/exifr\").then(__webpack_require__.bind(__webpack_require__, /*! exifr */ \"(ssr)/./node_modules/exifr/dist/full.esm.mjs\"));\n        const gpsData = await exifr.gps(buffer);\n        if (gpsData && gpsData.latitude && gpsData.longitude) {\n            return {\n                lat: gpsData.latitude,\n                lng: gpsData.longitude,\n                timestamp: gpsData.DateTimeOriginal?.toISOString()\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error extracting GPS from image:\", error);\n        return null;\n    }\n}\n/**\n * Parse SRT file for GPS coordinates\n */ async function parseSRTFile(buffer) {\n    try {\n        const srtContent = buffer.toString(\"utf-8\");\n        const parser = await __webpack_require__.e(/*! import() */ \"vendor-chunks/srt-parser-2\").then(__webpack_require__.bind(__webpack_require__, /*! srt-parser-2 */ \"(ssr)/./node_modules/srt-parser-2/dist/index.js\"));\n        const srtParser = new parser.default();\n        const subtitles = srtParser.fromSrt(srtContent);\n        const gpsCoordinates = [];\n        // Extract GPS coordinates from SRT content\n        for (const subtitle of subtitles){\n            const text = subtitle.text;\n            // Multiple regex patterns for different GPS coordinate formats in drone SRT files\n            const gpsPatterns = [\n                // Standard GPS format: GPS(lat,lng) or GPS: lat, lng\n                /GPS[\\s:]*\\(?(-?\\d+\\.?\\d*)[,\\s]+(-?\\d+\\.?\\d*)\\)?/i,\n                // Latitude/Longitude format: LAT: xx.xxx LON: yy.yyy\n                /LAT(?:ITUDE)?[\\s:]*(-?\\d+\\.?\\d*)[,\\s]*LON(?:GITUDE)?[\\s:]*(-?\\d+\\.?\\d*)/i,\n                // DJI format: [GPS] lat,lng\n                /\\[GPS\\][\\s:]*(-?\\d+\\.?\\d*)[,\\s]+(-?\\d+\\.?\\d*)/i,\n                // Coordinate format: lat,lng (simple comma-separated)\n                /^(-?\\d{1,3}\\.\\d+)[,\\s]+(-?\\d{1,3}\\.\\d+)$/,\n                // Home format: HOME(lat,lng)\n                /HOME[\\s:]*\\(?(-?\\d+\\.?\\d*)[,\\s]+(-?\\d+\\.?\\d*)\\)?/i,\n                // Position format: POS: lat lng\n                /POS(?:ITION)?[\\s:]*(-?\\d+\\.?\\d*)[,\\s]+(-?\\d+\\.?\\d*)/i\n            ];\n            for (const pattern of gpsPatterns){\n                const match = text.match(pattern);\n                if (match) {\n                    const lat = parseFloat(match[1]);\n                    const lng = parseFloat(match[2]);\n                    if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {\n                        gpsCoordinates.push({\n                            lat,\n                            lng\n                        });\n                        break; // Found valid coordinates, move to next subtitle\n                    }\n                }\n            }\n        }\n        if (gpsCoordinates.length > 0) {\n            return {\n                start: gpsCoordinates[0],\n                end: gpsCoordinates[gpsCoordinates.length - 1]\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error parsing SRT file:\", error);\n        return null;\n    }\n}\n/**\n * Create a public share link for a file\n */ async function createPublicShareLink(accessToken, fileId) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${fileId}/createLink`, {\n            method: \"POST\",\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                type: \"view\",\n                scope: \"anonymous\"\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.link.webUrl;\n    } catch (error) {\n        console.error(\"Error creating public share link:\", error);\n        throw error;\n    }\n}\n/**\n * Upload geo metadata JSON file to SharePoint\n */ async function uploadGeoMetadataFile(accessToken, scheduleFolderId, metadata) {\n    try {\n        const siteId = await getSiteIdInternal(accessToken);\n        const jsonContent = JSON.stringify(metadata, null, 2);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${scheduleFolderId}:/geo_metadata.json:/content`, {\n            method: \"PUT\",\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: jsonContent\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.id;\n    } catch (error) {\n        console.error(\"Error uploading geo metadata file:\", error);\n        throw error;\n    }\n}\n/**\n * Main function to process heatmap automation for a schedule\n */ async function processHeatmapAutomation(scheduleId) {\n    try {\n        console.log(\"\\uD83D\\uDDFA️ Starting heatmap automation for schedule:\", scheduleId);\n        // Get access token\n        const accessToken = await (0,_lib_microsoft_graph__WEBPACK_IMPORTED_MODULE_0__.getMicrosoftGraphAccessToken)();\n        // Get schedule folder information from database\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const { data: schedule, error } = await supabase.from(\"schedules\").select(`\n        id,\n        custom_id,\n        sharepoint_folder_id,\n        projects!inner (\n          id,\n          custom_id,\n          name,\n          clients!inner (\n            id,\n            custom_id,\n            name\n          )\n        )\n      `).eq(\"id\", scheduleId).single();\n        if (error || !schedule || !schedule.sharepoint_folder_id) {\n            throw new Error(\"Schedule not found or SharePoint folder not created\");\n        }\n        const scheduleFolderId = schedule.sharepoint_folder_id;\n        // 1. List all files in the schedule folder\n        console.log(\"\\uD83D\\uDCC2 Listing files in schedule folder...\");\n        const allFiles = await listFilesInFolder(accessToken, scheduleFolderId);\n        const files = allFiles.filter((item)=>!item.folder) // Only files, not folders\n        ;\n        if (files.length === 0) {\n            return {\n                success: true,\n                message: \"No files found to process\"\n            };\n        }\n        // 2. Create folder structure\n        console.log(\"\\uD83D\\uDCC1 Creating folder structure...\");\n        const folderStructure = await ensureFolderStructure(accessToken, scheduleFolderId);\n        // 3. Organize files by type\n        console.log(\"\\uD83D\\uDDC2️ Organizing files by type...\");\n        const organizedFiles = {\n            photos: [],\n            videos: [],\n            srt: [],\n            lrf: [],\n            other: []\n        };\n        for (const file of files){\n            const fileType = getFileType(file.name);\n            if (fileType) {\n                organizedFiles[fileType].push(file);\n            } else {\n                organizedFiles.other.push(file);\n            }\n        }\n        // Move files to appropriate folders\n        for (const [type, typeFiles] of Object.entries(organizedFiles)){\n            if (type === \"other\" || typeFiles.length === 0) continue;\n            const targetFolderId = folderStructure[type];\n            if (!targetFolderId) continue;\n            // Get existing files in target folder to handle duplicates\n            const existingFiles = await listFilesInFolder(accessToken, targetFolderId);\n            const existingFilenames = existingFiles.map((f)=>f.name);\n            for (const file of typeFiles){\n                const uniqueName = generateUniqueFilename(file.name, existingFilenames);\n                await moveFile(accessToken, file.id, targetFolderId, uniqueName !== file.name ? uniqueName : undefined);\n                existingFilenames.push(uniqueName);\n                console.log(`📦 Moved ${file.name} to ${type} folder`);\n            }\n        }\n        // 4. Extract metadata from organized files (with parallel processing)\n        console.log(\"\\uD83C\\uDF0D Extracting geolocation metadata...\");\n        const geoMetadata = [];\n        // Process photos in parallel (batches of 10 for performance)\n        const photoBatches = [];\n        for(let i = 0; i < organizedFiles.photos.length; i += 10){\n            photoBatches.push(organizedFiles.photos.slice(i, i + 10));\n        }\n        for (const batch of photoBatches){\n            const photoPromises = batch.map(async (photoFile)=>{\n                try {\n                    const buffer = await downloadFileContent(accessToken, photoFile.id);\n                    const gpsData = await extractImageGPS(buffer);\n                    if (gpsData) {\n                        console.log(`📸 Extracted GPS from photo: ${photoFile.name}`);\n                        return {\n                            type: \"photo\",\n                            filename: photoFile.name,\n                            lat: gpsData.lat,\n                            lng: gpsData.lng,\n                            timestamp: gpsData.timestamp\n                        };\n                    }\n                } catch (error) {\n                    console.warn(`⚠️ Failed to extract GPS from photo ${photoFile.name}:`, error);\n                }\n                return null;\n            });\n            const batchResults = await Promise.all(photoPromises);\n            geoMetadata.push(...batchResults.filter((result)=>result !== null));\n        }\n        // Process videos with SRT files in parallel\n        const videoPromises = organizedFiles.videos.map(async (videoFile)=>{\n            try {\n                // Find matching SRT file\n                const videoBaseName = videoFile.name.replace(/\\.[^/.]+$/, \"\");\n                const matchingSRT = organizedFiles.srt.find((srtFile)=>srtFile.name.toLowerCase().includes(videoBaseName.toLowerCase()));\n                if (matchingSRT) {\n                    const buffer = await downloadFileContent(accessToken, matchingSRT.id);\n                    const gpsData = await parseSRTFile(buffer);\n                    if (gpsData) {\n                        console.log(`🎥 Extracted GPS from video: ${videoFile.name}`);\n                        return {\n                            type: \"video\",\n                            filename: videoFile.name,\n                            start: gpsData.start,\n                            end: gpsData.end\n                        };\n                    }\n                }\n            } catch (error) {\n                console.warn(`⚠️ Failed to extract GPS from video ${videoFile.name}:`, error);\n            }\n            return null;\n        });\n        const videoResults = await Promise.all(videoPromises);\n        geoMetadata.push(...videoResults.filter((result)=>result !== null));\n        // 5. Generate and upload geo_metadata.json\n        console.log(\"\\uD83D\\uDCC4 Generating geo_metadata.json...\");\n        const metadataFileId = await uploadGeoMetadataFile(accessToken, scheduleFolderId, geoMetadata);\n        // 6. Create public share link\n        console.log(\"\\uD83D\\uDD17 Creating public share link...\");\n        const shareLink = await createPublicShareLink(accessToken, metadataFileId);\n        // 7. Store share link in database\n        await supabase.from(\"schedules\").update({\n            geo_metadata_link: shareLink,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", scheduleId);\n        console.log(\"✅ Heatmap automation completed successfully\");\n        return {\n            success: true,\n            message: `Processed ${geoMetadata.length} geotagged files and created metadata file`,\n            shareLink\n        };\n    } catch (error) {\n        console.error(\"❌ Heatmap automation failed:\", error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : \"Unknown error occurred\"\n        };\n    }\n}\n/**\n * Queue heatmap automation as a background job\n */ function queueHeatmapAutomation(scheduleId) {\n    const { queueBackgroundJob } = __webpack_require__(/*! @/lib/background-jobs */ \"(ssr)/./src/lib/background-jobs.ts\");\n    return queueBackgroundJob(\"heatmap_automation\", \"schedule\", scheduleId, {\n        scheduleId\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/heatmap-automation.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/microsoft-graph.ts":
/*!************************************!*\
  !*** ./src/lib/microsoft-graph.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkIfFolderExists: () => (/* binding */ folderExists),\n/* harmony export */   createClientFolder: () => (/* binding */ createClientFolder),\n/* harmony export */   createFolder: () => (/* binding */ createFolder),\n/* harmony export */   createProjectFolder: () => (/* binding */ createProjectFolder),\n/* harmony export */   createProjectFolderStructure: () => (/* binding */ createProjectFolderStructure),\n/* harmony export */   createScheduleFolder: () => (/* binding */ createScheduleFolder),\n/* harmony export */   createSubfolderIfNotExists: () => (/* binding */ createSubfolderIfNotExists),\n/* harmony export */   getClientsFolderId: () => (/* binding */ getClientsFolderId),\n/* harmony export */   getMicrosoftGraphAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getRootFolderId: () => (/* binding */ getRootFolderId),\n/* harmony export */   getScheduleFolderLinks: () => (/* binding */ getScheduleFolderLinks),\n/* harmony export */   storeClientFolderInDatabase: () => (/* binding */ storeClientFolderInDatabase),\n/* harmony export */   storeProjectFolderInDatabase: () => (/* binding */ storeProjectFolderInDatabase)\n/* harmony export */ });\n// Function to load environment variables from .env.local if not already loaded\nfunction loadEnvIfNeeded() {\n    // Only run on server side\n    if (false) {}\n    // Check if Microsoft Graph credentials are already loaded\n    if (process.env.MICROSOFT_CLIENT_ID && process.env.MICROSOFT_CLIENT_SECRET && process.env.MICROSOFT_TENANT_ID) {\n        return;\n    }\n    // Temporarily disabled to fix chunk loading issues\n    console.log(\"Microsoft Graph environment loading temporarily disabled\");\n}\n// Constants for SharePoint configuration\nconst SHAREPOINT_SITE_URL = \"https://zn6bn.sharepoint.com/sites/files\";\nconst DRIVE_ID = \"b!6BliBHB7rkqGeijn571F56igGQK5UQ9GkI_AIFbTSvvooGrFp94hQr6gPCGK1yYV\";\n// Cache for site ID and access tokens to avoid repeated API calls\nlet cachedSiteId = null;\nlet cachedAccessToken = null;\nlet tokenExpiry = null;\n// Token refresh threshold (5 minutes before expiry)\nconst TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000;\n/**\n * Get the Site ID from SharePoint site URL\n * @param accessToken Microsoft Graph API access token\n * @returns Site ID\n */ async function getSiteId(accessToken) {\n    if (cachedSiteId) {\n        return cachedSiteId;\n    }\n    try {\n        // Extract hostname and site path from the SharePoint URL\n        const url = new URL(SHAREPOINT_SITE_URL);\n        const hostname = url.hostname;\n        const sitePath = url.pathname; // This will be '/sites/files'\n        // Format for Microsoft Graph API: hostname:/sites/sitename:\n        const siteIdentifier = `${hostname}:${sitePath}:`;\n        console.log(\"Getting Site ID for:\", siteIdentifier);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteIdentifier}`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Site ID request failed:\", response.status, errorText);\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log(\"Site ID retrieved successfully:\", data.id);\n        cachedSiteId = data.id;\n        return data.id;\n    } catch (error) {\n        console.error(\"Error getting Site ID:\", error);\n        throw new Error(\"Failed to get SharePoint Site ID\");\n    }\n}\n/**\n * Get Microsoft Graph API access token using client credentials flow\n * @returns Access token for Microsoft Graph API\n */ async function getAccessToken() {\n    // Check if we have a cached token that's still valid\n    const now = Date.now();\n    if (cachedAccessToken && tokenExpiry && now < tokenExpiry - TOKEN_REFRESH_THRESHOLD) {\n        return cachedAccessToken;\n    }\n    // Ensure environment variables are loaded\n    loadEnvIfNeeded();\n    const clientId = process.env.MICROSOFT_CLIENT_ID;\n    const clientSecret = process.env.MICROSOFT_CLIENT_SECRET;\n    const tenantId = process.env.MICROSOFT_TENANT_ID;\n    if (!clientId || !clientSecret || !tenantId) {\n        throw new Error(\"Missing Microsoft Graph API credentials in environment variables\");\n    }\n    try {\n        const response = await fetch(`https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            },\n            body: new URLSearchParams({\n                client_id: clientId,\n                scope: \"https://graph.microsoft.com/.default\",\n                client_secret: clientSecret,\n                grant_type: \"client_credentials\"\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        // Cache the token and its expiry time (typically 1 hour)\n        cachedAccessToken = data.access_token;\n        tokenExpiry = now + data.expires_in * 1000;\n        return data.access_token;\n    } catch (error) {\n        console.error(\"Error getting Microsoft Graph API access token:\", error);\n        throw new Error(\"Failed to authenticate with Microsoft Graph API\");\n    }\n}\n/**\n * Check if a folder exists in SharePoint\n * @param accessToken Microsoft Graph API access token\n * @param folderPath Path to check\n * @returns Folder information if exists, null otherwise\n */ async function folderExists(accessToken, folderPath) {\n    try {\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/root:${folderPath}`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            // If folder doesn't exist, Graph API returns 404\n            if (response.status === 404) {\n                return null;\n            }\n            // Re-throw other errors\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            id: data.id,\n            webUrl: data.webUrl\n        };\n    } catch (error) {\n        console.error(`Error checking if folder exists at \"${folderPath}\":`, error);\n        throw error;\n    }\n}\n/**\n * Create a folder in SharePoint with retry logic\n * @param accessToken Microsoft Graph API access token\n * @param parentFolderId ID of parent folder\n * @param folderName Name of folder to create\n * @returns Created folder information\n */ async function createFolder(accessToken, parentFolderId, folderName) {\n    const maxRetries = 3;\n    let lastError;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            const siteId = await getSiteId(accessToken);\n            const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${parentFolderId}/children`, {\n                method: \"POST\",\n                headers: {\n                    Authorization: `Bearer ${accessToken}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    name: folderName,\n                    folder: {}\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            return {\n                id: data.id,\n                webUrl: data.webUrl\n            };\n        } catch (error) {\n            console.error(`Error creating folder \"${folderName}\" (attempt ${attempt}/${maxRetries}):`, error);\n            lastError = error;\n            // Wait before retrying (exponential backoff)\n            if (attempt < maxRetries) {\n                await new Promise((resolve)=>setTimeout(resolve, Math.pow(2, attempt) * 1000));\n            }\n        }\n    }\n    throw new Error(`Failed to create folder \"${folderName}\" after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : \"Unknown error\"}`);\n}\n/**\n * Create a share link for a folder with \"anyone with the link\" permissions\n * @param accessToken Microsoft Graph API access token\n * @param folderId ID of the folder to create share link for\n * @returns Share link URL\n */ async function createShareLink(accessToken, folderId) {\n    try {\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${folderId}/createLink`, {\n            method: \"POST\",\n            headers: {\n                Authorization: `Bearer ${accessToken}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                type: \"view\",\n                scope: \"anonymous\"\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.link.webUrl;\n    } catch (error) {\n        console.error(`Error creating share link for folder ID \"${folderId}\":`, error);\n        throw new Error(`Failed to create share link for folder`);\n    }\n}\n/**\n * Get the ID of the root drive folder\n * @param accessToken Microsoft Graph API access token\n * @returns ID of the root folder\n */ async function getRootFolderId(accessToken) {\n    try {\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/root`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.id;\n    } catch (error) {\n        console.error(\"Error getting root folder ID:\", error);\n        throw new Error(\"Failed to get root folder ID\");\n    }\n}\n/**\n * Create a client folder in SharePoint root directory\n * @param clientId ID of the client to associate with the folder\n * @param clientCustomId Custom ID of the client (e.g., CYMCL-25-001)\n * @param clientName Name of the client\n * @returns Object containing folder information\n */ async function createClientFolder(clientId, clientCustomId, clientName) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Get the root folder ID\n        const rootFolderId = await getRootFolderId(accessToken);\n        // Create folder name: {ClientCustomID} {ClientName}\n        const folderName = `${clientCustomId} ${clientName}`;\n        // Check if folder already exists\n        const existingFolder = await folderExists(accessToken, `/${folderName}`);\n        let clientFolder;\n        if (existingFolder) {\n            console.log(`Client folder \"${folderName}\" already exists`);\n            clientFolder = existingFolder;\n        } else {\n            console.log(`Creating client folder \"${folderName}\"`);\n            clientFolder = await createFolder(accessToken, rootFolderId, folderName);\n        }\n        // Create share link for the client folder\n        let shareLink;\n        try {\n            shareLink = await createShareLink(accessToken, clientFolder.id);\n            console.log(`Created share link for client folder: ${shareLink}`);\n        } catch (error) {\n            console.warn(\"Failed to create share link for client folder:\", error);\n        // Continue without share link\n        }\n        // Store folder information in the database\n        await storeClientFolderInDatabase(clientId, clientFolder.id, clientFolder.webUrl, shareLink);\n        return {\n            folder: {\n                ...clientFolder,\n                shareLink\n            }\n        };\n    } catch (error) {\n        console.error(\"Error creating client folder:\", error);\n        throw new Error(`Failed to create client folder: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n    }\n}\n/**\n * Create a project folder in SharePoint\n * @param projectId ID of the project\n * @param projectCustomId Custom ID of the project\n * @param projectName Name of the project\n * @param clientId ID of the client (parent folder)\n * @returns Result containing folder information\n */ async function createProjectFolder(projectId, projectCustomId, projectName, clientId) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Get client information from database using server-side client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log(\"\\uD83D\\uDD0D Environment variable check in storeProjectFolderInDatabase:\", {\n            supabaseUrl: supabaseUrl ? \"Present\" : \"Missing\",\n            supabaseServiceKey: supabaseServiceKey ? \"Present\" : \"Missing\",\n            nodeEnv: \"development\",\n            context: \"background-job\"\n        });\n        if (!supabaseUrl) {\n            console.error(\"❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context\");\n            throw new Error(\"NEXT_PUBLIC_SUPABASE_URL is required\");\n        }\n        if (!supabaseServiceKey) {\n            console.error(\"❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context\");\n            throw new Error(\"SUPABASE_SERVICE_ROLE_KEY is required\");\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const { data: client, error: clientError } = await supabase.from(\"clients\").select(\"custom_id, name\").eq(\"id\", clientId).single();\n        if (clientError || !client) {\n            throw new Error(`Failed to get client information: ${clientError?.message || \"Client not found\"}`);\n        }\n        // Create client folder name and project folder name\n        const clientFolderName = `${client.custom_id} ${client.name}`;\n        const projectFolderName = `${projectCustomId} ${projectName}`;\n        // Check if client folder exists, create if not\n        const clientFolderPath = `/${clientFolderName}`;\n        let clientFolder = await folderExists(accessToken, clientFolderPath);\n        if (!clientFolder) {\n            const rootFolderId = await getRootFolderId(accessToken);\n            clientFolder = await createFolder(accessToken, rootFolderId, clientFolderName);\n        }\n        // Check if project folder already exists\n        const projectFolderPath = `/${clientFolderName}/${projectFolderName}`;\n        const existingProjectFolder = await folderExists(accessToken, projectFolderPath);\n        let projectFolder;\n        if (existingProjectFolder) {\n            console.log(`Project folder \"${projectFolderName}\" already exists`);\n            projectFolder = existingProjectFolder;\n        } else {\n            console.log(`Creating project folder \"${projectFolderName}\"`);\n            projectFolder = await createFolder(accessToken, clientFolder.id, projectFolderName);\n        }\n        // Create share link for the project folder\n        let shareLink;\n        try {\n            shareLink = await createShareLink(accessToken, projectFolder.id);\n            console.log(`Created share link for project folder: ${shareLink}`);\n        } catch (error) {\n            console.warn(\"Failed to create share link for project folder:\", error);\n        // Continue without share link\n        }\n        // Store folder information in the database\n        await storeProjectFolderInDatabase(projectId, projectFolder.id, projectFolder.webUrl, shareLink);\n        return {\n            folder: {\n                ...projectFolder,\n                shareLink\n            }\n        };\n    } catch (error) {\n        console.error(\"Error creating project folder:\", error);\n        throw new Error(`Failed to create project folder: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n    }\n}\n/**\n * Create a schedule folder inside the project folder\n * @param scheduleId ID of the schedule to associate with the folder\n * @param scheduleCustomId Custom ID of the schedule (e.g., CYM-25-005)\n * @param scheduleDate Date of the schedule (YYYY-MM-DD format)\n * @param projectCustomId Custom ID of the project\n * @param projectName Name of the project\n * @param clientCustomId Custom ID of the client\n * @param clientName Name of the client\n * @returns Object containing folder information for the main schedule folder\n */ async function createScheduleFolder(scheduleId, scheduleCustomId, scheduleDate, projectCustomId, projectName, clientCustomId, clientName) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Create folder names\n        const clientFolderName = `${clientCustomId} ${clientName}`;\n        const projectFolderName = `${projectCustomId} ${projectName}`;\n        const scheduleFolderName = `${scheduleCustomId} ${scheduleDate}`;\n        // Ensure client folder exists\n        const clientFolderPath = `/${clientFolderName}`;\n        let clientFolder = await folderExists(accessToken, clientFolderPath);\n        if (!clientFolder) {\n            const rootFolderId = await getRootFolderId(accessToken);\n            clientFolder = await createFolder(accessToken, rootFolderId, clientFolderName);\n        }\n        // Ensure project folder exists\n        const projectFolderPath = `/${clientFolderName}/${projectFolderName}`;\n        let projectFolder = await folderExists(accessToken, projectFolderPath);\n        if (!projectFolder) {\n            projectFolder = await createFolder(accessToken, clientFolder.id, projectFolderName);\n        }\n        // Check if schedule folder already exists\n        const scheduleFolderPath = `/${clientFolderName}/${projectFolderName}/${scheduleFolderName}`;\n        const existingScheduleFolder = await folderExists(accessToken, scheduleFolderPath);\n        let scheduleFolder;\n        if (existingScheduleFolder) {\n            console.log(`Schedule folder \"${scheduleFolderName}\" already exists`);\n            scheduleFolder = existingScheduleFolder;\n        } else {\n            console.log(`Creating schedule folder \"${scheduleFolderName}\"`);\n            scheduleFolder = await createFolder(accessToken, projectFolder.id, scheduleFolderName);\n        }\n        // Create Raw and Output subfolders inside the schedule folder\n        try {\n            console.log(`Creating \"Raw\" subfolder in schedule folder \"${scheduleFolderName}\"`);\n            await createFolder(accessToken, scheduleFolder.id, \"Raw\");\n            console.log(`Successfully created \"Raw\" subfolder`);\n            console.log(`Creating \"Output\" subfolder in schedule folder \"${scheduleFolderName}\"`);\n            await createFolder(accessToken, scheduleFolder.id, \"Output\");\n            console.log(`Successfully created \"Output\" subfolder`);\n        } catch (subfolderError) {\n            console.warn(\"Failed to create subfolders (Raw/Output) in schedule folder:\", subfolderError);\n        // Don't fail the entire operation if subfolder creation fails\n        }\n        // Create share link for the schedule folder\n        let scheduleShareLink;\n        try {\n            scheduleShareLink = await createShareLink(accessToken, scheduleFolder.id);\n            console.log(`Created share link for schedule folder: ${scheduleShareLink}`);\n        } catch (error) {\n            console.warn(\"Failed to create share link for schedule folder:\", error);\n        }\n        // Store folder information in the database (only main schedule folder, not subfolders)\n        await storeScheduleFolderLinksInDatabase(scheduleId, scheduleFolder.id, scheduleFolder.webUrl, scheduleShareLink);\n        return {\n            folder: {\n                ...scheduleFolder,\n                shareLink: scheduleShareLink\n            }\n        };\n    } catch (error) {\n        console.error(\"Error creating schedule folder:\", error);\n        throw new Error(`Failed to create schedule folder: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n    }\n}\n/**\n * Create folder structure in SharePoint for a project and store links in database\n * @param scheduleId ID of the schedule to associate with the folders\n * @param clientName Name of the client\n * @param projectName Name of the project\n * @param date Date of the project (YYYY-MM-DD format)\n * @returns Object containing folder information for the main schedule folder\n */ async function createProjectFolderStructure(scheduleId, clientName, projectName, date) {\n    try {\n        // Get access token\n        const accessToken = await getAccessToken();\n        // Get the Clients folder ID (root for our structure)\n        let currentFolderId = await getClientsFolderId(accessToken);\n        // Build the folder path: /Clients/<clientName>/<projectName>/<date>/\n        const folderPath = `/${clientName}/${projectName}/${date}`;\n        const fullPath = `/Clients${folderPath}`;\n        // Create each folder in the path if it doesn't exist\n        const pathParts = [\n            clientName,\n            projectName,\n            date\n        ];\n        for (const part of pathParts){\n            const currentPath = `/Clients/${pathParts.slice(0, pathParts.indexOf(part) + 1).join(\"/\")}`;\n            const existingFolder = await folderExists(accessToken, currentPath);\n            if (existingFolder) {\n                console.log(`Folder \"${part}\" already exists at ${currentPath}`);\n                currentFolderId = existingFolder.id;\n            } else {\n                console.log(`Creating folder \"${part}\" at ${currentPath}`);\n                const newFolder = await createFolder(accessToken, currentFolderId, part);\n                currentFolderId = newFolder.id;\n            }\n        }\n        // Create share link for the main folder\n        const shareLink = await createShareLink(accessToken, currentFolderId);\n        // Get the main folder information\n        const mainFolder = {\n            id: currentFolderId,\n            webUrl: fullPath,\n            shareLink: shareLink\n        };\n        // Store folder information in the database\n        await storeScheduleFolderLinksInDatabase(scheduleId, mainFolder.id, mainFolder.webUrl, mainFolder.shareLink);\n        return mainFolder;\n    } catch (error) {\n        console.error(\"Error creating project folder structure:\", error);\n        throw new Error(`Failed to create folder structure: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n    }\n}\n/**\n * Create a subfolder if it doesn't already exist\n * @param accessToken Microsoft Graph API access token\n * @param parentFolderId ID of parent folder\n * @param folderName Name of folder to create\n * @returns Folder information\n */ async function createSubfolderIfNotExists(accessToken, parentFolderId, folderName) {\n    try {\n        // Try to get the folder first\n        const siteId = await getSiteId(accessToken);\n        const response = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${parentFolderId}/children?$filter=name eq '${folderName}'`, {\n            headers: {\n                Authorization: `Bearer ${accessToken}`\n            }\n        });\n        if (!response.ok) {\n            // If we get an error other than 404, re-throw it\n            if (response.status !== 404) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n        // If 404, continue to create the folder\n        } else {\n            const data = await response.json();\n            // Check if folder exists in response\n            if (data.value && data.value.length > 0) {\n                const existingFolder = data.value[0];\n                if (existingFolder.folder) {\n                    console.log(`Folder \"${folderName}\" already exists`);\n                    return {\n                        id: existingFolder.id,\n                        webUrl: existingFolder.webUrl\n                    };\n                }\n            }\n        }\n        // Folder doesn't exist, create it\n        console.log(`Creating folder \"${folderName}\"`);\n        return await createFolder(accessToken, parentFolderId, folderName);\n    } catch (error) {\n        console.error(`Error checking/creating folder \"${folderName}\":`, error);\n        // Try to create the folder directly\n        return await createFolder(accessToken, parentFolderId, folderName);\n    }\n}\n/**\n * Store client folder information in the database\n * @param clientId ID of the client to update\n * @param folderId SharePoint ID of the client folder\n * @param folderUrl SharePoint URL of the client folder\n * @param shareLink Public share link for the client folder (optional)\n */ async function storeClientFolderInDatabase(clientId, folderId, folderUrl, shareLink) {\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log(\"\\uD83D\\uDD0D Environment variable check in storeClientFolderInDatabase:\", {\n            supabaseUrl: supabaseUrl ? \"Present\" : \"Missing\",\n            supabaseServiceKey: supabaseServiceKey ? \"Present\" : \"Missing\",\n            nodeEnv: \"development\",\n            context: \"background-job\"\n        });\n        if (!supabaseUrl) {\n            console.error(\"❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context\");\n            throw new Error(\"NEXT_PUBLIC_SUPABASE_URL is required\");\n        }\n        if (!supabaseServiceKey) {\n            console.error(\"❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context\");\n            throw new Error(\"SUPABASE_SERVICE_ROLE_KEY is required\");\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const updateData = {\n            sharepoint_folder_id: folderId,\n            sharepoint_folder_url: folderUrl\n        };\n        if (shareLink) {\n            updateData.sharepoint_share_link = shareLink;\n        }\n        const { error } = await supabase.from(\"clients\").update(updateData).eq(\"id\", clientId);\n        if (error) {\n            console.error(\"Error storing client folder in database:\", error);\n            throw new Error(\"Failed to store client folder in database\");\n        }\n        console.log(\"Successfully stored client folder in database for client:\", clientId);\n    } catch (error) {\n        console.error(\"Error in storeClientFolderInDatabase:\", error);\n        throw error;\n    }\n}\n/**\n * Store project folder information in the database\n * @param projectId ID of the project to update\n * @param folderId SharePoint ID of the project folder\n * @param folderUrl SharePoint URL of the project folder\n * @param shareLink Public share link for the project folder (optional)\n */ async function storeProjectFolderInDatabase(projectId, folderId, folderUrl, shareLink) {\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log(\"\\uD83D\\uDD0D Environment variable check in getScheduleFolderLinks:\", {\n            supabaseUrl: supabaseUrl ? \"Present\" : \"Missing\",\n            supabaseServiceKey: supabaseServiceKey ? \"Present\" : \"Missing\",\n            nodeEnv: \"development\",\n            context: \"background-job\"\n        });\n        if (!supabaseUrl) {\n            console.error(\"❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context\");\n            throw new Error(\"NEXT_PUBLIC_SUPABASE_URL is required\");\n        }\n        if (!supabaseServiceKey) {\n            console.error(\"❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context\");\n            throw new Error(\"SUPABASE_SERVICE_ROLE_KEY is required\");\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const updateData = {\n            sharepoint_folder_id: folderId,\n            sharepoint_folder_url: folderUrl\n        };\n        if (shareLink) {\n            updateData.sharepoint_share_link = shareLink;\n        }\n        const { error } = await supabase.from(\"projects\").update(updateData).eq(\"id\", projectId);\n        if (error) {\n            console.error(\"Error storing project folder in database:\", error);\n            throw new Error(\"Failed to store project folder in database\");\n        }\n        console.log(\"Successfully stored project folder in database for project:\", projectId);\n    } catch (error) {\n        console.error(\"Error in storeProjectFolderInDatabase:\", error);\n        throw error;\n    }\n}\n/**\n * Store schedule folder links in the schedules table\n * @param scheduleId ID of the schedule\n * @param scheduleFolderId SharePoint ID of the schedule folder\n * @param scheduleFolderUrl SharePoint URL of the schedule folder\n * @param scheduleShareLink Public share link for the schedule folder\n */ async function storeScheduleFolderLinksInDatabase(scheduleId, scheduleFolderId, scheduleFolderUrl, scheduleShareLink) {\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables with better error context\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        console.log(\"\\uD83D\\uDD0D Environment variable check in storeScheduleFolderLinksInDatabase:\", {\n            supabaseUrl: supabaseUrl ? \"Present\" : \"Missing\",\n            supabaseServiceKey: supabaseServiceKey ? \"Present\" : \"Missing\",\n            nodeEnv: \"development\",\n            context: \"background-job\"\n        });\n        if (!supabaseUrl) {\n            console.error(\"❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context\");\n            throw new Error(\"NEXT_PUBLIC_SUPABASE_URL is required\");\n        }\n        if (!supabaseServiceKey) {\n            console.error(\"❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context\");\n            throw new Error(\"SUPABASE_SERVICE_ROLE_KEY is required\");\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        // Prepare update data for schedules table\n        const updateData = {\n            sharepoint_folder_id: scheduleFolderId,\n            sharepoint_folder_url: scheduleFolderUrl\n        };\n        if (scheduleShareLink) {\n            updateData.sharepoint_share_link = scheduleShareLink;\n        }\n        // Update the schedules table\n        const { error } = await supabase.from(\"schedules\").update(updateData).eq(\"id\", scheduleId);\n        if (error) {\n            console.error(\"Error storing schedule folder links in database:\", error);\n            throw new Error(\"Failed to store schedule folder links in database\");\n        }\n        console.log(\"Successfully stored schedule folder links in schedules table for schedule:\", scheduleId);\n    } catch (error) {\n        console.error(\"Error in storeScheduleFolderLinksInDatabase:\", error);\n        throw error;\n    }\n}\n/**\n * Get schedule folder links from the database\n * @param scheduleId ID of the schedule\n * @returns Object containing folder information for the main schedule folder, or null if not found\n */ async function getScheduleFolderLinks(scheduleId) {\n    // Skip on client side\n    if (false) {}\n    try {\n        // Use server-side Supabase client\n        const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\"));\n        // Validate environment variables\n        const supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\n        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n        if (!supabaseUrl) {\n            throw new Error(\"NEXT_PUBLIC_SUPABASE_URL is required\");\n        }\n        if (!supabaseServiceKey) {\n            throw new Error(\"SUPABASE_SERVICE_ROLE_KEY is required\");\n        }\n        const supabase = createClient(supabaseUrl, supabaseServiceKey);\n        const { data, error } = await supabase.from(\"schedules\").select(`\n        sharepoint_folder_id,\n        sharepoint_folder_url,\n        sharepoint_share_link\n      `).eq(\"id\", scheduleId).single();\n        if (error) {\n            console.error(\"Error getting schedule folder links from database:\", error);\n            return null;\n        }\n        if (!data || !data.sharepoint_folder_id || !data.sharepoint_folder_url) {\n            console.log(\"No schedule folder links found for schedule:\", scheduleId);\n            return null;\n        }\n        const result = {\n            id: data.sharepoint_folder_id,\n            webUrl: data.sharepoint_folder_url,\n            shareLink: data.sharepoint_share_link\n        };\n        return result;\n    } catch (error) {\n        console.error(\"Error in getScheduleFolderLinks:\", error);\n        return null;\n    }\n}\n/**\n * Get the ID of the Clients folder (legacy function for compatibility)\n * @param accessToken Microsoft Graph API access token\n * @returns ID of the Clients folder\n */ async function getClientsFolderId(accessToken) {\n    // For backward compatibility, return the root folder ID\n    return await getRootFolderId(accessToken);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/microsoft-graph.ts\n");

/***/ })

};
;