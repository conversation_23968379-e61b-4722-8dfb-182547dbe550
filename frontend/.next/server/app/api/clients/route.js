"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/clients/route";
exports.ids = ["app/api/clients/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fclients%2Froute&page=%2Fapi%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclients%2Froute.ts&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fclients%2Froute&page=%2Fapi%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclients%2Froute.ts&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_vijayyaso_Documents_Development_cymatics_frontend_src_app_api_clients_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/clients/route.ts */ \"(rsc)/./src/app/api/clients/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/clients/route\",\n        pathname: \"/api/clients\",\n        filename: \"route\",\n        bundlePath: \"app/api/clients/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/api/clients/route.ts\",\n    nextConfigOutput,\n    userland: _Users_vijayyaso_Documents_Development_cymatics_frontend_src_app_api_clients_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/clients/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fclients%2Froute&page=%2Fapi%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclients%2Froute.ts&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/clients/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/clients/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nconst supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        console.log(\"Server-side client creation started:\", body);\n        // Create Supabase client with service role key for server-side operations\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n        // Insert the client into the database\n        const { data, error } = await supabase.from(\"clients\").insert(body).select(\"id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link, created_at, updated_at\").single();\n        if (error) {\n            console.error(\"Database error during client creation:\", error);\n            throw error;\n        }\n        console.log(\"Client created in database:\", data);\n        // Queue SharePoint folder creation as background job\n        try {\n            const { queueSharePointFolderCreation } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_background-jobs_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/background-jobs */ \"(rsc)/./src/lib/background-jobs.ts\"));\n            const jobId = queueSharePointFolderCreation(\"client\", data.id, {\n                customId: data.custom_id,\n                name: data.name\n            });\n            console.log(\"SharePoint folder creation queued for client:\", {\n                clientId: data.id,\n                customId: data.custom_id,\n                name: data.name,\n                jobId\n            });\n            data.sharepoint_job_id = jobId;\n        } catch (jobError) {\n            console.error(\"Failed to queue SharePoint folder creation for client:\", {\n                clientId: data.id,\n                customId: data.custom_id,\n                name: data.name,\n                error: jobError instanceof Error ? jobError.message : jobError\n            });\n        // Don't fail the client creation if job queuing fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error in client creation API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : \"Failed to create client\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    try {\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n        const { data, error } = await supabase.from(\"clients\").select(`\n        id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link, created_at, updated_at,\n        contact_persons (*)\n      `).order(\"name\");\n        if (error) throw error;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data || []);\n    } catch (error) {\n        console.error(\"Error fetching clients:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : \"Failed to fetch clients\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/clients/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fclients%2Froute&page=%2Fapi%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclients%2Froute.ts&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();