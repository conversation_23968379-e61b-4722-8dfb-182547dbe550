"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/schedules/route";
exports.ids = ["app/api/schedules/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fschedules%2Froute&page=%2Fapi%2Fschedules%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fschedules%2Froute.ts&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fschedules%2Froute&page=%2Fapi%2Fschedules%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fschedules%2Froute.ts&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_vijayyaso_Documents_Development_cymatics_frontend_src_app_api_schedules_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/schedules/route.ts */ \"(rsc)/./src/app/api/schedules/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/schedules/route\",\n        pathname: \"/api/schedules\",\n        filename: \"route\",\n        bundlePath: \"app/api/schedules/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/api/schedules/route.ts\",\n    nextConfigOutput,\n    userland: _Users_vijayyaso_Documents_Development_cymatics_frontend_src_app_api_schedules_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/schedules/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fschedules%2Froute&page=%2Fapi%2Fschedules%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fschedules%2Froute.ts&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/schedules/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/schedules/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _lib_auth_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-server */ \"(rsc)/./src/lib/auth-server.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(rsc)/./src/lib/api.ts\");\n/* harmony import */ var _lib_server_tasks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/server-tasks */ \"(rsc)/./src/lib/server-tasks.ts\");\n\n\n\n\n\nconst supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nasync function GET(request) {\n    try {\n        // Use service role client for server-side operations (matching projects API pattern)\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_4__.createClient)(supabaseUrl, supabaseServiceKey);\n        // Fetch schedules with project and client information\n        const { data: schedules, error } = await supabase.from(\"schedules\").select(`\n        *,\n        project:projects(\n          *,\n          client:clients(name)\n        ),\n        pilot:users(id, name, email, role),\n        vendors:schedule_vendors(\n          id,\n          cost,\n          notes,\n          vendor:outsourcing_vendors(id, name, specialization, contact_person, phone, email)\n        )\n      `).order(\"scheduled_date\", {\n            ascending: false\n        });\n        if (error) {\n            console.error(\"Error fetching schedules:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch schedules\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(schedules || []);\n    } catch (error) {\n        console.error(\"API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        // Check authentication\n        const supabaseAuth = await (0,_lib_auth_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabaseClient)();\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { schedule, vendors } = await request.json();\n        if (!schedule) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Schedule data is required\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"\\uD83D\\uDE80 Creating schedule via API endpoint:\", {\n            project_id: schedule.project_id,\n            scheduled_date: schedule.scheduled_date,\n            location: schedule.location,\n            schedule_type: schedule.schedule_type,\n            user_id: user.id\n        });\n        // Use service role client for server-side operations\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_4__.createClient)(supabaseUrl, supabaseServiceKey);\n        // Normalize vendors payload\n        const normalizedVendors = Array.isArray(vendors) ? vendors.map((v)=>({\n                vendor_id: String(v.vendor_id),\n                cost: Number(v.cost) || 0,\n                notes: v.notes || \"\"\n            })) : [];\n        // Call the PostgreSQL function to create schedule with vendors\n        const { data, error } = await supabase.rpc(\"create_schedule_with_vendors\", {\n            p_project_id: schedule.project_id,\n            p_scheduled_date: schedule.scheduled_date,\n            p_scheduled_end_date: schedule.scheduled_end_date,\n            p_pilot_id: schedule.pilot_id,\n            p_amount: schedule.amount,\n            p_location: schedule.location,\n            p_google_maps_link: schedule.google_maps_link,\n            p_notes: schedule.notes,\n            p_is_recurring: schedule.is_recurring,\n            p_recurring_pattern: schedule.recurring_pattern,\n            p_is_outsourced: !!schedule.is_outsourced,\n            p_schedule_type: schedule.schedule_type,\n            p_has_gst: schedule.has_gst || false,\n            p_gst_amount: schedule.gst_amount || 0,\n            p_total_amount: schedule.total_amount || schedule.amount || 0,\n            p_vendors: normalizedVendors\n        });\n        if (error) {\n            console.error(\"❌ Error creating schedule:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message\n            }, {\n                status: 500\n            });\n        }\n        console.log(`✅ Schedule created: ${data.custom_id} (ID: ${data.id})`);\n        // Recalculate project total\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.projectsApi.recalculateProjectTotal(schedule.project_id);\n        } catch (projectError) {\n            console.error(\"⚠️ Failed to recalculate project total:\", projectError);\n        // Don't fail the schedule creation if project total calculation fails\n        }\n        // Create default tasks with due dates based on schedule date\n        try {\n            const { data: project } = await supabase.from(\"projects\").select(`\n          id,\n          client_id,\n          clients!inner (\n            client_type\n          )\n        `).eq(\"id\", schedule.project_id).single();\n            if (project?.clients?.client_type) {\n                console.log(\"Creating default tasks for new schedule:\", {\n                    projectId: schedule.project_id,\n                    clientType: project.clients.client_type,\n                    scheduleDate: schedule.scheduled_date,\n                    scheduleId: data.id\n                });\n                // Create tasks directly using service role client instead of projectsApi\n                await (0,_lib_server_tasks__WEBPACK_IMPORTED_MODULE_3__.createDefaultTasksServerSide)(schedule.project_id, project.clients.client_type, schedule.scheduled_date, data.id, data.schedule_type // Pass the schedule type for schedule-based task templates\n                );\n                console.log(\"Default tasks created successfully for schedule:\", data.id);\n            } else {\n                console.log(\"No client type found, skipping task creation for schedule:\", data.id);\n            }\n        } catch (taskError) {\n            console.error(\"⚠️ Failed to create default tasks for schedule:\", taskError);\n            console.error(\"Task creation error details:\", {\n                message: taskError instanceof Error ? taskError.message : \"Unknown error\",\n                stack: taskError instanceof Error ? taskError.stack : undefined\n            });\n        // Don't fail the schedule creation if task creation fails\n        }\n        // Queue SharePoint folder creation as background job for fast response\n        try {\n            console.log(\"Queuing SharePoint folder creation for schedule:\", data.id);\n            // Ensure background job system is initialized\n            const { initializeBackgroundJobs } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_init-background-jobs_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/init-background-jobs */ \"(rsc)/./src/lib/init-background-jobs.ts\"));\n            initializeBackgroundJobs();\n            const { queueSharePointFolderCreation } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_background-jobs_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/background-jobs */ \"(rsc)/./src/lib/background-jobs.ts\"));\n            // Always use background processing for fast form submission\n            const jobId = await queueSharePointFolderCreation(\"schedule\", data.id, {\n                scheduleId: data.id,\n                customId: data.custom_id,\n                scheduledDate: schedule.scheduled_date\n            }, {\n                executeSync: false,\n                fallbackToSync: false // Don't fallback to sync to keep it fast\n            });\n            console.log(\"SharePoint folder creation queued for schedule:\", {\n                scheduleId: data.id,\n                customId: data.custom_id,\n                jobId,\n                executionType: \"background\"\n            });\n        } catch (jobError) {\n            console.error(\"⚠️ Failed to queue SharePoint folder creation for schedule:\", jobError);\n        // Don't fail the schedule creation if SharePoint folder creation fails\n        // This ensures the user can still create schedules even if SharePoint is down\n        }\n        // Return the created schedule\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Error in schedule creation API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : \"Failed to create schedule\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/schedules/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityLogsApi: () => (/* binding */ activityLogsApi),\n/* harmony export */   clientsApi: () => (/* binding */ clientsApi),\n/* harmony export */   contactPersonsApi: () => (/* binding */ contactPersonsApi),\n/* harmony export */   dashboardApi: () => (/* binding */ dashboardApi),\n/* harmony export */   expensesApi: () => (/* binding */ expensesApi),\n/* harmony export */   paymentsApi: () => (/* binding */ paymentsApi),\n/* harmony export */   peekNextId: () => (/* binding */ peekNextId),\n/* harmony export */   previewNextVendorId: () => (/* binding */ previewNextVendorId),\n/* harmony export */   projectsApi: () => (/* binding */ projectsApi),\n/* harmony export */   schedulesApi: () => (/* binding */ schedulesApi),\n/* harmony export */   shootsApi: () => (/* binding */ shootsApi),\n/* harmony export */   tasksApi: () => (/* binding */ tasksApi),\n/* harmony export */   usersApi: () => (/* binding */ usersApi),\n/* harmony export */   vendorsApi: () => (/* binding */ vendorsApi)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _project_calculations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./project-calculations */ \"(rsc)/./src/lib/project-calculations.ts\");\n/* harmony import */ var _lib_cache__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/cache */ \"(rsc)/./src/lib/cache.ts\");\n/* harmony import */ var _lib_performance__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/performance */ \"(rsc)/./src/lib/performance.ts\");\n\n\n\n\nconst supabase = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.createClientSupabaseClient)();\n// Preview next structured ID for create-mode UX only\nasync function peekNextId(entity) {\n    try {\n        const { data, error } = await supabase.rpc(\"peek_next_entity_id\", {\n            entity_type: entity\n        });\n        if (error) {\n            console.warn(\"peekNextId RPC failed:\", error);\n            return null;\n        }\n        // data is expected to be text; normalize to string or null\n        return data ?? null;\n    } catch (e) {\n        console.warn(\"peekNextId unexpected error:\", e);\n        return null;\n    }\n}\n// Convenience helper specifically for Outsourcing Vendors (CYMOU series)\nasync function previewNextVendorId() {\n    return peekNextId(\"vendor\");\n}\n// Users API\nconst usersApi = {\n    async getAll () {\n        const { data, error } = await supabase.from(\"users\").select(\"*\").order(\"name\");\n        if (error) throw error;\n        return data || [];\n    },\n    async getByRole (role) {\n        const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"role\", role).order(\"name\");\n        if (error) throw error;\n        return data || [];\n    },\n    async getUsersByRoles () {\n        const { data, error } = await supabase.from(\"users\").select(\"*\").in(\"role\", [\n            \"pilot\",\n            \"editor\",\n            \"manager\",\n            \"admin\"\n        ]).order(\"name\");\n        if (error) throw error;\n        const usersByRole = {\n            pilot: [],\n            editor: [],\n            admin: []\n        };\n        data?.forEach((user)=>{\n            if (user.role === \"pilot\") {\n                usersByRole.pilot.push(user);\n            } else if (user.role === \"editor\") {\n                usersByRole.editor.push(user);\n            } else if (user.role === \"manager\" || user.role === \"admin\") {\n                usersByRole.admin.push(user);\n            }\n        });\n        return usersByRole;\n    },\n    async create (userData) {\n        // Use the server-side API endpoint for user creation\n        const response = await fetch(\"/api/users\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || \"Failed to create user\");\n        }\n        const result = await response.json();\n        return result.data;\n    },\n    async update (id, updates) {\n        const { data, error } = await supabase.from(\"users\").update(updates).eq(\"id\", id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async delete (id) {\n        // First delete from users table\n        const { error: userError } = await supabase.from(\"users\").delete().eq(\"id\", id);\n        if (userError) throw userError;\n        // Then delete from Supabase Auth\n        const { error: authError } = await supabase.auth.admin.deleteUser(id);\n        if (authError) {\n            console.warn(\"Failed to delete user from auth:\", authError);\n        // Don't throw here as the user record is already deleted\n        }\n    }\n};\n// Clients API\nconst clientsApi = {\n    async getAll () {\n        const response = await fetch(\"/api/clients\");\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch clients\");\n        }\n        const data = await response.json();\n        return data || [];\n    },\n    async getById (id) {\n        const { data, error } = await supabase.from(\"clients\").select(`\n        id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, created_at, updated_at,\n        contact_persons (*)\n      `).eq(\"id\", id).single();\n        if (error) throw error;\n        return data;\n    },\n    async create (client) {\n        const response = await fetch(\"/api/clients\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(client)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || \"Failed to create client\");\n        }\n        const data = await response.json();\n        return data;\n    },\n    async update (id, updates) {\n        const { data, error } = await supabase.from(\"clients\").update(updates).eq(\"id\", id).select(\"id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, created_at, updated_at\").single();\n        if (error) throw error;\n        return data;\n    },\n    async delete (id) {\n        console.log(\"Deleting client via API endpoint:\", id);\n        const response = await fetch(`/api/clients/${id}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error(\"Delete client API error:\", errorData);\n            throw new Error(errorData.error || \"Failed to delete client\");\n        }\n        const result = await response.json();\n        console.log(\"Client deleted successfully:\", result);\n    }\n};\n// Contact Persons API\nconst contactPersonsApi = {\n    async getByClientId (clientId) {\n        const { data, error } = await supabase.from(\"contact_persons\").select(\"*\").eq(\"client_id\", clientId).order(\"is_primary\", {\n            ascending: false\n        }).order(\"name\");\n        if (error) throw error;\n        return data || [];\n    },\n    async create (contactPerson) {\n        const { data, error } = await supabase.from(\"contact_persons\").insert(contactPerson).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async update (id, contactPerson) {\n        const { data, error } = await supabase.from(\"contact_persons\").update(contactPerson).eq(\"id\", id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async delete (id) {\n        const { error } = await supabase.from(\"contact_persons\").delete().eq(\"id\", id);\n        if (error) throw error;\n    },\n    async setPrimary (id, clientId) {\n        // First, unset all primary contacts for this client\n        await supabase.from(\"contact_persons\").update({\n            is_primary: false\n        }).eq(\"client_id\", clientId);\n        // Then set the specified contact as primary\n        const { error } = await supabase.from(\"contact_persons\").update({\n            is_primary: true\n        }).eq(\"id\", id);\n        if (error) throw error;\n    }\n};\n// Projects API\nconst projectsApi = {\n    async getAll () {\n        // Check cache first\n        const cacheKey = _lib_cache__WEBPACK_IMPORTED_MODULE_2__.cacheKeys.projects();\n        const cached = _lib_cache__WEBPACK_IMPORTED_MODULE_2__.apiCache.get(cacheKey);\n        if (cached) return cached;\n        const { data, error } = await (0,_lib_performance__WEBPACK_IMPORTED_MODULE_3__.measureApiCall)(\"projects.getAll\", async ()=>await supabase.from(\"projects\").select(`\n          id, custom_id, name, description, client_id, location, status, total_amount, amount_received, amount_pending, vendor_payment_status, created_at, updated_at,\n          client:clients(id, custom_id, name, client_type),\n          contact_person:contact_persons(id, name, phone, email),\n          schedules:schedules(count)\n        `).order(\"created_at\", {\n                ascending: false\n            }).limit(50) // Reduced limit for better performance\n        );\n        if (error) throw error;\n        const projects = data || [];\n        // Cache for 5 minutes (increased from 2 minutes)\n        _lib_cache__WEBPACK_IMPORTED_MODULE_2__.apiCache.set(cacheKey, projects, 5 * 60 * 1000);\n        return projects;\n    },\n    async getById (id) {\n        const { data, error } = await supabase.from(\"projects\").select(`\n        id, custom_id, name, description, client_id, location, google_maps_link, status, total_amount, gst_inclusive, amount_received, amount_pending, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date, vendor_payment_date, vendor_payment_notes, created_at, updated_at,\n        client:clients(*),\n        contact_person:contact_persons(*),\n        schedules:schedules(*),\n        payments:payments(*),\n        tasks:tasks(*)\n      `).eq(\"id\", id).single();\n        if (error) throw error;\n        return data;\n    },\n    async create (project) {\n        // Use server-side API endpoint for project creation with SharePoint integration\n        const response = await fetch(\"/api/projects\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(project)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || \"Failed to create project\");\n        }\n        const data = await response.json();\n        // Auto-generate project-level default tasks based on client type\n        // First, get the client data to determine client type\n        const { data: clientData, error: clientError } = await supabase.from(\"clients\").select(\"client_type\").eq(\"id\", data.client_id).single();\n        if (!clientError && clientData && clientData.client_type) {\n            try {\n                console.log(\"Creating project-level default tasks for new project:\", {\n                    projectId: data.id,\n                    clientType: clientData.client_type\n                });\n                // Call without shootId to create only project-level tasks\n                await this.createDefaultTasks(data.id, clientData.client_type, undefined, undefined);\n                console.log(\"Project-level default tasks created successfully for project:\", data.id);\n            } catch (taskError) {\n                console.error(\"Failed to create default tasks:\", taskError);\n            // Don't fail the project creation if task creation fails\n            }\n        }\n        // Invalidate cache\n        _lib_cache__WEBPACK_IMPORTED_MODULE_2__.invalidateCache.projects();\n        return data;\n    },\n    async createDefaultTasks (projectId, clientType, shootDate, shootId) {\n        console.log(\"=== createDefaultTasks START ===\");\n        console.log(\"Parameters:\", {\n            projectId,\n            clientType,\n            shootDate,\n            shootId\n        });\n        console.log(\"Task type:\", shootId ? \"SHOOT-BASED TASKS\" : \"PROJECT-LEVEL TASKS\");\n        try {\n            const { getDefaultTasksForClientType, createTasksFromTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/default-tasks */ \"(rsc)/./src/lib/default-tasks.ts\"));\n            // Validate inputs\n            if (!projectId || !clientType) {\n                console.error(\"❌ VALIDATION ERROR: Missing required parameters:\", {\n                    projectId,\n                    clientType\n                });\n                throw new Error(\"Project ID and client type are required\");\n            }\n            // Validate projectId format (should be UUID)\n            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;\n            if (!uuidRegex.test(projectId)) {\n                console.error(\"❌ VALIDATION ERROR: Invalid project ID format:\", projectId);\n                throw new Error(\"Invalid project ID format\");\n            }\n            console.log(\"✅ Input validation passed\");\n            // Check if tasks already exist to prevent duplicates\n            const existingTasksQuery = supabase.from(\"tasks\").select(\"id, title, shoot_id\").eq(\"project_id\", projectId);\n            // If shootId is provided, check for shoot-specific tasks\n            if (shootId) {\n                existingTasksQuery.eq(\"shoot_id\", shootId);\n            } else {\n                // Check for project-level tasks (no shoot_id)\n                existingTasksQuery.is(\"shoot_id\", null);\n            }\n            const { data: existingTasks, error: existingTasksError } = await existingTasksQuery;\n            if (existingTasksError) {\n                console.error(\"Error checking existing tasks:\", existingTasksError);\n                throw existingTasksError;\n            }\n            if (existingTasks && existingTasks.length > 0) {\n                console.log(`Tasks already exist for ${shootId ? \"shoot \" + shootId : \"project \" + projectId}:`);\n                existingTasks.forEach((task)=>{\n                    console.log(`  - ${task.title} (${task.shoot_id ? \"shoot task\" : \"project task\"})`);\n                });\n                console.log(\"=== createDefaultTasks END (SKIPPED - DUPLICATES) ===\");\n                return; // Don't create duplicate tasks\n            }\n            // Get default task templates for this client type\n            const templates = getDefaultTasksForClientType(clientType);\n            console.log('Templates found for client type \"' + clientType + '\":', templates.length, \"templates\");\n            if (templates.length === 0) {\n                console.log(\"No templates found for client type:\", clientType);\n                console.log(\"Available client types in templates:\", Object.keys((await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/default-tasks */ \"(rsc)/./src/lib/default-tasks.ts\"))).DEFAULT_TASK_TEMPLATES));\n                return;\n            }\n            // Get users by role for task assignment\n            const usersByRole = await usersApi.getUsersByRoles();\n            console.log(\"Users by role:\", usersByRole);\n            // Create a mapping of role to first available user ID\n            const roleToUserId = {};\n            if (usersByRole.pilot.length > 0) roleToUserId.pilot = usersByRole.pilot[0].id;\n            if (usersByRole.editor.length > 0) roleToUserId.editor = usersByRole.editor[0].id;\n            if (usersByRole.admin.length > 0) roleToUserId.admin = usersByRole.admin[0].id;\n            // Fallback: if no specific role users exist, use admin for all tasks\n            const fallbackUserId = usersByRole.admin.length > 0 ? usersByRole.admin[0].id : \"\";\n            if (!roleToUserId.pilot && fallbackUserId) roleToUserId.pilot = fallbackUserId;\n            if (!roleToUserId.editor && fallbackUserId) roleToUserId.editor = fallbackUserId;\n            if (!roleToUserId.admin && fallbackUserId) roleToUserId.admin = fallbackUserId;\n            // Ultimate fallback: if no role-specific users exist, use the current authenticated user\n            if (!fallbackUserId) {\n                console.log(\"No role-specific users found, trying to use current user\");\n                try {\n                    const { data: { user: currentUser } } = await supabase.auth.getUser();\n                    if (currentUser) {\n                        console.log(\"Using current user as fallback:\", currentUser.id);\n                        roleToUserId.pilot = currentUser.id;\n                        roleToUserId.editor = currentUser.id;\n                        roleToUserId.admin = currentUser.id;\n                    } else {\n                        console.log(\"No authenticated user found, cannot create tasks\");\n                        return;\n                    }\n                } catch (authError) {\n                    console.error(\"Error getting current user:\", authError);\n                    return;\n                }\n            }\n            console.log(\"Role to user mapping:\", roleToUserId);\n            // Separate shoot-based and project-level tasks\n            const shootBasedTemplates = templates.filter((t)=>!t.isProjectTask);\n            const projectLevelTemplates = templates.filter((t)=>t.isProjectTask);\n            let createdCount = 0;\n            let skippedCount = 0;\n            // Create shoot-based tasks if shootId is provided\n            if (shootId && shootBasedTemplates.length > 0) {\n                console.log(\"Creating shoot-based tasks for shoot:\", shootId);\n                const shootTaskForms = createTasksFromTemplates(shootBasedTemplates, projectId, roleToUserId, shootDate, shootId);\n                for (const taskForm of shootTaskForms){\n                    if (taskForm.assigned_to) {\n                        console.log(\"Creating shoot task:\", {\n                            title: taskForm.title,\n                            assigned_to: taskForm.assigned_to,\n                            project_id: taskForm.project_id,\n                            shoot_id: taskForm.shoot_id\n                        });\n                        try {\n                            const createdTask = await tasksApi.create(taskForm);\n                            console.log(\"Shoot task created successfully:\", createdTask.id, createdTask.title);\n                            createdCount++;\n                        } catch (taskCreateError) {\n                            console.error(\"Failed to create shoot task:\", taskCreateError);\n                            throw taskCreateError;\n                        }\n                    } else {\n                        console.log(\"Skipping shoot task without assigned user:\", taskForm.title);\n                        skippedCount++;\n                    }\n                }\n            }\n            // Create project-level tasks only if they don't already exist and only when called without shootId\n            if (projectLevelTemplates.length > 0 && !shootId) {\n                // Check if project-level tasks already exist\n                const { data: existingProjectTasks } = await supabase.from(\"tasks\").select(\"id, title\").eq(\"project_id\", projectId).is(\"shoot_id\", null);\n                if (!existingProjectTasks || existingProjectTasks.length === 0) {\n                    console.log(\"Creating project-level tasks for project:\", projectId);\n                    const projectTaskForms = createTasksFromTemplates(projectLevelTemplates, projectId, roleToUserId, shootDate, undefined);\n                    for (const taskForm of projectTaskForms){\n                        if (taskForm.assigned_to) {\n                            console.log(\"Creating project task:\", {\n                                title: taskForm.title,\n                                assigned_to: taskForm.assigned_to,\n                                project_id: taskForm.project_id,\n                                shoot_id: taskForm.shoot_id\n                            });\n                            try {\n                                const createdTask = await tasksApi.create(taskForm);\n                                console.log(\"Project task created successfully:\", createdTask.id, createdTask.title);\n                                createdCount++;\n                            } catch (taskCreateError) {\n                                console.error(\"Failed to create project task:\", taskCreateError);\n                                throw taskCreateError;\n                            }\n                        } else {\n                            console.log(\"Skipping project task without assigned user:\", taskForm.title);\n                            skippedCount++;\n                        }\n                    }\n                } else {\n                    console.log(\"Project-level tasks already exist, skipping creation\");\n                }\n            } else if (projectLevelTemplates.length > 0 && shootId) {\n                console.log(\"Skipping project-level task creation during shoot creation - they should already exist from project creation\");\n            }\n            console.log(`=== createDefaultTasks END ===`);\n            console.log(`Summary: ${createdCount} tasks created, ${skippedCount} tasks skipped`);\n            console.log(`Task type: ${shootId ? \"SHOOT-BASED\" : \"PROJECT-LEVEL\"}`);\n            console.log(\"=======================================\");\n        } catch (error) {\n            console.error(\"❌ CRITICAL ERROR in createDefaultTasks:\", error);\n            console.error(\"Parameters that caused the error:\", {\n                projectId,\n                clientType,\n                shootDate,\n                shootId\n            });\n            console.error(\"Error details:\", {\n                message: error instanceof Error ? error.message : \"Unknown error\",\n                stack: error instanceof Error ? error.stack : undefined\n            });\n            console.log(\"=== createDefaultTasks END (ERROR) ===\");\n            throw error;\n        }\n    },\n    async cleanupDuplicateTasks (projectId) {\n        console.log(\"=== cleanupDuplicateTasks START ===\");\n        console.log(\"Project ID:\", projectId);\n        try {\n            // Get all tasks for the project\n            const { data: allTasks, error } = await supabase.from(\"tasks\").select(\"id, title, shoot_id, created_at\").eq(\"project_id\", projectId).order(\"created_at\", {\n                ascending: true\n            });\n            if (error) {\n                console.error(\"Error fetching tasks:\", error);\n                throw error;\n            }\n            if (!allTasks || allTasks.length === 0) {\n                console.log(\"No tasks found for project\");\n                return;\n            }\n            // Group tasks by title and shoot_id to find duplicates\n            const taskGroups = {};\n            allTasks.forEach((task)=>{\n                const key = `${task.title}_${task.shoot_id || \"project\"}`;\n                if (!taskGroups[key]) {\n                    taskGroups[key] = [];\n                }\n                taskGroups[key].push(task);\n            });\n            // Find and remove duplicates (keep the oldest one)\n            let deletedCount = 0;\n            for (const [key, tasks] of Object.entries(taskGroups)){\n                if (tasks.length > 1) {\n                    console.log(`Found ${tasks.length} duplicates for: ${key}`);\n                    // Keep the first (oldest) task, delete the rest\n                    const tasksToDelete = tasks.slice(1);\n                    for (const task of tasksToDelete){\n                        console.log(`Deleting duplicate task: ${task.title} (${task.id})`);\n                        const { error: deleteError } = await supabase.from(\"tasks\").delete().eq(\"id\", task.id);\n                        if (deleteError) {\n                            console.error(\"Error deleting duplicate task:\", deleteError);\n                        } else {\n                            deletedCount++;\n                        }\n                    }\n                }\n            }\n            console.log(`=== cleanupDuplicateTasks END ===`);\n            console.log(`Summary: ${deletedCount} duplicate tasks removed`);\n        } catch (error) {\n            console.error(\"Error in cleanupDuplicateTasks:\", error);\n            throw error;\n        }\n    },\n    async fixTaskOrder () {\n        console.log(\"=== fixTaskOrder START ===\");\n        try {\n            // Get all tasks\n            const { data: allTasks, error } = await supabase.from(\"tasks\").select(\"id, title, shoot_id, project_id\");\n            if (error) {\n                console.error(\"Error fetching tasks:\", error);\n                throw error;\n            }\n            if (!allTasks || allTasks.length === 0) {\n                console.log(\"No tasks found\");\n                return;\n            }\n            console.log(`Found ${allTasks.length} tasks to fix`);\n            // Define task order mapping\n            const taskOrderMap = {\n                \"Plan Flight\": 1,\n                \"Mark GCPs\": 2,\n                \"Script Confirmation\": 1,\n                \"Shoot\": 2,\n                \"File Upload\": 3,\n                \"File Backup\": 4,\n                \"Edit\": 5,\n                \"Post-Processing\": 6,\n                \"Deliver Files\": 100,\n                \"Payment Collect\": 101\n            };\n            let updatedCount = 0;\n            // Update tasks in batches\n            for (const task of allTasks){\n                let newOrder = taskOrderMap[task.title];\n                if (!newOrder) {\n                    console.log(`Unknown task title: ${task.title}, skipping`);\n                    continue;\n                }\n                // Adjust order for tasks that come after Script Confirmation\n                if (task.shoot_id && [\n                    \"Shoot\",\n                    \"File Upload\",\n                    \"File Backup\",\n                    \"Edit\"\n                ].includes(task.title)) {\n                    // Check if this project has Script Confirmation\n                    const hasScriptConfirmation = allTasks.some((t)=>t.project_id === task.project_id && t.title === \"Script Confirmation\");\n                    if (hasScriptConfirmation) {\n                        newOrder += 1 // Shift by 1 if Script Confirmation exists\n                        ;\n                    }\n                }\n                // Update the task\n                const { error: updateError } = await supabase.from(\"tasks\").update({\n                    order: newOrder\n                }).eq(\"id\", task.id);\n                if (updateError) {\n                    console.error(`Error updating task ${task.id}:`, updateError);\n                } else {\n                    console.log(`Updated task \"${task.title}\" to order ${newOrder}`);\n                    updatedCount++;\n                }\n            }\n            console.log(`=== fixTaskOrder END ===`);\n            console.log(`Summary: ${updatedCount} tasks updated`);\n        } catch (error) {\n            console.error(\"Error in fixTaskOrder:\", error);\n            throw error;\n        }\n    },\n    async completeShootTasks (shootId) {\n        console.log(\"=== completeShootTasks START ===\");\n        console.log(\"Shoot ID:\", shootId);\n        try {\n            // Get all shoot-based tasks for this shoot that are not already completed\n            const { data: shootTasks, error } = await supabase.from(\"tasks\").select(\"id, title, status\").eq(\"shoot_id\", shootId).neq(\"status\", \"completed\").neq(\"status\", \"cancelled\");\n            if (error) {\n                console.error(\"Error fetching shoot tasks:\", error);\n                throw error;\n            }\n            if (!shootTasks || shootTasks.length === 0) {\n                console.log(\"No incomplete shoot tasks found\");\n                return;\n            }\n            console.log(`Found ${shootTasks.length} tasks to complete:`, shootTasks.map((t)=>t.title));\n            // Complete all shoot tasks\n            const { error: updateError } = await supabase.from(\"tasks\").update({\n                status: \"completed\",\n                completed_at: new Date().toISOString()\n            }).eq(\"shoot_id\", shootId).neq(\"status\", \"completed\").neq(\"status\", \"cancelled\");\n            if (updateError) {\n                console.error(\"Error completing shoot tasks:\", updateError);\n                throw updateError;\n            }\n            console.log(`Successfully completed ${shootTasks.length} shoot tasks`);\n            console.log(\"=== completeShootTasks END ===\");\n        } catch (error) {\n            console.error(\"Error in completeShootTasks:\", error);\n            throw error;\n        }\n    },\n    async update (id, updates) {\n        const { data, error } = await supabase.from(\"projects\").update(updates).eq(\"id\", id).select(`\n        id, custom_id, name, description, client_id, location, google_maps_link, status, total_amount, gst_inclusive, amount_received, amount_pending, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date, vendor_payment_date, vendor_payment_notes, created_at, updated_at,\n        client:clients(*),\n        contact_person:contact_persons(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    async delete (id) {\n        console.log(\"Deleting project via API endpoint:\", id);\n        const response = await fetch(`/api/projects/${id}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error(\"Delete project API error:\", errorData);\n            throw new Error(errorData.error || \"Failed to delete project\");\n        }\n        const result = await response.json();\n        console.log(\"Project deleted successfully:\", result);\n    },\n    async recalculateProjectTotal (projectId) {\n        // Get project with client and schedules including vendors for accurate outsourcing calculations\n        const { data: project, error: projectError } = await supabase.from(\"projects\").select(`\n        *,\n        client:clients(*),\n        schedules:schedules(\n          *,\n          vendors:schedule_vendors(\n            *,\n            vendor:outsourcing_vendors(*)\n          )\n        )\n      `).eq(\"id\", projectId).single();\n        if (projectError) throw projectError;\n        if (!project) throw new Error(\"Project not found\");\n        // Calculate new amounts\n        const updatedAmounts = (0,_project_calculations__WEBPACK_IMPORTED_MODULE_1__.getUpdatedProjectAmounts)(project.schedules || [], project.client, project.amount_received);\n        // Update project with new amounts\n        const { data: updatedProject, error: updateError } = await supabase.from(\"projects\").update(updatedAmounts).eq(\"id\", projectId).select(`\n        *,\n        client:clients(*),\n        contact_person:contact_persons(*)\n      `).single();\n        if (updateError) throw updateError;\n        return updatedProject;\n    },\n    async updateVendorPaymentStatus (projectId) {\n        // Get project with schedules and expenses\n        const { data: project, error: projectError } = await supabase.from(\"projects\").select(`\n        *,\n        client:clients(*),\n        schedules:schedules(\n          *,\n          vendors:schedule_vendors(\n            *,\n            vendor:outsourcing_vendors(*)\n          )\n        )\n      `).eq(\"id\", projectId).single();\n        if (projectError) throw projectError;\n        if (!project) throw new Error(\"Project not found\");\n        // Get all expenses for this project with category 'outsourcing'\n        const { data: expenses, error: expensesError } = await supabase.from(\"expenses\").select(\"amount, category\").eq(\"project_id\", projectId).eq(\"category\", \"outsourcing\");\n        if (expensesError) throw expensesError;\n        // Calculate outsourcing expenses\n        const outsourcingExpenses = expenses?.reduce((sum, expense)=>sum + expense.amount, 0) || 0;\n        // Calculate vendor payment status using the new function\n        const { calculateVendorPaymentStatus } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./project-calculations */ \"(rsc)/./src/lib/project-calculations.ts\"));\n        const paymentStatus = calculateVendorPaymentStatus(project.schedules || [], outsourcingExpenses);\n        // Determine the status to store in the database\n        let dbStatus = \"pending\";\n        if (paymentStatus.status === \"paid\") {\n            dbStatus = \"paid\";\n        } else if (paymentStatus.status === \"partial\") {\n            dbStatus = \"pending\" // Partial payments are still considered pending\n            ;\n        }\n        // Update project with new vendor payment status\n        const { data: updatedProject, error: updateError } = await supabase.from(\"projects\").update({\n            vendor_payment_status: dbStatus,\n            vendor_payment_amount: paymentStatus.totalPaid\n        }).eq(\"id\", projectId).select(`\n        *,\n        client:clients(*),\n        contact_person:contact_persons(*)\n      `).single();\n        if (updateError) throw updateError;\n        return updatedProject;\n    }\n};\n// Schedules API (renamed from Shoots)\nconst schedulesApi = {\n    async getAll () {\n        const { data, error } = await supabase.from(\"schedules\").select(`\n        id, custom_id, project_id, scheduled_date, scheduled_end_date, actual_date, status, pilot_id, amount, location, google_maps_link, notes, weather_conditions, onedrive_folder_path, is_recurring, recurring_pattern, is_outsourced, device_used, battery_count, shoot_start_time, shoot_end_time, completion_notes, created_at, updated_at,\n        project:projects(*),\n        pilot:users(*),\n        vendors:schedule_vendors(\n          *,\n          vendor:outsourcing_vendors(*)\n        )\n      `).order(\"scheduled_date\");\n        if (error) throw error;\n        return data || [];\n    },\n    async getById (id) {\n        const { data, error } = await supabase.from(\"schedules\").select(`\n        id, custom_id, project_id, scheduled_date, scheduled_end_date, actual_date, status, pilot_id, amount, location, google_maps_link, notes, weather_conditions, onedrive_folder_path, is_recurring, recurring_pattern, is_outsourced, device_used, battery_count, shoot_start_time, shoot_end_time, completion_notes, created_at, updated_at,\n        project:projects(*),\n        pilot:users(*),\n        vendors:schedule_vendors(\n          *,\n          vendor:outsourcing_vendors(*)\n        )\n      `).eq(\"id\", id).single();\n        if (error) throw error;\n        return data;\n    },\n    async getUpcoming () {\n        const { data, error } = await supabase.from(\"schedules\").select(`\n        *,\n        project:projects(*),\n        pilot:users(*)\n      `).gte(\"scheduled_date\", new Date().toISOString()).eq(\"status\", \"scheduled\").order(\"scheduled_date\").limit(10);\n        if (error) throw error;\n        return data || [];\n    },\n    async create (schedule) {\n        const { data, error } = await supabase.from(\"schedules\").insert(schedule).select(`\n        id, custom_id, project_id, scheduled_date, scheduled_end_date, actual_date, status, pilot_id, amount, location, google_maps_link, notes, weather_conditions, onedrive_folder_path, is_recurring, recurring_pattern, is_outsourced, device_used, battery_count, shoot_start_time, shoot_end_time, completion_notes, created_at, updated_at,\n        project:projects(\n          id, custom_id, name, description, client_id, location, google_maps_link, status, total_amount, gst_inclusive, amount_received, amount_pending, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date, vendor_payment_date, vendor_payment_notes, created_at, updated_at,\n          client:clients(id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, created_at, updated_at)\n        ),\n        pilot:users(id, email, name, role, avatar_url, created_at, updated_at)\n      `).single();\n        if (error) throw error;\n        // Recalculate project total\n        await projectsApi.recalculateProjectTotal(schedule.project_id);\n        // Create default tasks with due dates based on schedule date\n        {\n            const projAny = data.project;\n            const project = Array.isArray(projAny) ? projAny[0] : projAny;\n            const clientAny = project?.client;\n            const client = Array.isArray(clientAny) ? clientAny[0] : clientAny;\n            if (client?.client_type) {\n                try {\n                    console.log(\"Creating default tasks for new schedule:\", {\n                        projectId: data.project_id,\n                        scheduleId: data.id,\n                        clientType: client.client_type,\n                        scheduleDate: data.scheduled_date\n                    });\n                    await projectsApi.createDefaultTasks(data.project_id, client.client_type, data.scheduled_date, data.id // Pass the schedule ID for shoot-based tasks\n                    );\n                    console.log(\"Default tasks created successfully for schedule:\", data.id);\n                } catch (taskError) {\n                    console.error(\"Failed to create default tasks for schedule:\", taskError);\n                // Don't fail the schedule creation if task creation fails\n                }\n            } else {\n                console.log(\"No client type found, skipping task creation for schedule:\", data.id);\n            }\n        }\n        // Queue SharePoint folder creation as background job\n        try {\n            console.log(\"Queuing SharePoint folder creation for schedule:\", data.id);\n            const { queueSharePointFolderCreation } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_background-jobs_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/background-jobs */ \"(rsc)/./src/lib/background-jobs.ts\"));\n            const jobId = queueSharePointFolderCreation(\"schedule\", data.id, {\n                scheduleId: data.id,\n                customId: data.custom_id,\n                scheduledDate: data.scheduled_date\n            });\n            console.log(\"SharePoint folder creation queued for schedule:\", {\n                scheduleId: data.id,\n                customId: data.custom_id,\n                jobId\n            });\n        } catch (jobError) {\n            console.error(\"Failed to queue SharePoint folder creation for schedule:\", jobError);\n        // Don't fail the schedule creation if job queuing fails\n        }\n        return data;\n    },\n    async update (id, updates) {\n        // Use the API endpoint that includes task due date synchronization\n        const response = await fetch(`/api/schedules/${id}`, {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(updates)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || \"Failed to update schedule\");\n        }\n        const data = await response.json();\n        // Recalculate project total\n        try {\n            await projectsApi.recalculateProjectTotal(data.project_id);\n        } catch (error) {\n            console.warn(\"Failed to recalculate project total:\", error);\n        // Don't fail the update if project total calculation fails\n        }\n        return data;\n    },\n    async delete (id) {\n        // Get the schedule to find the project_id before deleting\n        const { data: schedule, error: getError } = await supabase.from(\"schedules\").select(\"project_id\").eq(\"id\", id).single();\n        if (getError) throw getError;\n        const { error } = await supabase.from(\"schedules\").delete().eq(\"id\", id);\n        if (error) throw error;\n        // Recalculate project total\n        if (schedule) {\n            await projectsApi.recalculateProjectTotal(schedule.project_id);\n        }\n    },\n    async createWithVendors (schedule, vendors) {\n        // Normalize vendors payload; DB function inserts vendors solely based on p_vendors array\n        const normalizedVendors = Array.isArray(vendors) ? vendors.map((v)=>({\n                vendor_id: String(v.vendor_id),\n                cost: Number(v.cost) || 0,\n                notes: v.notes || \"\"\n            })) : [];\n        // Call the PostgreSQL function to create schedule with vendors\n        const { data, error } = await supabase.rpc(\"create_schedule_with_vendors\", {\n            p_project_id: schedule.project_id,\n            p_scheduled_date: schedule.scheduled_date,\n            p_scheduled_end_date: schedule.scheduled_end_date,\n            p_pilot_id: schedule.pilot_id,\n            p_amount: schedule.amount,\n            p_location: schedule.location,\n            p_google_maps_link: schedule.google_maps_link,\n            p_notes: schedule.notes,\n            p_is_recurring: schedule.is_recurring,\n            p_recurring_pattern: schedule.recurring_pattern,\n            // p_is_outsourced kept for column, but vendors insert does NOT depend on it\n            p_is_outsourced: !!schedule.is_outsourced,\n            p_schedule_type: schedule.schedule_type,\n            p_vendors: normalizedVendors\n        });\n        if (error) throw error;\n        // Recalculate project total\n        await projectsApi.recalculateProjectTotal(schedule.project_id);\n        // Create default tasks with due dates based on schedule date\n        const project = await projectsApi.getById(schedule.project_id);\n        if (project?.client?.client_type) {\n            try {\n                console.log(\"Creating default tasks for new schedule:\", {\n                    projectId: schedule.project_id,\n                    clientType: project.client.client_type,\n                    scheduleDate: schedule.scheduled_date\n                });\n                await projectsApi.createDefaultTasks(schedule.project_id, project.client.client_type, schedule.scheduled_date, data.id // Pass the schedule ID for shoot-based tasks\n                );\n                console.log(\"Default tasks created successfully for schedule:\", data.id);\n            } catch (taskError) {\n                console.error(\"Failed to create default tasks for schedule:\", taskError);\n            // Don't fail the schedule creation if task creation fails\n            }\n        } else {\n            console.log(\"No client type found, skipping task creation for schedule:\", data.id);\n        }\n        // Queue SharePoint folder creation based on environment configuration\n        try {\n            console.log(\"Processing SharePoint folder creation for schedule:\", data.id);\n            const { queueSharePointFolderCreation } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_background-jobs_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/background-jobs */ \"(rsc)/./src/lib/background-jobs.ts\"));\n            // Determine execution mode from environment variable\n            const mode = process.env.SHAREPOINT_FOLDER_CREATION_MODE || \"background-with-fallback\";\n            let executeSync = false;\n            let fallbackToSync = false;\n            switch(mode){\n                case \"sync\":\n                    executeSync = true;\n                    fallbackToSync = false;\n                    break;\n                case \"background\":\n                    executeSync = false;\n                    fallbackToSync = false;\n                    break;\n                case \"background-with-fallback\":\n                default:\n                    executeSync = false;\n                    fallbackToSync = true;\n                    break;\n            }\n            console.log(`SharePoint folder creation mode: ${mode} (executeSync: ${executeSync}, fallbackToSync: ${fallbackToSync})`);\n            // Execute SharePoint folder creation based on configuration\n            const jobId = await queueSharePointFolderCreation(\"schedule\", data.id, {\n                scheduleId: data.id,\n                customId: data.custom_id,\n                scheduledDate: schedule.scheduled_date\n            }, {\n                executeSync,\n                fallbackToSync\n            });\n            console.log(\"SharePoint folder creation processed for schedule:\", {\n                scheduleId: data.id,\n                customId: data.custom_id,\n                jobId,\n                mode,\n                executionType: jobId.startsWith(\"sync\") ? \"synchronous\" : \"background\"\n            });\n        } catch (jobError) {\n            console.error(\"Failed to process SharePoint folder creation for schedule:\", jobError);\n        // Don't fail the schedule creation if SharePoint folder creation fails\n        // This ensures the user can still create schedules even if SharePoint is down\n        }\n        // Re-fetch full schedule with vendors for immediate UI consumption\n        try {\n            const full = await schedulesApi.getById(data.id);\n            if (full) return full;\n        } catch  {}\n        return data;\n    },\n    async updateWithVendors (id, updates, vendors) {\n        // Align with DB function signature:\n        // public.update_schedule_with_vendors(p_schedule_id uuid, p_updates jsonb, p_vendors jsonb)\n        // Build updates JSON object with fields the function expects\n        const p_updates = {\n            scheduled_date: updates.scheduled_date ?? null,\n            scheduled_end_date: updates.scheduled_end_date ?? null,\n            pilot_id: updates.pilot_id ?? null,\n            amount: updates.amount ?? null,\n            has_gst: updates.has_gst ?? null,\n            gst_amount: updates.gst_amount ?? null,\n            total_amount: updates.total_amount ?? null,\n            location: updates.location ?? null,\n            google_maps_link: updates.google_maps_link ?? null,\n            notes: updates.notes ?? null,\n            is_recurring: updates.is_recurring ?? null,\n            recurring_pattern: updates.recurring_pattern ?? null,\n            is_outsourced: updates.is_outsourced ?? null,\n            schedule_type: updates.schedule_type ?? null\n        };\n        // App-side safeguard: send vendors only if is_outsourced is true, else send empty array.\n        const shouldSendVendors = !!updates.is_outsourced;\n        const normalizedVendors = Array.isArray(vendors) ? vendors : vendors ? [\n            vendors\n        ] : [];\n        const p_vendors = shouldSendVendors ? normalizedVendors : [];\n        const { data, error } = await supabase.rpc(\"update_schedule_with_vendors\", {\n            p_schedule_id: id,\n            p_updates,\n            p_vendors\n        });\n        if (error) throw error;\n        // Recalculate project total\n        await projectsApi.recalculateProjectTotal(updates.project_id || (await this.getById(id))?.project_id || \"\");\n        return data;\n    }\n};\n// Keep shootsApi as alias for backward compatibility\nconst shootsApi = schedulesApi;\n// Expenses API\nconst expensesApi = {\n    async getAll () {\n        const { data, error } = await supabase.from(\"expenses\").select(`\n        *,\n        project:projects(*),\n        user:users(*)\n      `).order(\"date\", {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    async getByProjectId (projectId) {\n        const { data, error } = await supabase.from(\"expenses\").select(`\n        *,\n        project:projects(*),\n        user:users(*)\n      `).eq(\"project_id\", projectId).order(\"date\", {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    async create (expense) {\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) throw new Error(\"Not authenticated\");\n        const { data, error } = await supabase.from(\"expenses\").insert({\n            ...expense,\n            user_id: user.id\n        }).select(`\n        *,\n        project:projects(*),\n        user:users(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    async update (id, updates) {\n        const { data, error } = await supabase.from(\"expenses\").update(updates).eq(\"id\", id).select(`\n        *,\n        project:projects(*),\n        user:users(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    async delete (id) {\n        const { error } = await supabase.from(\"expenses\").delete().eq(\"id\", id);\n        if (error) throw error;\n    }\n};\n// Tasks API\nconst tasksApi = {\n    async getAll () {\n        const { data, error } = await supabase.from(\"tasks\").select(`\n        *,\n        assigned_user:users(*),\n        project:projects(*),\n        shoot:schedules(*)\n      `).order(\"order\", {\n            ascending: true,\n            nullsFirst: false\n        }).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    async getAllWithFilters (filters) {\n        let query = supabase.from(\"tasks\").select(`\n        id, title, description, status, priority, assigned_to, assigned_role, project_id, shoot_id, due_date, created_at, updated_at, started_at, completed_at, order,\n        assigned_user:users(id, name, email, role),\n        project:projects(id, custom_id, name, status),\n        shoot:schedules(id, custom_id, scheduled_date)\n      `);\n        // Apply filters\n        if (filters?.assignedTo) {\n            query = query.eq(\"assigned_to\", filters.assignedTo);\n        }\n        if (filters?.status) {\n            query = query.eq(\"status\", filters.status);\n        }\n        if (filters?.priority) {\n            query = query.eq(\"priority\", filters.priority);\n        }\n        if (filters?.projectId) {\n            query = query.eq(\"project_id\", filters.projectId);\n        }\n        if (filters?.shootId) {\n            query = query.eq(\"shoot_id\", filters.shootId);\n        }\n        // Add pagination with reasonable defaults\n        const limit = filters?.limit || 25 // Reduced default from 100 to 25\n        ;\n        const offset = filters?.offset || 0;\n        query = query.range(offset, offset + limit - 1);\n        const { data, error } = await query.order(\"order\", {\n            ascending: true,\n            nullsFirst: false\n        }).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    async getMyTasks () {\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) throw new Error(\"Not authenticated\");\n        // Get user profile to check role\n        const { data: userProfile, error: profileError } = await supabase.from(\"users\").select(\"role\").eq(\"id\", user.id).single();\n        if (profileError) throw profileError;\n        // Admin and Manager can see all tasks, others see only assigned tasks\n        const isAdminOrManager = userProfile && (userProfile.role === \"admin\" || userProfile.role === \"manager\");\n        const query = supabase.from(\"tasks\").select(`\n        *,\n        assigned_user:users(*),\n        project:projects(*),\n        shoot:schedules(*)\n      `);\n        // Apply filter based on role\n        if (!isAdminOrManager) {\n            query.eq(\"assigned_to\", user.id);\n        }\n        const { data, error } = await query.order(\"order\", {\n            ascending: true,\n            nullsFirst: false\n        }).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    async create (task) {\n        console.log(\"Creating task via API endpoint:\", task.title);\n        const response = await fetch(\"/api/tasks\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(task)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error(\"Create task API error:\", errorData);\n            throw new Error(errorData.error || \"Failed to create task\");\n        }\n        const result = await response.json();\n        console.log(\"Task created successfully:\", result.title, result.id);\n        return result;\n    },\n    async update (id, updates) {\n        // Get the current task data first\n        const { data: currentTask, error: fetchError } = await supabase.from(\"tasks\").select(`\n        *,\n        assigned_user:users(*),\n        project:projects(*),\n        shoot:schedules(*)\n      `).eq(\"id\", id).single();\n        if (fetchError) throw fetchError;\n        // Add timestamp updates based on status changes\n        const finalUpdates = {\n            ...updates\n        };\n        if (updates.status && currentTask && updates.status !== currentTask.status) {\n            if (updates.status === \"in_progress\" && currentTask.status === \"pending\") {\n                finalUpdates.started_at = new Date().toISOString();\n            } else if (updates.status === \"completed\" && currentTask.status === \"in_progress\") {\n                finalUpdates.completed_at = new Date().toISOString();\n            }\n        }\n        const { data, error } = await supabase.from(\"tasks\").update(finalUpdates).eq(\"id\", id).select(`\n        *,\n        assigned_user:users(*),\n        project:projects(*),\n        shoot:schedules(*)\n      `).single();\n        if (error) throw error;\n        // Handle automatic status completion if status changed\n        if (updates.status && currentTask && updates.status !== currentTask.status) {\n            try {\n                const { handleTaskStatusChange } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_status-completion_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/status-completion */ \"(rsc)/./src/lib/status-completion.ts\"));\n                await handleTaskStatusChange(data, updates.status);\n            } catch (statusError) {\n                console.error(\"Error handling task status change:\", statusError);\n            // Don't fail the task update if status completion fails\n            }\n        }\n        return data;\n    },\n    async delete (id) {\n        console.log(\"Deleting task via API endpoint:\", id);\n        const response = await fetch(`/api/tasks/${id}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error(\"Delete task API error:\", errorData);\n            throw new Error(errorData.error || \"Failed to delete task\");\n        }\n        const result = await response.json();\n        console.log(\"Task deleted successfully:\", result);\n    },\n    async assignTask (taskId, userId) {\n        // Check if current user has permission to assign tasks\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) throw new Error(\"Not authenticated\");\n        const { data: userProfile, error: profileError } = await supabase.from(\"users\").select(\"role\").eq(\"id\", user.id).single();\n        if (profileError) throw profileError;\n        const isAdminOrManager = userProfile && (userProfile.role === \"admin\" || userProfile.role === \"manager\");\n        if (!isAdminOrManager) {\n            throw new Error(\"Only admin and manager users can assign tasks\");\n        }\n        // Update the task assignment\n        const { data, error } = await supabase.from(\"tasks\").update({\n            assigned_to: userId\n        }).eq(\"id\", taskId).select(`\n        *,\n        assigned_user:users(*),\n        project:projects(*),\n        shoot:schedules(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    async bulkAssignTasks (taskIds, userId) {\n        // Check if current user has permission to assign tasks\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) throw new Error(\"Not authenticated\");\n        const { data: userProfile, error: profileError } = await supabase.from(\"users\").select(\"role\").eq(\"id\", user.id).single();\n        if (profileError) throw profileError;\n        const isAdminOrManager = userProfile && (userProfile.role === \"admin\" || userProfile.role === \"manager\");\n        if (!isAdminOrManager) {\n            throw new Error(\"Only admin and manager users can assign tasks\");\n        }\n        // Update multiple tasks\n        const { error } = await supabase.from(\"tasks\").update({\n            assigned_to: userId\n        }).in(\"id\", taskIds);\n        if (error) throw error;\n    },\n    async updateAssignment (id, assignedTo) {\n        const { data, error } = await supabase.from(\"tasks\").update({\n            assigned_to: assignedTo\n        }).eq(\"id\", id).select(`\n        *,\n        assigned_user:users(*),\n        project:projects(*),\n        shoot:schedules(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    async updateDueDate (id, dueDate) {\n        const { data, error } = await supabase.from(\"tasks\").update({\n            due_date: dueDate\n        }).eq(\"id\", id).select(`\n        *,\n        assigned_user:users(*),\n        project:projects(*),\n        shoot:schedules(*)\n      `).single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Vendors API\nconst vendorsApi = {\n    async getAll () {\n        const { data, error } = await supabase.from(\"outsourcing_vendors\").select(`\n        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at\n      `).order(\"name\");\n        if (error) throw error;\n        return data || [];\n    },\n    async getActive () {\n        const { data, error } = await supabase.from(\"outsourcing_vendors\").select(`\n        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at\n      `).eq(\"is_active\", true).order(\"name\");\n        if (error) throw error;\n        return data || [];\n    },\n    async getById (id) {\n        const { data, error } = await supabase.from(\"outsourcing_vendors\").select(`\n        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at\n      `).eq(\"id\", id).single();\n        if (error) throw error;\n        return data;\n    },\n    async create (vendor) {\n        const { data, error } = await supabase.from(\"outsourcing_vendors\").insert(vendor).select(`\n        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    async update (id, updates) {\n        const { data, error } = await supabase.from(\"outsourcing_vendors\").update(updates).eq(\"id\", id).select(`\n        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at\n      `).single();\n        if (error) throw error;\n        return data;\n    },\n    async delete (id) {\n        const { error } = await supabase.from(\"outsourcing_vendors\").delete().eq(\"id\", id);\n        if (error) throw error;\n    }\n};\n// Payments API\nconst paymentsApi = {\n    async getAll () {\n        const { data, error } = await supabase.from(\"payments\").select(`\n        *,\n        project:projects(*)\n      `).order(\"payment_date\", {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    },\n    async create (payment) {\n        const { data, error } = await supabase.from(\"payments\").insert(payment).select(`\n        *,\n        project:projects(*)\n      `).single();\n        if (error) throw error;\n        // Update project's amount_received and recalculate totals\n        await paymentsApi.updateProjectAmountReceived(payment.project_id);\n        return data;\n    },\n    async updateProjectAmountReceived (projectId) {\n        // Get all payments for this project\n        const { data: payments, error: paymentsError } = await supabase.from(\"payments\").select(\"amount\").eq(\"project_id\", projectId);\n        if (paymentsError) throw paymentsError;\n        // Calculate total amount received\n        const totalReceived = payments?.reduce((sum, p)=>sum + p.amount, 0) || 0;\n        // Get project with client and shoots to recalculate totals\n        const { data: project, error: projectError } = await supabase.from(\"projects\").select(`\n        *,\n        client:clients(*),\n        schedules:schedules(*)\n      `).eq(\"id\", projectId).single();\n        if (projectError) throw projectError;\n        if (!project) throw new Error(\"Project not found\");\n        // Calculate updated amounts with new amount_received\n        const updatedAmounts = (0,_project_calculations__WEBPACK_IMPORTED_MODULE_1__.getUpdatedProjectAmounts)(project.schedules || [], project.client, totalReceived);\n        // Update project with new amounts\n        const { error: updateError } = await supabase.from(\"projects\").update(updatedAmounts).eq(\"id\", projectId);\n        if (updateError) throw updateError;\n    },\n    async update (id, updates) {\n        // Get the payment to find the project_id before updating\n        const { data: existingPayment, error: getError } = await supabase.from(\"payments\").select(\"project_id\").eq(\"id\", id).single();\n        if (getError) throw getError;\n        const { data, error } = await supabase.from(\"payments\").update(updates).eq(\"id\", id).select(`\n        *,\n        project:projects(*)\n      `).single();\n        if (error) throw error;\n        // Update project's amount_received and recalculate totals\n        await paymentsApi.updateProjectAmountReceived(existingPayment.project_id);\n        // If project_id changed, also update the new project\n        if (updates.project_id && updates.project_id !== existingPayment.project_id) {\n            await paymentsApi.updateProjectAmountReceived(updates.project_id);\n        }\n        return data;\n    },\n    async delete (id) {\n        // Get the payment to find the project_id before deleting\n        const { data: payment, error: getError } = await supabase.from(\"payments\").select(\"project_id\").eq(\"id\", id).single();\n        if (getError) throw getError;\n        const { error } = await supabase.from(\"payments\").delete().eq(\"id\", id);\n        if (error) throw error;\n        // Update project's amount_received and recalculate totals\n        if (payment) {\n            await paymentsApi.updateProjectAmountReceived(payment.project_id);\n        }\n    }\n};\n// Dashboard API\nconst dashboardApi = {\n    async getStats () {\n        // Check cache first\n        const cacheKey = _lib_cache__WEBPACK_IMPORTED_MODULE_2__.cacheKeys.dashboardStats();\n        const cached = _lib_cache__WEBPACK_IMPORTED_MODULE_2__.apiCache.get(cacheKey);\n        if (cached) return cached;\n        // Use optimized database queries with aggregations\n        const now = new Date();\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n        // Get aggregated stats using PostgreSQL functions\n        const { data: stats, error } = await supabase.rpc(\"get_dashboard_stats\");\n        if (error) {\n            console.error(\"Error fetching dashboard stats:\", error);\n            throw error;\n        }\n        // The function returns an array with a single row, so we need to get the first element\n        const statsRow = stats ? Array.isArray(stats) ? stats[0] : stats : null;\n        const dashboardStats = {\n            totalProjects: (statsRow?.active_projects || 0) + (statsRow?.completed_projects || 0),\n            activeProjects: statsRow?.active_projects || 0,\n            completedSchedules: 0,\n            pendingPayments: 0,\n            totalRevenue: 0,\n            monthlyRevenue: statsRow?.monthly_revenue || 0,\n            upcomingSchedules: statsRow?.upcoming_schedules || 0,\n            overdueTasks: statsRow?.overdue_tasks || 0,\n            vendorPayments: {\n                totalPending: 0,\n                totalOverdue: 0,\n                pendingCount: 0,\n                overdueCount: 0 // This would need to be calculated separately if needed\n            }\n        };\n        // Cache for 2 minutes (increased from 1 minute)\n        _lib_cache__WEBPACK_IMPORTED_MODULE_2__.apiCache.set(cacheKey, dashboardStats, 2 * 60 * 1000);\n        return dashboardStats;\n    }\n};\n// Activity Logs API\nconst activityLogsApi = {\n    async getAll (limit = 50) {\n        // Check cache first\n        const cacheKey = `activity_logs_${limit}`;\n        const cached = _lib_cache__WEBPACK_IMPORTED_MODULE_2__.apiCache.get(cacheKey);\n        if (cached) return cached;\n        const { data, error } = await supabase.from(\"activity_logs\").select(`\n        *,\n        users!inner(name, email)\n      `).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching activity logs:\", error);\n            throw error;\n        }\n        // Cache for 1 minute\n        _lib_cache__WEBPACK_IMPORTED_MODULE_2__.apiCache.set(cacheKey, data || [], 60 * 1000);\n        return data || [];\n    },\n    async getByUserId (userId, limit = 20) {\n        const { data, error } = await supabase.from(\"activity_logs\").select(`\n        *,\n        users!inner(name, email)\n      `).eq(\"user_id\", userId).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching user activity logs:\", error);\n            throw error;\n        }\n        return data || [];\n    },\n    async create (activityLog) {\n        const { data, error } = await supabase.from(\"activity_logs\").insert(activityLog).select().single();\n        if (error) {\n            console.error(\"Error creating activity log:\", error);\n            throw error;\n        }\n        // Invalidate cache\n        (0,_lib_cache__WEBPACK_IMPORTED_MODULE_2__.invalidateCache)(\"activity_logs\");\n        return data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-server.ts":
/*!********************************!*\
  !*** ./src/lib/auth-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   updateSession: () => (/* binding */ updateSession)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n\nconst supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU\";\n// Server-side Supabase client\nasync function createServerSupabaseClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n// Middleware helper for auth\nasync function updateSession(request) {\n    let supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next({\n        request\n    });\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            getAll () {\n                return request.cookies.getAll();\n            },\n            setAll (cookiesToSet) {\n                cookiesToSet.forEach(({ name, value })=>request.cookies.set(name, value));\n                supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next({\n                    request\n                });\n                cookiesToSet.forEach(({ name, value, options })=>supabaseResponse.cookies.set(name, value, options));\n            }\n        }\n    });\n    // IMPORTANT: Avoid writing any logic between createServerClient and\n    // supabase.auth.getUser(). A simple mistake could make it very hard to debug\n    // issues with users being randomly logged out.\n    const { data: { user }, error } = await supabase.auth.getUser();\n    console.log(\"\\uD83D\\uDD0D Auth middleware: getUser result:\", {\n        user: user ? `User found: ${user.id}` : \"No user\",\n        error: error?.message,\n        cookies: request.cookies.getAll().map((c)=>c.name).join(\", \")\n    });\n    // List of paths that don't require authentication\n    const unauthenticatedPaths = [\n        \"/login\",\n        \"/auth\",\n        \"/api/auth/callback\",\n        \"/api/auth/signout\",\n        \"/api/auth/signin\",\n        \"/api/auth/session\",\n        \"/api/auth/providers\",\n        \"/api/auth/csrf\",\n        \"/api/public-preview\",\n        \"/api/ensure-sharepoint-folders\",\n        \"/api/ensure-sharepoint-folder\",\n        \"/api/background-jobs\",\n        \"/api/test-schedule-bg-jobs\",\n        \"/api/test-sharepoint\",\n        \"/api/init\",\n        \"/api/test-createwithvendors\",\n        \"/api/test-db-function\",\n        \"/api/test-background-jobs\",\n        \"/api/test-task-creation-fix\"\n    ];\n    const isUnauthenticatedPath = unauthenticatedPaths.some((path)=>request.nextUrl.pathname.startsWith(path));\n    if (!user && !isUnauthenticatedPath) {\n        console.log(\"\\uD83D\\uDEAB Auth middleware: User not authenticated, redirecting to login, path:\", request.nextUrl.pathname);\n        const loginUrl = new URL(\"/login\", request.url);\n        loginUrl.searchParams.set(\"redirect\", request.nextUrl.pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(loginUrl);\n    }\n    // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're\n    // creating a new response object with NextResponse.next() make sure to:\n    // 1. Pass the request in it, like so:\n    //    const myNewResponse = NextResponse.next({ request })\n    // 2. Copy over the cookies, like so:\n    //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\n    // 3. Change the myNewResponse object here instead of the supabaseResponse object\n    return supabaseResponse;\n}\n// Get current user with profile data\nasync function getCurrentUser() {\n    const supabase = await createServerSupabaseClient();\n    const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();\n    if (authError || !authUser) {\n        return null;\n    }\n    // Get user profile data\n    const { data: profile, error: profileError } = await supabase.from(\"users\").select(\"*\").eq(\"id\", authUser.id).single();\n    if (profileError || !profile) {\n        return null;\n    }\n    return {\n        id: profile.id,\n        email: profile.email,\n        name: profile.name,\n        role: profile.role,\n        avatar_url: profile.avatar_url,\n        created_at: profile.created_at,\n        updated_at: profile.updated_at\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabaseClient: () => (/* binding */ createClientSupabaseClient),\n/* harmony export */   hasMinimumRole: () => (/* binding */ hasMinimumRole),\n/* harmony export */   hasRole: () => (/* binding */ hasRole)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\nconst supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU\";\n// Singleton Supabase client instance\nlet supabaseClient = null;\n// Client-side Supabase client (singleton) with SSR support\nfunction createClientSupabaseClient() {\n    if (!supabaseUrl || !supabaseAnonKey) {\n        console.error(\"Missing Supabase environment variables:\", {\n            url: !!supabaseUrl,\n            key: !!supabaseAnonKey\n        });\n        throw new Error(\"Missing Supabase environment variables\");\n    }\n    // Return existing client if already created\n    if (supabaseClient) {\n        return supabaseClient;\n    }\n    // Create new SSR-compatible browser client\n    supabaseClient = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey);\n    return supabaseClient;\n}\n// Check if user has required role\nfunction hasRole(userRole, requiredRoles) {\n    return requiredRoles.includes(userRole);\n}\n// Role hierarchy for permission checking\nconst roleHierarchy = {\n    admin: 4,\n    manager: 3,\n    pilot: 2,\n    editor: 1\n};\n// Check if user has minimum role level\nfunction hasMinimumRole(userRole, minimumRole) {\n    return roleHierarchy[userRole] >= roleHierarchy[minimumRole];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cache.ts":
/*!**************************!*\
  !*** ./src/lib/cache.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiCache: () => (/* binding */ apiCache),\n/* harmony export */   cacheKeys: () => (/* binding */ cacheKeys),\n/* harmony export */   invalidateCache: () => (/* binding */ invalidateCache)\n/* harmony export */ });\n// Simple in-memory cache for API responses\nclass SimpleCache {\n    set(key, data, ttlMs = 5 * 60 * 1000) {\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            ttl: ttlMs\n        });\n    }\n    get(key) {\n        const entry = this.cache.get(key);\n        if (!entry) return null;\n        const now = Date.now();\n        if (now - entry.timestamp > entry.ttl) {\n            this.cache.delete(key);\n            return null;\n        }\n        return entry.data;\n    }\n    delete(key) {\n        this.cache.delete(key);\n    }\n    clear() {\n        this.cache.clear();\n    }\n    // Clean up expired entries\n    cleanup() {\n        const now = Date.now();\n        for (const [key, entry] of this.cache.entries()){\n            if (now - entry.timestamp > entry.ttl) {\n                this.cache.delete(key);\n            }\n        }\n    }\n    constructor(){\n        this.cache = new Map();\n    }\n}\nconst apiCache = new SimpleCache();\n// Clean up expired entries every 15 minutes to reduce CPU usage\nif (false) {}\n// Cache key generators with optimized TTLs\nconst cacheKeys = {\n    projects: ()=>\"projects:all\",\n    project: (id)=>`project:${id}`,\n    clients: ()=>\"clients:all\",\n    client: (id)=>`client:${id}`,\n    users: ()=>\"users:all\",\n    tasks: (filters)=>`tasks:${JSON.stringify(filters || {})}`,\n    dashboardStats: ()=>\"dashboard:stats\",\n    schedules: (projectId)=>projectId ? `schedules:project:${projectId}` : \"schedules:all\" // 5 minutes for schedules\n};\n// Cache invalidation helpers\nconst invalidateCache = {\n    projects: ()=>{\n        apiCache.delete(cacheKeys.projects());\n        apiCache.delete(cacheKeys.dashboardStats());\n    },\n    project: (id)=>{\n        apiCache.delete(cacheKeys.project(id));\n        apiCache.delete(cacheKeys.projects());\n        apiCache.delete(cacheKeys.dashboardStats());\n    },\n    clients: ()=>{\n        apiCache.delete(cacheKeys.clients());\n    },\n    client: (id)=>{\n        apiCache.delete(cacheKeys.client(id));\n        apiCache.delete(cacheKeys.clients());\n    },\n    tasks: ()=>{\n        // Clear all task-related cache entries\n        for (const key of apiCache[\"cache\"].keys()){\n            if (key.startsWith(\"tasks:\")) {\n                apiCache.delete(key);\n            }\n        }\n        apiCache.delete(cacheKeys.dashboardStats());\n    },\n    schedules: (projectId)=>{\n        if (projectId) {\n            apiCache.delete(cacheKeys.schedules(projectId));\n            apiCache.delete(cacheKeys.project(projectId));\n        }\n        apiCache.delete(cacheKeys.schedules());\n        apiCache.delete(cacheKeys.dashboardStats());\n    },\n    all: ()=>{\n        apiCache.clear();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cache.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/default-tasks.ts":
/*!**********************************!*\
  !*** ./src/lib/default-tasks.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TASK_TEMPLATES: () => (/* binding */ DEFAULT_TASK_TEMPLATES),\n/* harmony export */   createTasksFromTemplates: () => (/* binding */ createTasksFromTemplates),\n/* harmony export */   getClientTypesWithDefaultTasks: () => (/* binding */ getClientTypesWithDefaultTasks),\n/* harmony export */   getDefaultTasksForClientType: () => (/* binding */ getDefaultTasksForClientType),\n/* harmony export */   getDefaultTasksForScheduleType: () => (/* binding */ getDefaultTasksForScheduleType)\n/* harmony export */ });\n// Default task templates for each schedule type (based on requirements/tasks.md)\nconst DEFAULT_TASK_TEMPLATES = {\n    // Wedding, Movie, Surveillance, Events, News, Collaboration\n    Wedding: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the wedding shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Movie: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the movie shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Surveillance: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the surveillance shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Event: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the event shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    News: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the news shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Collaboration: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the collaboration shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Corporate, Real Estate (Script Confirmation + Shoot + File Upload + File Backup + Edit)\n    Corporate: [\n        {\n            title: \"Script Confirmation\",\n            description: \"Confirm script and requirements with client\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Shoot\",\n            description: \"Conduct the corporate shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Edit\",\n            description: \"Edit and post-process the footage\",\n            assigned_role: \"editor\",\n            priority: \"high\",\n            order: 5,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    \"Real Estate\": [\n        {\n            title: \"Script Confirmation\",\n            description: \"Confirm property details and requirements\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Shoot\",\n            description: \"Conduct the real estate shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Edit\",\n            description: \"Edit and enhance the property footage\",\n            assigned_role: \"editor\",\n            priority: \"high\",\n            order: 5,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Government, NGO (Shoot + File Upload + File Backup + Edit)\n    Govt: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the government project shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Edit\",\n            description: \"Edit and process the footage\",\n            assigned_role: \"editor\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    NGO: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the NGO project shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Edit\",\n            description: \"Edit and process the footage\",\n            assigned_role: \"editor\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Survey (Plan Flight + Mark GCPs + Shoot + File Upload + File Backup + Post-Processing)\n    Survey: [\n        {\n            title: \"Plan Flight\",\n            description: \"Plan the survey flight path and parameters\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Mark GCPs\",\n            description: \"Mark Ground Control Points for survey accuracy\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: \"Shoot\",\n            description: \"Conduct the survey shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 3,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured survey data to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all survey data\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 5,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Post-Processing\",\n            description: \"Process survey data and generate maps/models\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 6,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver processed survey results to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ]\n};\n/**\n * Get default tasks for a specific client type (for project-level tasks)\n */ function getDefaultTasksForClientType(clientType) {\n    return DEFAULT_TASK_TEMPLATES[clientType] || [];\n}\n/**\n * Get default tasks for a specific schedule type (for schedule-based tasks)\n */ function getDefaultTasksForScheduleType(scheduleType) {\n    return DEFAULT_TASK_TEMPLATES[scheduleType] || [];\n}\n/**\n * Create task forms from templates for a specific project\n */ function createTasksFromTemplates(templates, projectId, usersByRole, shootDate, shootId // Optional shoot ID for shoot-based tasks\n) {\n    const sortedTemplates = templates.sort((a, b)=>a.order - b.order);\n    return sortedTemplates.map((template)=>{\n        let dueDate;\n        // Special case: Shoot task should have due date as the schedule start time\n        if (shootDate && template.title === \"Shoot\") {\n            dueDate = shootDate // Use the full schedule date/time\n            ;\n        } else if (shootDate && template.dueDaysAfterShoot) {\n            const shootDateTime = new Date(shootDate);\n            const dueDateObj = new Date(shootDateTime);\n            dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot);\n            dueDate = dueDateObj.toISOString().split(\"T\")[0] // Format as YYYY-MM-DD\n            ;\n        }\n        // Handle tasks that depend on other tasks (like Payment Collect after Deliver Files)\n        if (shootDate && template.dueDaysAfterTask && template.dueDaysAfterShoot) {\n            const shootDateTime = new Date(shootDate);\n            const dueDateObj = new Date(shootDateTime);\n            dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot);\n            dueDate = dueDateObj.toISOString().split(\"T\")[0];\n        }\n        return {\n            title: template.title,\n            description: template.description,\n            status: \"pending\",\n            priority: template.priority,\n            assigned_to: usersByRole[template.assigned_role] || \"\",\n            assigned_role: template.assigned_role,\n            project_id: projectId,\n            shoot_id: template.isProjectTask ? undefined : shootId,\n            due_date: dueDate,\n            order: template.order\n        };\n    });\n}\n/**\n * Get all available client types that have default tasks\n */ function getClientTypesWithDefaultTasks() {\n    return Object.keys(DEFAULT_TASK_TEMPLATES);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/default-tasks.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/performance.ts":
/*!********************************!*\
  !*** ./src/lib/performance.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initWebVitals: () => (/* binding */ initWebVitals),\n/* harmony export */   measureApiCall: () => (/* binding */ measureApiCall),\n/* harmony export */   measureRender: () => (/* binding */ measureRender),\n/* harmony export */   performanceMonitor: () => (/* binding */ performanceMonitor)\n/* harmony export */ });\n// Performance monitoring utilities\nclass PerformanceMonitor {\n    // Start timing an operation\n    startTimer(name) {\n        this.timers.set(name, performance.now());\n    }\n    // End timing and record the metric\n    endTimer(name) {\n        const startTime = this.timers.get(name);\n        if (!startTime) {\n            console.warn(`Timer '${name}' was not started`);\n            return 0;\n        }\n        const duration = performance.now() - startTime;\n        this.timers.delete(name);\n        this.metrics.push({\n            name,\n            duration,\n            timestamp: Date.now()\n        });\n        // Log slow operations (> 100ms)\n        if (duration > 100) {\n            console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`);\n        }\n        return duration;\n    }\n    // Measure a function execution time\n    async measure(name, fn) {\n        this.startTimer(name);\n        try {\n            const result = await fn();\n            this.endTimer(name);\n            return result;\n        } catch (error) {\n            this.endTimer(name);\n            throw error;\n        }\n    }\n    // Get performance metrics\n    getMetrics() {\n        return [\n            ...this.metrics\n        ];\n    }\n    // Get metrics for a specific operation\n    getMetricsFor(name) {\n        return this.metrics.filter((m)=>m.name === name);\n    }\n    // Get average duration for an operation\n    getAverageDuration(name) {\n        const operationMetrics = this.getMetricsFor(name);\n        if (operationMetrics.length === 0) return 0;\n        const total = operationMetrics.reduce((sum, m)=>sum + m.duration, 0);\n        return total / operationMetrics.length;\n    }\n    // Clear old metrics (keep only last 100)\n    cleanup() {\n        if (this.metrics.length > 100) {\n            this.metrics = this.metrics.slice(-100);\n        }\n    }\n    // Get performance summary\n    getSummary() {\n        const summary = {};\n        this.metrics.forEach((metric)=>{\n            if (!summary[metric.name]) {\n                summary[metric.name] = {\n                    count: 0,\n                    avgDuration: 0,\n                    maxDuration: 0\n                };\n            }\n            summary[metric.name].count++;\n            summary[metric.name].maxDuration = Math.max(summary[metric.name].maxDuration, metric.duration);\n        });\n        // Calculate averages\n        Object.keys(summary).forEach((name)=>{\n            summary[name].avgDuration = this.getAverageDuration(name);\n        });\n        return summary;\n    }\n    constructor(){\n        this.metrics = [];\n        this.timers = new Map();\n    }\n}\nconst performanceMonitor = new PerformanceMonitor();\n// Clean up metrics every 15 minutes to reduce CPU usage\nif (false) {}\n// Utility function to measure API calls\nasync function measureApiCall(name, apiCall) {\n    return performanceMonitor.measure(`api:${name}`, apiCall);\n}\n// Utility function to measure component renders\nfunction measureRender(componentName) {\n    const startTime = performance.now();\n    return ()=>{\n        const duration = performance.now() - startTime;\n        if (duration > 16) {\n            console.warn(`Slow render: ${componentName} took ${duration.toFixed(2)}ms`);\n        }\n    };\n}\n// Web Vitals monitoring\nfunction initWebVitals() {\n    if (true) return;\n    // Monitor Largest Contentful Paint (LCP)\n    if (\"PerformanceObserver\" in window) {\n        try {\n            const observer = new PerformanceObserver((list)=>{\n                const entries = list.getEntries();\n                const lastEntry = entries[entries.length - 1];\n                if (lastEntry && lastEntry.startTime > 2500) {\n                    console.warn(`Poor LCP: ${lastEntry.startTime.toFixed(2)}ms`);\n                }\n            });\n            observer.observe({\n                entryTypes: [\n                    \"largest-contentful-paint\"\n                ]\n            });\n        } catch (e) {\n        // Ignore errors in older browsers\n        }\n    }\n    // Monitor First Input Delay (FID)\n    if (\"PerformanceObserver\" in window) {\n        try {\n            const observer = new PerformanceObserver((list)=>{\n                const entries = list.getEntries();\n                entries.forEach((entry)=>{\n                    if (entry.processingStart - entry.startTime > 100) {\n                        console.warn(`Poor FID: ${(entry.processingStart - entry.startTime).toFixed(2)}ms`);\n                    }\n                });\n            });\n            observer.observe({\n                entryTypes: [\n                    \"first-input\"\n                ]\n            });\n        } catch (e) {\n        // Ignore errors in older browsers\n        }\n    }\n}\n// Initialize web vitals monitoring\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3BlcmZvcm1hbmNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSxtQ0FBbUM7QUFRbkMsTUFBTUE7SUFJSiw0QkFBNEI7SUFDNUJDLFdBQVdDLElBQVksRUFBUTtRQUM3QixJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsR0FBRyxDQUFDRixNQUFNRyxZQUFZQyxHQUFHO0lBQ3ZDO0lBRUEsbUNBQW1DO0lBQ25DQyxTQUFTTCxJQUFZLEVBQVU7UUFDN0IsTUFBTU0sWUFBWSxJQUFJLENBQUNMLE1BQU0sQ0FBQ00sR0FBRyxDQUFDUDtRQUNsQyxJQUFJLENBQUNNLFdBQVc7WUFDZEUsUUFBUUMsSUFBSSxDQUFDLENBQUMsT0FBTyxFQUFFVCxLQUFLLGlCQUFpQixDQUFDO1lBQzlDLE9BQU87UUFDVDtRQUVBLE1BQU1VLFdBQVdQLFlBQVlDLEdBQUcsS0FBS0U7UUFDckMsSUFBSSxDQUFDTCxNQUFNLENBQUNVLE1BQU0sQ0FBQ1g7UUFFbkIsSUFBSSxDQUFDWSxPQUFPLENBQUNDLElBQUksQ0FBQztZQUNoQmI7WUFDQVU7WUFDQUksV0FBV0MsS0FBS1gsR0FBRztRQUNyQjtRQUVBLGdDQUFnQztRQUNoQyxJQUFJTSxXQUFXLEtBQUs7WUFDbEJGLFFBQVFDLElBQUksQ0FBQyxDQUFDLHlCQUF5QixFQUFFVCxLQUFLLE1BQU0sRUFBRVUsU0FBU00sT0FBTyxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBQy9FO1FBRUEsT0FBT047SUFDVDtJQUVBLG9DQUFvQztJQUNwQyxNQUFNTyxRQUFXakIsSUFBWSxFQUFFa0IsRUFBb0IsRUFBYztRQUMvRCxJQUFJLENBQUNuQixVQUFVLENBQUNDO1FBQ2hCLElBQUk7WUFDRixNQUFNbUIsU0FBUyxNQUFNRDtZQUNyQixJQUFJLENBQUNiLFFBQVEsQ0FBQ0w7WUFDZCxPQUFPbUI7UUFDVCxFQUFFLE9BQU9DLE9BQU87WUFDZCxJQUFJLENBQUNmLFFBQVEsQ0FBQ0w7WUFDZCxNQUFNb0I7UUFDUjtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCQyxhQUFrQztRQUNoQyxPQUFPO2VBQUksSUFBSSxDQUFDVCxPQUFPO1NBQUM7SUFDMUI7SUFFQSx1Q0FBdUM7SUFDdkNVLGNBQWN0QixJQUFZLEVBQXVCO1FBQy9DLE9BQU8sSUFBSSxDQUFDWSxPQUFPLENBQUNXLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXhCLElBQUksS0FBS0E7SUFDN0M7SUFFQSx3Q0FBd0M7SUFDeEN5QixtQkFBbUJ6QixJQUFZLEVBQVU7UUFDdkMsTUFBTTBCLG1CQUFtQixJQUFJLENBQUNKLGFBQWEsQ0FBQ3RCO1FBQzVDLElBQUkwQixpQkFBaUJDLE1BQU0sS0FBSyxHQUFHLE9BQU87UUFFMUMsTUFBTUMsUUFBUUYsaUJBQWlCRyxNQUFNLENBQUMsQ0FBQ0MsS0FBS04sSUFBTU0sTUFBTU4sRUFBRWQsUUFBUSxFQUFFO1FBQ3BFLE9BQU9rQixRQUFRRixpQkFBaUJDLE1BQU07SUFDeEM7SUFFQSx5Q0FBeUM7SUFDekNJLFVBQWdCO1FBQ2QsSUFBSSxJQUFJLENBQUNuQixPQUFPLENBQUNlLE1BQU0sR0FBRyxLQUFLO1lBQzdCLElBQUksQ0FBQ2YsT0FBTyxHQUFHLElBQUksQ0FBQ0EsT0FBTyxDQUFDb0IsS0FBSyxDQUFDLENBQUM7UUFDckM7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQkMsYUFBMEY7UUFDeEYsTUFBTUMsVUFBdUYsQ0FBQztRQUU5RixJQUFJLENBQUN0QixPQUFPLENBQUN1QixPQUFPLENBQUNDLENBQUFBO1lBQ25CLElBQUksQ0FBQ0YsT0FBTyxDQUFDRSxPQUFPcEMsSUFBSSxDQUFDLEVBQUU7Z0JBQ3pCa0MsT0FBTyxDQUFDRSxPQUFPcEMsSUFBSSxDQUFDLEdBQUc7b0JBQUVxQyxPQUFPO29CQUFHQyxhQUFhO29CQUFHQyxhQUFhO2dCQUFFO1lBQ3BFO1lBRUFMLE9BQU8sQ0FBQ0UsT0FBT3BDLElBQUksQ0FBQyxDQUFDcUMsS0FBSztZQUMxQkgsT0FBTyxDQUFDRSxPQUFPcEMsSUFBSSxDQUFDLENBQUN1QyxXQUFXLEdBQUdDLEtBQUtDLEdBQUcsQ0FBQ1AsT0FBTyxDQUFDRSxPQUFPcEMsSUFBSSxDQUFDLENBQUN1QyxXQUFXLEVBQUVILE9BQU8xQixRQUFRO1FBQy9GO1FBRUEscUJBQXFCO1FBQ3JCZ0MsT0FBT0MsSUFBSSxDQUFDVCxTQUFTQyxPQUFPLENBQUNuQyxDQUFBQTtZQUMzQmtDLE9BQU8sQ0FBQ2xDLEtBQUssQ0FBQ3NDLFdBQVcsR0FBRyxJQUFJLENBQUNiLGtCQUFrQixDQUFDekI7UUFDdEQ7UUFFQSxPQUFPa0M7SUFDVDs7YUEzRlF0QixVQUErQixFQUFFO2FBQ2pDWCxTQUE4QixJQUFJMkM7O0FBMkY1QztBQUVPLE1BQU1DLHFCQUFxQixJQUFJL0MscUJBQW9CO0FBRTFELHdEQUF3RDtBQUN4RCxJQUFJLEtBQWtCLEVBQWEsRUFJbEM7QUFFRCx3Q0FBd0M7QUFDakMsZUFBZWlELGVBQWtCL0MsSUFBWSxFQUFFZ0QsT0FBeUI7SUFDN0UsT0FBT0gsbUJBQW1CNUIsT0FBTyxDQUFDLENBQUMsSUFBSSxFQUFFakIsS0FBSyxDQUFDLEVBQUVnRDtBQUNuRDtBQUVBLGdEQUFnRDtBQUN6QyxTQUFTQyxjQUFjQyxhQUFxQjtJQUNqRCxNQUFNNUMsWUFBWUgsWUFBWUMsR0FBRztJQUNqQyxPQUFPO1FBQ0wsTUFBTU0sV0FBV1AsWUFBWUMsR0FBRyxLQUFLRTtRQUNyQyxJQUFJSSxXQUFXLElBQUk7WUFDakJGLFFBQVFDLElBQUksQ0FBQyxDQUFDLGFBQWEsRUFBRXlDLGNBQWMsTUFBTSxFQUFFeEMsU0FBU00sT0FBTyxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBQzVFO0lBQ0Y7QUFDRjtBQUVBLHdCQUF3QjtBQUNqQixTQUFTbUM7SUFDZCxJQUFJLElBQWtCLEVBQWE7SUFFbkMseUNBQXlDO0lBQ3pDLElBQUkseUJBQXlCQyxRQUFRO1FBQ25DLElBQUk7WUFDRixNQUFNQyxXQUFXLElBQUlDLG9CQUFvQixDQUFDQztnQkFDeEMsTUFBTUMsVUFBVUQsS0FBS0UsVUFBVTtnQkFDL0IsTUFBTUMsWUFBWUYsT0FBTyxDQUFDQSxRQUFRN0IsTUFBTSxHQUFHLEVBQUU7Z0JBRTdDLElBQUkrQixhQUFhQSxVQUFVcEQsU0FBUyxHQUFHLE1BQU07b0JBQzNDRSxRQUFRQyxJQUFJLENBQUMsQ0FBQyxVQUFVLEVBQUVpRCxVQUFVcEQsU0FBUyxDQUFDVSxPQUFPLENBQUMsR0FBRyxFQUFFLENBQUM7Z0JBQzlEO1lBQ0Y7WUFFQXFDLFNBQVNNLE9BQU8sQ0FBQztnQkFBRUMsWUFBWTtvQkFBQztpQkFBMkI7WUFBQztRQUM5RCxFQUFFLE9BQU9DLEdBQUc7UUFDVixrQ0FBa0M7UUFDcEM7SUFDRjtJQUVBLGtDQUFrQztJQUNsQyxJQUFJLHlCQUF5QlQsUUFBUTtRQUNuQyxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxJQUFJQyxvQkFBb0IsQ0FBQ0M7Z0JBQ3hDLE1BQU1DLFVBQVVELEtBQUtFLFVBQVU7Z0JBQy9CRCxRQUFRckIsT0FBTyxDQUFDLENBQUMyQjtvQkFDZixJQUFJQSxNQUFNQyxlQUFlLEdBQUdELE1BQU14RCxTQUFTLEdBQUcsS0FBSzt3QkFDakRFLFFBQVFDLElBQUksQ0FBQyxDQUFDLFVBQVUsRUFBRSxDQUFDcUQsTUFBTUMsZUFBZSxHQUFHRCxNQUFNeEQsU0FBUyxFQUFFVSxPQUFPLENBQUMsR0FBRyxFQUFFLENBQUM7b0JBQ3BGO2dCQUNGO1lBQ0Y7WUFFQXFDLFNBQVNNLE9BQU8sQ0FBQztnQkFBRUMsWUFBWTtvQkFBQztpQkFBYztZQUFDO1FBQ2pELEVBQUUsT0FBT0MsR0FBRztRQUNWLGtDQUFrQztRQUNwQztJQUNGO0FBQ0Y7QUFFQSxtQ0FBbUM7QUFDbkMsSUFBSSxLQUFrQixFQUFhLEVBRWxDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3ltYXRpY3MtZnJvbnRlbmQvLi9zcmMvbGliL3BlcmZvcm1hbmNlLnRzPzI2ODQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUGVyZm9ybWFuY2UgbW9uaXRvcmluZyB1dGlsaXRpZXNcblxuaW50ZXJmYWNlIFBlcmZvcm1hbmNlTWV0cmljIHtcbiAgbmFtZTogc3RyaW5nXG4gIGR1cmF0aW9uOiBudW1iZXJcbiAgdGltZXN0YW1wOiBudW1iZXJcbn1cblxuY2xhc3MgUGVyZm9ybWFuY2VNb25pdG9yIHtcbiAgcHJpdmF0ZSBtZXRyaWNzOiBQZXJmb3JtYW5jZU1ldHJpY1tdID0gW11cbiAgcHJpdmF0ZSB0aW1lcnM6IE1hcDxzdHJpbmcsIG51bWJlcj4gPSBuZXcgTWFwKClcblxuICAvLyBTdGFydCB0aW1pbmcgYW4gb3BlcmF0aW9uXG4gIHN0YXJ0VGltZXIobmFtZTogc3RyaW5nKTogdm9pZCB7XG4gICAgdGhpcy50aW1lcnMuc2V0KG5hbWUsIHBlcmZvcm1hbmNlLm5vdygpKVxuICB9XG5cbiAgLy8gRW5kIHRpbWluZyBhbmQgcmVjb3JkIHRoZSBtZXRyaWNcbiAgZW5kVGltZXIobmFtZTogc3RyaW5nKTogbnVtYmVyIHtcbiAgICBjb25zdCBzdGFydFRpbWUgPSB0aGlzLnRpbWVycy5nZXQobmFtZSlcbiAgICBpZiAoIXN0YXJ0VGltZSkge1xuICAgICAgY29uc29sZS53YXJuKGBUaW1lciAnJHtuYW1lfScgd2FzIG5vdCBzdGFydGVkYClcbiAgICAgIHJldHVybiAwXG4gICAgfVxuXG4gICAgY29uc3QgZHVyYXRpb24gPSBwZXJmb3JtYW5jZS5ub3coKSAtIHN0YXJ0VGltZVxuICAgIHRoaXMudGltZXJzLmRlbGV0ZShuYW1lKVxuXG4gICAgdGhpcy5tZXRyaWNzLnB1c2goe1xuICAgICAgbmFtZSxcbiAgICAgIGR1cmF0aW9uLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgfSlcblxuICAgIC8vIExvZyBzbG93IG9wZXJhdGlvbnMgKD4gMTAwbXMpXG4gICAgaWYgKGR1cmF0aW9uID4gMTAwKSB7XG4gICAgICBjb25zb2xlLndhcm4oYFNsb3cgb3BlcmF0aW9uIGRldGVjdGVkOiAke25hbWV9IHRvb2sgJHtkdXJhdGlvbi50b0ZpeGVkKDIpfW1zYClcbiAgICB9XG5cbiAgICByZXR1cm4gZHVyYXRpb25cbiAgfVxuXG4gIC8vIE1lYXN1cmUgYSBmdW5jdGlvbiBleGVjdXRpb24gdGltZVxuICBhc3luYyBtZWFzdXJlPFQ+KG5hbWU6IHN0cmluZywgZm46ICgpID0+IFByb21pc2U8VD4pOiBQcm9taXNlPFQ+IHtcbiAgICB0aGlzLnN0YXJ0VGltZXIobmFtZSlcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZm4oKVxuICAgICAgdGhpcy5lbmRUaW1lcihuYW1lKVxuICAgICAgcmV0dXJuIHJlc3VsdFxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aGlzLmVuZFRpbWVyKG5hbWUpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8vIEdldCBwZXJmb3JtYW5jZSBtZXRyaWNzXG4gIGdldE1ldHJpY3MoKTogUGVyZm9ybWFuY2VNZXRyaWNbXSB7XG4gICAgcmV0dXJuIFsuLi50aGlzLm1ldHJpY3NdXG4gIH1cblxuICAvLyBHZXQgbWV0cmljcyBmb3IgYSBzcGVjaWZpYyBvcGVyYXRpb25cbiAgZ2V0TWV0cmljc0ZvcihuYW1lOiBzdHJpbmcpOiBQZXJmb3JtYW5jZU1ldHJpY1tdIHtcbiAgICByZXR1cm4gdGhpcy5tZXRyaWNzLmZpbHRlcihtID0+IG0ubmFtZSA9PT0gbmFtZSlcbiAgfVxuXG4gIC8vIEdldCBhdmVyYWdlIGR1cmF0aW9uIGZvciBhbiBvcGVyYXRpb25cbiAgZ2V0QXZlcmFnZUR1cmF0aW9uKG5hbWU6IHN0cmluZyk6IG51bWJlciB7XG4gICAgY29uc3Qgb3BlcmF0aW9uTWV0cmljcyA9IHRoaXMuZ2V0TWV0cmljc0ZvcihuYW1lKVxuICAgIGlmIChvcGVyYXRpb25NZXRyaWNzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIDBcblxuICAgIGNvbnN0IHRvdGFsID0gb3BlcmF0aW9uTWV0cmljcy5yZWR1Y2UoKHN1bSwgbSkgPT4gc3VtICsgbS5kdXJhdGlvbiwgMClcbiAgICByZXR1cm4gdG90YWwgLyBvcGVyYXRpb25NZXRyaWNzLmxlbmd0aFxuICB9XG5cbiAgLy8gQ2xlYXIgb2xkIG1ldHJpY3MgKGtlZXAgb25seSBsYXN0IDEwMClcbiAgY2xlYW51cCgpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5tZXRyaWNzLmxlbmd0aCA+IDEwMCkge1xuICAgICAgdGhpcy5tZXRyaWNzID0gdGhpcy5tZXRyaWNzLnNsaWNlKC0xMDApXG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IHBlcmZvcm1hbmNlIHN1bW1hcnlcbiAgZ2V0U3VtbWFyeSgpOiBSZWNvcmQ8c3RyaW5nLCB7IGNvdW50OiBudW1iZXI7IGF2Z0R1cmF0aW9uOiBudW1iZXI7IG1heER1cmF0aW9uOiBudW1iZXIgfT4ge1xuICAgIGNvbnN0IHN1bW1hcnk6IFJlY29yZDxzdHJpbmcsIHsgY291bnQ6IG51bWJlcjsgYXZnRHVyYXRpb246IG51bWJlcjsgbWF4RHVyYXRpb246IG51bWJlciB9PiA9IHt9XG5cbiAgICB0aGlzLm1ldHJpY3MuZm9yRWFjaChtZXRyaWMgPT4ge1xuICAgICAgaWYgKCFzdW1tYXJ5W21ldHJpYy5uYW1lXSkge1xuICAgICAgICBzdW1tYXJ5W21ldHJpYy5uYW1lXSA9IHsgY291bnQ6IDAsIGF2Z0R1cmF0aW9uOiAwLCBtYXhEdXJhdGlvbjogMCB9XG4gICAgICB9XG5cbiAgICAgIHN1bW1hcnlbbWV0cmljLm5hbWVdLmNvdW50KytcbiAgICAgIHN1bW1hcnlbbWV0cmljLm5hbWVdLm1heER1cmF0aW9uID0gTWF0aC5tYXgoc3VtbWFyeVttZXRyaWMubmFtZV0ubWF4RHVyYXRpb24sIG1ldHJpYy5kdXJhdGlvbilcbiAgICB9KVxuXG4gICAgLy8gQ2FsY3VsYXRlIGF2ZXJhZ2VzXG4gICAgT2JqZWN0LmtleXMoc3VtbWFyeSkuZm9yRWFjaChuYW1lID0+IHtcbiAgICAgIHN1bW1hcnlbbmFtZV0uYXZnRHVyYXRpb24gPSB0aGlzLmdldEF2ZXJhZ2VEdXJhdGlvbihuYW1lKVxuICAgIH0pXG5cbiAgICByZXR1cm4gc3VtbWFyeVxuICB9XG59XG5cbmV4cG9ydCBjb25zdCBwZXJmb3JtYW5jZU1vbml0b3IgPSBuZXcgUGVyZm9ybWFuY2VNb25pdG9yKClcblxuLy8gQ2xlYW4gdXAgbWV0cmljcyBldmVyeSAxNSBtaW51dGVzIHRvIHJlZHVjZSBDUFUgdXNhZ2VcbmlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgcGVyZm9ybWFuY2VNb25pdG9yLmNsZWFudXAoKVxuICB9LCAxNSAqIDYwICogMTAwMClcbn1cblxuLy8gVXRpbGl0eSBmdW5jdGlvbiB0byBtZWFzdXJlIEFQSSBjYWxsc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIG1lYXN1cmVBcGlDYWxsPFQ+KG5hbWU6IHN0cmluZywgYXBpQ2FsbDogKCkgPT4gUHJvbWlzZTxUPik6IFByb21pc2U8VD4ge1xuICByZXR1cm4gcGVyZm9ybWFuY2VNb25pdG9yLm1lYXN1cmUoYGFwaToke25hbWV9YCwgYXBpQ2FsbClcbn1cblxuLy8gVXRpbGl0eSBmdW5jdGlvbiB0byBtZWFzdXJlIGNvbXBvbmVudCByZW5kZXJzXG5leHBvcnQgZnVuY3Rpb24gbWVhc3VyZVJlbmRlcihjb21wb25lbnROYW1lOiBzdHJpbmcpOiAoKSA9PiB2b2lkIHtcbiAgY29uc3Qgc3RhcnRUaW1lID0gcGVyZm9ybWFuY2Uubm93KClcbiAgcmV0dXJuICgpID0+IHtcbiAgICBjb25zdCBkdXJhdGlvbiA9IHBlcmZvcm1hbmNlLm5vdygpIC0gc3RhcnRUaW1lXG4gICAgaWYgKGR1cmF0aW9uID4gMTYpIHsgLy8gTW9yZSB0aGFuIG9uZSBmcmFtZSAoNjBmcHMpXG4gICAgICBjb25zb2xlLndhcm4oYFNsb3cgcmVuZGVyOiAke2NvbXBvbmVudE5hbWV9IHRvb2sgJHtkdXJhdGlvbi50b0ZpeGVkKDIpfW1zYClcbiAgICB9XG4gIH1cbn1cblxuLy8gV2ViIFZpdGFscyBtb25pdG9yaW5nXG5leHBvcnQgZnVuY3Rpb24gaW5pdFdlYlZpdGFscygpOiB2b2lkIHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cblxuICAvLyBNb25pdG9yIExhcmdlc3QgQ29udGVudGZ1bCBQYWludCAoTENQKVxuICBpZiAoJ1BlcmZvcm1hbmNlT2JzZXJ2ZXInIGluIHdpbmRvdykge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBQZXJmb3JtYW5jZU9ic2VydmVyKChsaXN0KSA9PiB7XG4gICAgICAgIGNvbnN0IGVudHJpZXMgPSBsaXN0LmdldEVudHJpZXMoKVxuICAgICAgICBjb25zdCBsYXN0RW50cnkgPSBlbnRyaWVzW2VudHJpZXMubGVuZ3RoIC0gMV1cbiAgICAgICAgXG4gICAgICAgIGlmIChsYXN0RW50cnkgJiYgbGFzdEVudHJ5LnN0YXJ0VGltZSA+IDI1MDApIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oYFBvb3IgTENQOiAke2xhc3RFbnRyeS5zdGFydFRpbWUudG9GaXhlZCgyKX1tc2ApXG4gICAgICAgIH1cbiAgICAgIH0pXG4gICAgICBcbiAgICAgIG9ic2VydmVyLm9ic2VydmUoeyBlbnRyeVR5cGVzOiBbJ2xhcmdlc3QtY29udGVudGZ1bC1wYWludCddIH0pXG4gICAgfSBjYXRjaCAoZSkge1xuICAgICAgLy8gSWdub3JlIGVycm9ycyBpbiBvbGRlciBicm93c2Vyc1xuICAgIH1cbiAgfVxuXG4gIC8vIE1vbml0b3IgRmlyc3QgSW5wdXQgRGVsYXkgKEZJRClcbiAgaWYgKCdQZXJmb3JtYW5jZU9ic2VydmVyJyBpbiB3aW5kb3cpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgb2JzZXJ2ZXIgPSBuZXcgUGVyZm9ybWFuY2VPYnNlcnZlcigobGlzdCkgPT4ge1xuICAgICAgICBjb25zdCBlbnRyaWVzID0gbGlzdC5nZXRFbnRyaWVzKClcbiAgICAgICAgZW50cmllcy5mb3JFYWNoKChlbnRyeTogYW55KSA9PiB7XG4gICAgICAgICAgaWYgKGVudHJ5LnByb2Nlc3NpbmdTdGFydCAtIGVudHJ5LnN0YXJ0VGltZSA+IDEwMCkge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKGBQb29yIEZJRDogJHsoZW50cnkucHJvY2Vzc2luZ1N0YXJ0IC0gZW50cnkuc3RhcnRUaW1lKS50b0ZpeGVkKDIpfW1zYClcbiAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgICB9KVxuICAgICAgXG4gICAgICBvYnNlcnZlci5vYnNlcnZlKHsgZW50cnlUeXBlczogWydmaXJzdC1pbnB1dCddIH0pXG4gICAgfSBjYXRjaCAoZSkge1xuICAgICAgLy8gSWdub3JlIGVycm9ycyBpbiBvbGRlciBicm93c2Vyc1xuICAgIH1cbiAgfVxufVxuXG4vLyBJbml0aWFsaXplIHdlYiB2aXRhbHMgbW9uaXRvcmluZ1xuaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gIGluaXRXZWJWaXRhbHMoKVxufVxuIl0sIm5hbWVzIjpbIlBlcmZvcm1hbmNlTW9uaXRvciIsInN0YXJ0VGltZXIiLCJuYW1lIiwidGltZXJzIiwic2V0IiwicGVyZm9ybWFuY2UiLCJub3ciLCJlbmRUaW1lciIsInN0YXJ0VGltZSIsImdldCIsImNvbnNvbGUiLCJ3YXJuIiwiZHVyYXRpb24iLCJkZWxldGUiLCJtZXRyaWNzIiwicHVzaCIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0ZpeGVkIiwibWVhc3VyZSIsImZuIiwicmVzdWx0IiwiZXJyb3IiLCJnZXRNZXRyaWNzIiwiZ2V0TWV0cmljc0ZvciIsImZpbHRlciIsIm0iLCJnZXRBdmVyYWdlRHVyYXRpb24iLCJvcGVyYXRpb25NZXRyaWNzIiwibGVuZ3RoIiwidG90YWwiLCJyZWR1Y2UiLCJzdW0iLCJjbGVhbnVwIiwic2xpY2UiLCJnZXRTdW1tYXJ5Iiwic3VtbWFyeSIsImZvckVhY2giLCJtZXRyaWMiLCJjb3VudCIsImF2Z0R1cmF0aW9uIiwibWF4RHVyYXRpb24iLCJNYXRoIiwibWF4IiwiT2JqZWN0Iiwia2V5cyIsIk1hcCIsInBlcmZvcm1hbmNlTW9uaXRvciIsInNldEludGVydmFsIiwibWVhc3VyZUFwaUNhbGwiLCJhcGlDYWxsIiwibWVhc3VyZVJlbmRlciIsImNvbXBvbmVudE5hbWUiLCJpbml0V2ViVml0YWxzIiwid2luZG93Iiwib2JzZXJ2ZXIiLCJQZXJmb3JtYW5jZU9ic2VydmVyIiwibGlzdCIsImVudHJpZXMiLCJnZXRFbnRyaWVzIiwibGFzdEVudHJ5Iiwib2JzZXJ2ZSIsImVudHJ5VHlwZXMiLCJlIiwiZW50cnkiLCJwcm9jZXNzaW5nU3RhcnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/performance.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/project-calculations.ts":
/*!*****************************************!*\
  !*** ./src/lib/project-calculations.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateOutsourcingExpenses: () => (/* binding */ calculateOutsourcingExpenses),\n/* harmony export */   calculateProjectTotal: () => (/* binding */ calculateProjectTotal),\n/* harmony export */   calculateVendorPaymentStatus: () => (/* binding */ calculateVendorPaymentStatus),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   getGstBreakdown: () => (/* binding */ getGstBreakdown),\n/* harmony export */   getUpdatedProjectAmounts: () => (/* binding */ getUpdatedProjectAmounts)\n/* harmony export */ });\n// GST rate (18% in India)\nconst GST_RATE = 0.18;\n/**\n * Calculate project totals based on shoots, client GST status, expenses, and outsourcing costs\n *\n * IMPORTANT: Outsourcing costs are handled in two ways:\n * 1. If vendor payments are recorded as expenses (recommended), they are included in the expenses parameter\n * 2. If vendor payments are not yet recorded, they are calculated from shoot.outsourcing_cost\n *\n * To avoid double-counting, we only subtract outsourcing costs that haven't been recorded as expenses yet.\n */ function calculateProjectTotal(shoots, client, expenses = 0, outsourcingExpenses = 0 // Expenses specifically categorized as \"outsourcing\"\n) {\n    // Calculate subtotal from all shoots\n    const subtotal = shoots.reduce((sum, shoot)=>sum + (shoot.amount || 0), 0);\n    // Calculate total outsourcing costs from all shoots\n    const totalOutsourcingFromShoots = shoots.reduce((sum, shoot)=>{\n        // For legacy single vendor shoots\n        if (shoot.outsourcing_cost) {\n            return sum + shoot.outsourcing_cost;\n        }\n        // For multiple vendor shoots, sum all vendor costs\n        if (shoot.vendors && shoot.vendors.length > 0) {\n            return sum + shoot.vendors.reduce((vendorSum, vendor)=>vendorSum + (vendor.cost || 0), 0);\n        }\n        return sum;\n    }, 0);\n    // Calculate unpaid outsourcing costs (total outsourcing - already paid as expenses)\n    // This prevents double-counting when vendor payments are recorded as expenses\n    const unpaidOutsourcing = Math.max(0, totalOutsourcingFromShoots - outsourcingExpenses);\n    // Calculate GST if client has GST registration\n    const hasGst = client.has_gst;\n    const gstAmount = hasGst ? subtotal * GST_RATE : 0;\n    const total = subtotal + gstAmount;\n    // Calculate profit (total revenue - all expenses - unpaid outsourcing costs)\n    // Note: expenses already include paid outsourcing costs, so we only subtract unpaid ones\n    const profit = total - expenses - unpaidOutsourcing;\n    return {\n        subtotal,\n        gstAmount,\n        total,\n        hasGst,\n        expenses,\n        outsourcing: totalOutsourcingFromShoots,\n        profit\n    };\n}\n/**\n * Update project amounts in the database\n */ function getUpdatedProjectAmounts(shoots, client, amountReceived = 0) {\n    // For project amount calculation, we don't include expenses since this is just for billing\n    const calculation = calculateProjectTotal(shoots, client, 0, 0);\n    return {\n        total_amount: calculation.total,\n        gst_inclusive: calculation.hasGst,\n        amount_received: amountReceived,\n        amount_pending: calculation.total - amountReceived\n    };\n}\n/**\n * Calculate outsourcing expenses from a list of expenses\n */ function calculateOutsourcingExpenses(expenses) {\n    return expenses.filter((expense)=>expense.category === \"outsourcing\").reduce((sum, expense)=>sum + expense.amount, 0);\n}\n/**\n * Format currency for display\n */ function formatCurrency(amount) {\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency: \"INR\",\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n}\n/**\n * Calculate GST breakdown for display\n */ function getGstBreakdown(subtotal, hasGst) {\n    const gstAmount = hasGst ? subtotal * GST_RATE : 0;\n    const total = subtotal + gstAmount;\n    return {\n        subtotal,\n        gstRate: hasGst ? GST_RATE * 100 : 0,\n        gstAmount,\n        total,\n        formattedSubtotal: formatCurrency(subtotal),\n        formattedGstAmount: formatCurrency(gstAmount),\n        formattedTotal: formatCurrency(total)\n    };\n}\n/**\n * Calculate vendor payment status for a project\n */ function calculateVendorPaymentStatus(shoots, outsourcingExpenses) {\n    // Calculate total outsourcing costs from all shoots\n    const totalOwed = shoots.reduce((sum, shoot)=>{\n        // For legacy single vendor shoots\n        if (shoot.outsourcing_cost) {\n            return sum + shoot.outsourcing_cost;\n        }\n        // For multiple vendor shoots, sum all vendor costs\n        if (shoot.vendors && shoot.vendors.length > 0) {\n            return sum + shoot.vendors.reduce((vendorSum, vendor)=>vendorSum + (vendor.cost || 0), 0);\n        }\n        return sum;\n    }, 0);\n    const totalPaid = outsourcingExpenses;\n    const paymentProgress = totalOwed > 0 ? totalPaid / totalOwed * 100 : 0;\n    let status = \"pending\";\n    if (totalPaid >= totalOwed && totalOwed > 0) {\n        status = \"paid\";\n    } else if (totalPaid > 0) {\n        status = \"partial\";\n    }\n    return {\n        totalOwed,\n        totalPaid,\n        status,\n        paymentProgress\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/project-calculations.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/server-tasks.ts":
/*!*********************************!*\
  !*** ./src/lib/server-tasks.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDefaultTasksServerSide: () => (/* binding */ createDefaultTasksServerSide)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _lib_default_tasks__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/default-tasks */ \"(rsc)/./src/lib/default-tasks.ts\");\n\n\nconst supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Server-side task creation function using service role client\nasync function createDefaultTasksServerSide(projectId, clientType, shootDate, shootId, scheduleType) {\n    console.log(\"=== createDefaultTasksServerSide START ===\");\n    console.log(\"Parameters:\", {\n        projectId,\n        clientType,\n        scheduleType,\n        shootDate,\n        shootId\n    });\n    console.log(\"Task type:\", shootId ? \"SHOOT-BASED TASKS\" : \"PROJECT-LEVEL TASKS\");\n    try {\n        // Create Supabase client with service role key\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n        // Get templates based on task type\n        let templates;\n        if (shootId && scheduleType) {\n            // For schedule-based tasks, use schedule type\n            templates = (0,_lib_default_tasks__WEBPACK_IMPORTED_MODULE_0__.getDefaultTasksForScheduleType)(scheduleType);\n            console.log(`Templates found for schedule type \"${scheduleType}\":`, templates.length, \"templates\");\n        } else {\n            // For project-level tasks, use client type\n            templates = (0,_lib_default_tasks__WEBPACK_IMPORTED_MODULE_0__.getDefaultTasksForClientType)(clientType);\n            console.log(`Templates found for client type \"${clientType}\":`, templates.length, \"templates\");\n        }\n        if (templates.length === 0) {\n            console.log(\"No templates found for type:\", shootId ? scheduleType : clientType);\n            return;\n        }\n        // Check if tasks already exist to avoid duplicates\n        let existingTasksQuery = supabase.from(\"tasks\").select(\"id, title, shoot_id\").eq(\"project_id\", projectId);\n        if (shootId) {\n            // For shoot-based tasks, check if tasks exist for this specific shoot\n            existingTasksQuery = existingTasksQuery.eq(\"shoot_id\", shootId);\n        } else {\n            // For project-level tasks, check if project tasks exist (shoot_id is null)\n            existingTasksQuery = existingTasksQuery.is(\"shoot_id\", null);\n        }\n        const { data: existingTasks, error: existingTasksError } = await existingTasksQuery;\n        if (existingTasksError) {\n            console.error(\"Error checking existing tasks:\", existingTasksError);\n            throw existingTasksError;\n        }\n        if (existingTasks && existingTasks.length > 0) {\n            console.log(`Tasks already exist for ${shootId ? \"shoot \" + shootId : \"project \" + projectId}:`);\n            existingTasks.forEach((task)=>{\n                console.log(`  - ${task.title} (${task.shoot_id ? \"shoot task\" : \"project task\"})`);\n            });\n            console.log(\"=== createDefaultTasksServerSide END (SKIPPED - DUPLICATES) ===\");\n            return; // Don't create duplicate tasks\n        }\n        // Get users by role for task assignment\n        const { data: users, error: usersError } = await supabase.from(\"users\").select(\"id, role\");\n        if (usersError) {\n            console.error(\"Error fetching users:\", usersError);\n            throw usersError;\n        }\n        // Group users by role\n        const usersByRole = {};\n        const usersGroupedByRole = users?.reduce((acc, user)=>{\n            if (!acc[user.role]) acc[user.role] = [];\n            acc[user.role].push(user);\n            return acc;\n        }, {}) || {};\n        console.log(\"Users by role:\", Object.keys(usersGroupedByRole).reduce((acc, role)=>{\n            acc[role] = usersGroupedByRole[role].length;\n            return acc;\n        }, {}));\n        // Assign first user of each role (you might want to implement better logic)\n        Object.keys(usersGroupedByRole).forEach((role)=>{\n            if (usersGroupedByRole[role].length > 0) {\n                usersByRole[role] = usersGroupedByRole[role][0].id;\n            }\n        });\n        console.log(\"Role to user mapping:\", usersByRole);\n        // Separate shoot-based and project-level tasks\n        const shootBasedTemplates = templates.filter((t)=>!t.isProjectTask);\n        const projectLevelTemplates = templates.filter((t)=>t.isProjectTask);\n        let createdCount = 0;\n        let skippedCount = 0;\n        // Create shoot-based tasks if shootId is provided\n        if (shootId && shootBasedTemplates.length > 0) {\n            console.log(\"Creating shoot-based tasks for shoot:\", shootId);\n            const shootTaskForms = (0,_lib_default_tasks__WEBPACK_IMPORTED_MODULE_0__.createTasksFromTemplates)(shootBasedTemplates, projectId, usersByRole, shootDate, shootId);\n            for (const taskForm of shootTaskForms){\n                if (taskForm.assigned_to) {\n                    console.log(\"Creating shoot task:\", {\n                        title: taskForm.title,\n                        assigned_to: taskForm.assigned_to,\n                        project_id: taskForm.project_id,\n                        shoot_id: taskForm.shoot_id\n                    });\n                    try {\n                        const { data: createdTask, error: createError } = await supabase.from(\"tasks\").insert(taskForm).select(\"id, title\").single();\n                        if (createError) {\n                            console.error(\"Failed to create shoot task:\", createError);\n                            throw createError;\n                        }\n                        console.log(\"Shoot task created successfully:\", createdTask.id, createdTask.title);\n                        createdCount++;\n                    } catch (taskCreateError) {\n                        console.error(\"Failed to create shoot task:\", taskCreateError);\n                        throw taskCreateError;\n                    }\n                } else {\n                    console.log(\"Skipping shoot task without assigned user:\", taskForm.title);\n                    skippedCount++;\n                }\n            }\n        }\n        // Create project-level tasks only if they don't already exist and only when called without shootId\n        if (projectLevelTemplates.length > 0 && !shootId) {\n            console.log(\"Creating project-level tasks for project:\", projectId);\n            const projectTaskForms = (0,_lib_default_tasks__WEBPACK_IMPORTED_MODULE_0__.createTasksFromTemplates)(projectLevelTemplates, projectId, usersByRole, shootDate, undefined);\n            for (const taskForm of projectTaskForms){\n                if (taskForm.assigned_to) {\n                    console.log(\"Creating project task:\", {\n                        title: taskForm.title,\n                        assigned_to: taskForm.assigned_to,\n                        project_id: taskForm.project_id,\n                        shoot_id: taskForm.shoot_id\n                    });\n                    try {\n                        const { data: createdTask, error: createError } = await supabase.from(\"tasks\").insert(taskForm).select(\"id, title\").single();\n                        if (createError) {\n                            console.error(\"Failed to create project task:\", createError);\n                            throw createError;\n                        }\n                        console.log(\"Project task created successfully:\", createdTask.id, createdTask.title);\n                        createdCount++;\n                    } catch (taskCreateError) {\n                        console.error(\"Failed to create project task:\", taskCreateError);\n                        throw taskCreateError;\n                    }\n                } else {\n                    console.log(\"Skipping project task without assigned user:\", taskForm.title);\n                    skippedCount++;\n                }\n            }\n        }\n        console.log(`=== createDefaultTasksServerSide END ===`);\n        console.log(`Summary: ${createdCount} tasks created, ${skippedCount} tasks skipped`);\n        console.log(`Task type: ${shootId ? \"SHOOT-BASED\" : \"PROJECT-LEVEL\"}`);\n        console.log(\"=======================================\");\n    } catch (error) {\n        console.error(\"❌ CRITICAL ERROR in createDefaultTasksServerSide:\", error);\n        console.error(\"Parameters that caused the error:\", {\n            projectId,\n            clientType,\n            shootDate,\n            shootId\n        });\n        console.error(\"Error details:\", {\n            message: error instanceof Error ? error.message : \"Unknown error\",\n            stack: error instanceof Error ? error.stack : undefined\n        });\n        console.log(\"=== createDefaultTasksServerSide END (ERROR) ===\");\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/server-tasks.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fschedules%2Froute&page=%2Fapi%2Fschedules%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fschedules%2Froute.ts&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();