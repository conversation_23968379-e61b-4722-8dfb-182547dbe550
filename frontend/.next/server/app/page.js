/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdmlqYXl5YXNvJTJGRG9jdW1lbnRzJTJGRGV2ZWxvcG1lbnQlMkZjeW1hdGljcyUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGYXBwLXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnZpamF5eWFzbyUyRkRvY3VtZW50cyUyRkRldmVsb3BtZW50JTJGY3ltYXRpY3MlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdmlqYXl5YXNvJTJGRG9jdW1lbnRzJTJGRGV2ZWxvcG1lbnQlMkZjeW1hdGljcyUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ2aWpheXlhc28lMkZEb2N1bWVudHMlMkZEZXZlbG9wbWVudCUyRmN5bWF0aWNzJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdmlqYXl5YXNvJTJGRG9jdW1lbnRzJTJGRGV2ZWxvcG1lbnQlMkZjeW1hdGljcyUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdmlqYXl5YXNvJTJGRG9jdW1lbnRzJTJGRGV2ZWxvcG1lbnQlMkZjeW1hdGljcyUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQW9KO0FBQ3BKO0FBQ0Esb09BQXFKO0FBQ3JKO0FBQ0EsME9BQXdKO0FBQ3hKO0FBQ0Esd09BQXVKO0FBQ3ZKO0FBQ0Esa1BBQTRKO0FBQzVKO0FBQ0Esc1FBQXNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3ltYXRpY3MtZnJvbnRlbmQvPzhmNjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdmlqYXl5YXNvL0RvY3VtZW50cy9EZXZlbG9wbWVudC9jeW1hdGljcy9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy92aWpheXlhc28vRG9jdW1lbnRzL0RldmVsb3BtZW50L2N5bWF0aWNzL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy92aWpheXlhc28vRG9jdW1lbnRzL0RldmVsb3BtZW50L2N5bWF0aWNzL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy92aWpheXlhc28vRG9jdW1lbnRzL0RldmVsb3BtZW50L2N5bWF0aWNzL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3ZpamF5eWFzby9Eb2N1bWVudHMvRGV2ZWxvcG1lbnQvY3ltYXRpY3MvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy92aWpheXlhc28vRG9jdW1lbnRzL0RldmVsb3BtZW50L2N5bWF0aWNzL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fglobal-error-handler.tsx%22%2C%22ids%22%3A%5B%22GlobalErrorHandler%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fperformance-monitor.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fpwa-install.tsx%22%2C%22ids%22%3A%5B%22PWAInstall%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fservice-worker-registration.tsx%22%2C%22ids%22%3A%5B%22ServiceWorkerRegistration%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fglobal-error-handler.tsx%22%2C%22ids%22%3A%5B%22GlobalErrorHandler%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fperformance-monitor.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fpwa-install.tsx%22%2C%22ids%22%3A%5B%22PWAInstall%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fservice-worker-registration.tsx%22%2C%22ids%22%3A%5B%22ServiceWorkerRegistration%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/global-error-handler.tsx */ \"(ssr)/./src/components/ui/global-error-handler.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/performance-monitor.tsx */ \"(ssr)/./src/components/ui/performance-monitor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/pwa-install.tsx */ \"(ssr)/./src/components/ui/pwa-install.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/service-worker-registration.tsx */ \"(ssr)/./src/components/ui/service-worker-registration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(ssr)/./src/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fglobal-error-handler.tsx%22%2C%22ids%22%3A%5B%22GlobalErrorHandler%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fperformance-monitor.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fpwa-install.tsx%22%2C%22ids%22%3A%5B%22PWAInstall%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fservice-worker-registration.tsx%22%2C%22ids%22%3A%5B%22ServiceWorkerRegistration%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdmlqYXl5YXNvJTJGRG9jdW1lbnRzJTJGRGV2ZWxvcG1lbnQlMkZjeW1hdGljcyUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUE4RyIsInNvdXJjZXMiOlsid2VicGFjazovL2N5bWF0aWNzLWZyb250ZW5kLz85MzliIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3ZpamF5eWFzby9Eb2N1bWVudHMvRGV2ZWxvcG1lbnQvY3ltYXRpY3MvZnJvbnRlbmQvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        console.log(\"Home page useEffect:\", {\n            loading,\n            user: !!user\n        });\n        if (!loading && user) {\n            console.log(\"Redirecting to dashboard...\");\n            router.push(\"/dashboard\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        console.log(\"Still loading...\");\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-xs text-muted-foreground\",\n                        children: [\n                            \"Debug: loading=\",\n                            loading.toString(),\n                            \", user=\",\n                            user ? \"exists\" : \"null\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this);\n    }\n    if (user) {\n        return null // Will redirect to dashboard\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background flex items-center justify-center p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-foreground mb-4\",\n                            children: \"Welcome to Cymatics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-muted-foreground mb-8\",\n                            children: \"Streamline your drone service operations with comprehensive project and client management\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-card rounded-lg p-6 shadow-lg border border-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-accent rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6 text-blue-600 dark:text-blue-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-foreground mb-2\",\n                                    children: \"Project Management\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Organize and track all your drone projects with detailed client information and location mapping.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-card rounded-lg p-6 shadow-lg border border-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-accent rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6 text-green-600 dark:text-green-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-foreground mb-2\",\n                                    children: \"Shoot Scheduling\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Schedule and manage drone shoots with automated reminders and recurring appointments.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-card rounded-lg p-6 shadow-lg border border-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-accent rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6 text-purple-600 dark:text-purple-400\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-foreground mb-2\",\n                                    children: \"Financial Tracking\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Monitor payments, expenses, and billing with GST support and comprehensive reporting.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        href: \"/login\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"border-2\",\n                            children: \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-sm text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Built with Next.js, TypeScript, and Supabase\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90 border border-transparent\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90 border border-transparent\",\n            outline: \"border border-input bg-background text-foreground hover:bg-muted\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-secondary\",\n            ghost: \"text-foreground hover:bg-muted\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/button.tsx\",\n        lineNumber: 43,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/global-error-handler.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/global-error-handler.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalErrorHandler: () => (/* binding */ GlobalErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ GlobalErrorHandler auto */ \n\n/**\n * Global Error Handler Component\n * Catches and handles unhandled errors, including external API calls\n */ function GlobalErrorHandler() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Handle unhandled promise rejections\n        const handleUnhandledRejection = (event)=>{\n            console.warn(\"Unhandled promise rejection:\", event.reason);\n            // Check if it's a webviewClick related error\n            if (event.reason?.message?.includes(\"webviewClick\") || event.reason?.message?.includes(\"请求超时\")) {\n                // Silently handle webviewClick errors as they're likely from external sources\n                event.preventDefault();\n                console.log(\"Handled external webviewClick error\");\n                return;\n            }\n            // For other errors, show a toast notification\n            if (event.reason?.message && !event.reason.message.includes(\"ChunkLoadError\")) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"An unexpected error occurred. Please try again.\");\n            }\n        };\n        // Handle general JavaScript errors\n        const handleError = (event)=>{\n            console.warn(\"Global error:\", event.error);\n            // Check if it's a webviewClick related error\n            if (event.error?.message?.includes(\"webviewClick\") || event.error?.message?.includes(\"请求超时\")) {\n                // Silently handle webviewClick errors\n                event.preventDefault();\n                console.log(\"Handled external webviewClick error\");\n                return;\n            }\n            // For other errors, show a toast notification\n            if (event.error?.message && !event.error.message.includes(\"ChunkLoadError\")) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"An unexpected error occurred. Please try again.\");\n            }\n        };\n        // Add event listeners\n        window.addEventListener(\"unhandledrejection\", handleUnhandledRejection);\n        window.addEventListener(\"error\", handleError);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"unhandledrejection\", handleUnhandledRejection);\n            window.removeEventListener(\"error\", handleError);\n        };\n    }, []);\n    return null // This component doesn't render anything\n    ;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/global-error-handler.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/performance-monitor.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/performance-monitor.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceMonitor: () => (/* binding */ PerformanceMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ PerformanceMonitor auto */ \nfunction PerformanceMonitor() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (false) {}\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/performance-monitor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/pwa-install.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/pwa-install.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PWAInstall: () => (/* binding */ PWAInstall),\n/* harmony export */   usePWAInstall: () => (/* binding */ usePWAInstall)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Smartphone,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ PWAInstall,usePWAInstall auto */ \n\n\n\nfunction PWAInstall() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInstallPrompt, setShowInstallPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInstalled, setIsInstalled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isIOS, setIsIOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStandalone, setIsStandalone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if app is already installed\n        const checkIfInstalled = ()=>{\n            const isStandaloneMode = window.matchMedia(\"(display-mode: standalone)\").matches;\n            const isIOSStandalone = window.navigator.standalone === true;\n            setIsStandalone(isStandaloneMode || isIOSStandalone);\n            setIsInstalled(isStandaloneMode || isIOSStandalone);\n        };\n        // Check if iOS\n        const checkIfIOS = ()=>{\n            const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent);\n            setIsIOS(isIOSDevice);\n        };\n        checkIfInstalled();\n        checkIfIOS();\n        // Listen for beforeinstallprompt event\n        const handleBeforeInstallPrompt = (e)=>{\n            e.preventDefault();\n            setDeferredPrompt(e);\n            setShowInstallPrompt(true);\n        };\n        // Listen for app installed event\n        const handleAppInstalled = ()=>{\n            setIsInstalled(true);\n            setShowInstallPrompt(false);\n            setDeferredPrompt(null);\n            console.log(\"PWA was installed\");\n        };\n        window.addEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n        window.addEventListener(\"appinstalled\", handleAppInstalled);\n        return ()=>{\n            window.removeEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n            window.removeEventListener(\"appinstalled\", handleAppInstalled);\n        };\n    }, [\n        isInstalled\n    ]);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        try {\n            await deferredPrompt.prompt();\n            const { outcome } = await deferredPrompt.userChoice;\n            if (outcome === \"accepted\") {\n                console.log(\"User accepted the install prompt\");\n            } else {\n                console.log(\"User dismissed the install prompt\");\n            }\n            setDeferredPrompt(null);\n            setShowInstallPrompt(false);\n        } catch (error) {\n            console.error(\"Error during installation:\", error);\n        }\n    };\n    const handleDismiss = ()=>{\n        setShowInstallPrompt(false);\n        // Safely store dismissal state\n        try {\n            if (false) {}\n        } catch (error) {\n            console.warn(\"Failed to save PWA install dismissal state:\", error);\n        }\n    };\n    // Safely check dismissal state\n    const isDismissed = ()=>{\n        try {\n            if (false) {}\n        } catch (error) {\n            console.warn(\"Failed to read PWA install dismissal state:\", error);\n        }\n        return false;\n    };\n    // Don't show if already installed, dismissed, or not ready\n    if (isInstalled || isStandalone || isDismissed() || !showInstallPrompt) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 dark:text-gray-100\",\n                                    children: \"Install App\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleDismiss,\n                            className: \"h-6 w-6 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                    children: \"Install Cymatics for quick access and a better experience.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                isIOS ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-2\",\n                            children: \"To install on iOS:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside space-y-1 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Tap the Share button in Safari\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: 'Scroll down and tap \"Add to Home Screen\"'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: 'Tap \"Add\" to confirm'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: handleInstallClick,\n                            size: \"sm\",\n                            className: \"flex-1\",\n                            disabled: !deferredPrompt,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Smartphone_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                \"Install\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: handleDismiss,\n                            children: \"Later\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n// Hook to check PWA installation status\nfunction usePWAInstall() {\n    const [isInstalled, setIsInstalled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [canInstall, setCanInstall] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkInstallStatus = ()=>{\n            const isStandaloneMode = window.matchMedia(\"(display-mode: standalone)\").matches;\n            const isIOSStandalone = window.navigator.standalone === true;\n            setIsInstalled(isStandaloneMode || isIOSStandalone);\n        };\n        const handleBeforeInstallPrompt = ()=>{\n            setCanInstall(true);\n        };\n        const handleAppInstalled = ()=>{\n            setIsInstalled(true);\n            setCanInstall(false);\n        };\n        checkInstallStatus();\n        window.addEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n        window.addEventListener(\"appinstalled\", handleAppInstalled);\n        return ()=>{\n            window.removeEventListener(\"beforeinstallprompt\", handleBeforeInstallPrompt);\n            window.removeEventListener(\"appinstalled\", handleAppInstalled);\n        };\n    }, []);\n    return {\n        isInstalled,\n        canInstall\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/pwa-install.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/service-worker-registration.tsx":
/*!***********************************************************!*\
  !*** ./src/components/ui/service-worker-registration.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceWorkerRegistration: () => (/* binding */ ServiceWorkerRegistration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ ServiceWorkerRegistration auto */ \nfunction ServiceWorkerRegistration() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Register service worker for performance optimization\n        if (false) {}\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9zZXJ2aWNlLXdvcmtlci1yZWdpc3RyYXRpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7OzsrRUFFaUM7QUFFMUIsU0FBU0M7SUFDZEQsZ0RBQVNBLENBQUM7UUFDUix1REFBdUQ7UUFDdkQsSUFBSSxLQUE2REUsRUFBRSxFQWlCbEU7SUFDSCxHQUFHLEVBQUU7SUFFTCxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jeW1hdGljcy1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL3VpL3NlcnZpY2Utd29ya2VyLXJlZ2lzdHJhdGlvbi50c3g/YmIyMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5cbmV4cG9ydCBmdW5jdGlvbiBTZXJ2aWNlV29ya2VyUmVnaXN0cmF0aW9uKCkge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFJlZ2lzdGVyIHNlcnZpY2Ugd29ya2VyIGZvciBwZXJmb3JtYW5jZSBvcHRpbWl6YXRpb25cbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgJ3NlcnZpY2VXb3JrZXInIGluIG5hdmlnYXRvcikge1xuICAgICAgLy8gVXNlIHJlcXVlc3RJZGxlQ2FsbGJhY2sgdG8gZGVmZXIgcmVnaXN0cmF0aW9uXG4gICAgICBjb25zdCByZWdpc3RlclNXID0gKCkgPT4ge1xuICAgICAgICBuYXZpZ2F0b3Iuc2VydmljZVdvcmtlci5yZWdpc3RlcignL3N3LmpzJylcbiAgICAgICAgICAudGhlbigocmVnaXN0cmF0aW9uKSA9PiB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygnU1cgcmVnaXN0ZXJlZDogJywgcmVnaXN0cmF0aW9uKVxuICAgICAgICAgIH0pXG4gICAgICAgICAgLmNhdGNoKChyZWdpc3RyYXRpb25FcnJvcikgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ1NXIHJlZ2lzdHJhdGlvbiBmYWlsZWQ6ICcsIHJlZ2lzdHJhdGlvbkVycm9yKVxuICAgICAgICAgIH0pXG4gICAgICB9XG5cbiAgICAgIGlmICgncmVxdWVzdElkbGVDYWxsYmFjaycgaW4gd2luZG93KSB7XG4gICAgICAgIHJlcXVlc3RJZGxlQ2FsbGJhY2socmVnaXN0ZXJTVylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFRpbWVvdXQocmVnaXN0ZXJTVywgMTAwMClcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtdKVxuXG4gIHJldHVybiBudWxsXG59Il0sIm5hbWVzIjpbInVzZUVmZmVjdCIsIlNlcnZpY2VXb3JrZXJSZWdpc3RyYXRpb24iLCJuYXZpZ2F0b3IiLCJyZWdpc3RlclNXIiwic2VydmljZVdvcmtlciIsInJlZ2lzdGVyIiwidGhlbiIsInJlZ2lzdHJhdGlvbiIsImNvbnNvbGUiLCJsb2ciLCJjYXRjaCIsInJlZ2lzdHJhdGlvbkVycm9yIiwid2luZG93IiwicmVxdWVzdElkbGVDYWxsYmFjayIsInNldFRpbWVvdXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/service-worker-registration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            try {\n                console.log(\"Getting initial session...\");\n                const { data: { session }, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Initial session:\", session ? \"Found\" : \"None\");\n                setSession(session);\n                if (session?.user) {\n                    await fetchUserProfile(session.user.id);\n                } else {\n                    setLoading(false);\n                }\n            } catch (error) {\n                console.error(\"Error in getInitialSession:\", error);\n                setLoading(false);\n            }\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state change:\", event, session ? \"Session exists\" : \"No session\");\n            setSession(session);\n            if (session?.user) {\n                await fetchUserProfile(session.user.id);\n            } else {\n                setUser(null);\n                setLoading(false);\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        supabase.auth\n    ]);\n    const fetchUserProfile = async (userId)=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D AuthContext: Fetching user profile for:\", userId);\n            const { data: profile, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user profile:\", error);\n                // If user doesn't exist in users table, create a basic user object\n                console.log(\"User not found in users table, using basic auth user data\");\n                const { data: { user: authUser } } = await supabase.auth.getUser();\n                if (authUser) {\n                    setUser({\n                        id: authUser.id,\n                        email: authUser.email || \"\",\n                        name: authUser.user_metadata?.name || authUser.email || \"\",\n                        role: \"user\",\n                        avatar_url: authUser.user_metadata?.avatar_url || null,\n                        created_at: authUser.created_at,\n                        updated_at: authUser.updated_at || authUser.created_at\n                    });\n                }\n                setLoading(false);\n                return;\n            }\n            if (profile) {\n                console.log(\"✅ AuthContext: User profile loaded:\", {\n                    id: profile.id,\n                    email: profile.email,\n                    name: profile.name,\n                    role: profile.role\n                });\n                setUser({\n                    id: profile.id,\n                    email: profile.email,\n                    name: profile.name,\n                    role: profile.role,\n                    avatar_url: profile.avatar_url,\n                    created_at: profile.created_at,\n                    updated_at: profile.updated_at\n                });\n            }\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error fetching user profile:\", error);\n            setLoading(false);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                console.error(\"Sign in error:\", error);\n                setLoading(false);\n                return {\n                    success: false,\n                    error: error.message\n                };\n            }\n            console.log(\"Sign in successful:\", data);\n            setLoading(false);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Sign in failed:\", error);\n            setLoading(false);\n            return {\n                success: false,\n                error: error.message || \"Failed to sign in\"\n            };\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                throw error;\n            }\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const resetPassword = async (email)=>{\n        const { error } = await supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        if (error) {\n            throw error;\n        }\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            throw new Error(\"No authenticated user\");\n        }\n        // Create a clean update object without undefined values\n        const cleanUpdates = {};\n        Object.entries(updates).forEach(([key, value])=>{\n            if (value !== undefined) {\n                cleanUpdates[key] = value;\n            }\n        });\n        const { error } = await supabase.from(\"users\").update(cleanUpdates).eq(\"id\", user.id);\n        if (error) {\n            throw error;\n        }\n        // Update local user state\n        setUser((prev)=>prev ? {\n                ...prev,\n                ...updates\n            } : null);\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signIn,\n        signOut,\n        resetPassword,\n        updateProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/contexts/AuthContext.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"system\");\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        // Load theme from localStorage on mount\n        const savedTheme = localStorage.getItem(\"theme\");\n        if (savedTheme && [\n            \"light\",\n            \"dark\",\n            \"system\"\n        ].includes(savedTheme)) {\n            setTheme(savedTheme);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const updateTheme = ()=>{\n            let shouldBeDark = false;\n            if (theme === \"dark\") {\n                shouldBeDark = true;\n            } else if (theme === \"light\") {\n                shouldBeDark = false;\n            } else {\n                // System theme\n                shouldBeDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n            }\n            setIsDark(shouldBeDark);\n            // Update document class\n            const root = document.documentElement;\n            if (shouldBeDark) {\n                root.classList.add(\"dark\");\n                root.style.colorScheme = \"dark\";\n            } else {\n                root.classList.remove(\"dark\");\n                root.style.colorScheme = \"light\";\n            }\n            // Save to localStorage\n            localStorage.setItem(\"theme\", theme);\n        };\n        // Small delay to ensure DOM is ready\n        const timer = setTimeout(updateTheme, 0);\n        // Listen for system theme changes\n        const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        const handleChange = ()=>{\n            if (theme === \"system\") {\n                updateTheme();\n            }\n        };\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>{\n            clearTimeout(timer);\n            mediaQuery.removeEventListener(\"change\", handleChange);\n        };\n    }, [\n        theme,\n        mounted\n    ]);\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n            value: {\n                theme: \"system\",\n                setTheme,\n                isDark: false\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/contexts/ThemeContext.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            setTheme,\n            isDark\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/contexts/ThemeContext.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabaseClient: () => (/* binding */ createClientSupabaseClient),\n/* harmony export */   hasMinimumRole: () => (/* binding */ hasMinimumRole),\n/* harmony export */   hasRole: () => (/* binding */ hasRole)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\nconst supabaseUrl = \"https://qxgfulribhhohmggyroq.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDgxMjksImV4cCI6MjA2ODU4NDEyOX0.lxP-ec7eabdAMPzgwIUZurLzXhGTK4kIeIIn5j_nnEU\";\n// Singleton Supabase client instance\nlet supabaseClient = null;\n// Client-side Supabase client (singleton) with SSR support\nfunction createClientSupabaseClient() {\n    if (!supabaseUrl || !supabaseAnonKey) {\n        console.error(\"Missing Supabase environment variables:\", {\n            url: !!supabaseUrl,\n            key: !!supabaseAnonKey\n        });\n        throw new Error(\"Missing Supabase environment variables\");\n    }\n    // Return existing client if already created\n    if (supabaseClient) {\n        return supabaseClient;\n    }\n    // Create new SSR-compatible browser client\n    supabaseClient = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey);\n    return supabaseClient;\n}\n// Check if user has required role\nfunction hasRole(userRole, requiredRoles) {\n    return requiredRoles.includes(userRole);\n}\n// Role hierarchy for permission checking\nconst roleHierarchy = {\n    admin: 4,\n    manager: 3,\n    pilot: 2,\n    editor: 1\n};\n// Check if user has minimum role level\nfunction hasMinimumRole(userRole, minimumRole) {\n    return roleHierarchy[userRole] >= roleHierarchy[minimumRole];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   generateId: () => (/* binding */ generateId)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"INR\") {\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(Math.round(amount));\n}\nfunction formatDate(date) {\n    const d = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(\"en-IN\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\"\n    }).format(d);\n}\nfunction formatDateTime(date) {\n    const d = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(\"en-IN\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(d);\n}\nfunction generateId() {\n    return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"176b3a04d219\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3ltYXRpY3MtZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzBkN2IiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxNzZiM2EwNGQyMTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _components_ui_pwa_install__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pwa-install */ \"(rsc)/./src/components/ui/pwa-install.tsx\");\n/* harmony import */ var _components_ui_service_worker_registration__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/service-worker-registration */ \"(rsc)/./src/components/ui/service-worker-registration.tsx\");\n/* harmony import */ var _components_ui_performance_monitor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/performance-monitor */ \"(rsc)/./src/components/ui/performance-monitor.tsx\");\n/* harmony import */ var _components_ui_global_error_handler__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/global-error-handler */ \"(rsc)/./src/components/ui/global-error-handler.tsx\");\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Cymatics - Drone Service Management\",\n    description: \"Streamline your drone service operations with comprehensive project and client management\",\n    manifest: \"/manifest.json\",\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"Cymatics\"\n    },\n    formatDetection: {\n        telephone: false\n    },\n    openGraph: {\n        type: \"website\",\n        siteName: \"Cymatics\",\n        title: \"Cymatics - Drone Service Management\",\n        description: \"Streamline your drone service operations with comprehensive project and client management\"\n    },\n    twitter: {\n        card: \"summary\",\n        title: \"Cymatics - Drone Service Management\",\n        description: \"Streamline your drone service operations with comprehensive project and client management\"\n    },\n    icons: {\n        icon: \"/icon-192x192.svg\",\n        shortcut: \"/icon-192x192.svg\",\n        apple: \"/icon-192x192.svg\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false,\n    themeColor: \"#3b82f6\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `h-full ${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_9___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans h-full bg-background text-foreground antialiased\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pwa_install__WEBPACK_IMPORTED_MODULE_5__.PWAInstall, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_service_worker_registration__WEBPACK_IMPORTED_MODULE_6__.ServiceWorkerRegistration, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_performance_monitor__WEBPACK_IMPORTED_MODULE_7__.PerformanceMonitor, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_global_error_handler__WEBPACK_IMPORTED_MODULE_8__.GlobalErrorHandler, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/app/layout.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/app/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ui/global-error-handler.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/global-error-handler.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GlobalErrorHandler: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/global-error-handler.tsx#GlobalErrorHandler`);


/***/ }),

/***/ "(rsc)/./src/components/ui/performance-monitor.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/performance-monitor.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PerformanceMonitor: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/performance-monitor.tsx#PerformanceMonitor`);


/***/ }),

/***/ "(rsc)/./src/components/ui/pwa-install.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/pwa-install.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PWAInstall: () => (/* binding */ e0),
/* harmony export */   usePWAInstall: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx#PWAInstall`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/pwa-install.tsx#usePWAInstall`);


/***/ }),

/***/ "(rsc)/./src/components/ui/service-worker-registration.tsx":
/*!***********************************************************!*\
  !*** ./src/components/ui/service-worker-registration.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ServiceWorkerRegistration: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/service-worker-registration.tsx#ServiceWorkerRegistration`);


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/contexts/AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/contexts/AuthContext.tsx#AuthProvider`);


/***/ }),

/***/ "(rsc)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/contexts/ThemeContext.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/cymatics/frontend/src/contexts/ThemeContext.tsx#useTheme`);


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jeW1hdGljcy1mcm9udGVuZC8uL3NyYy9hcHAvZmF2aWNvbi5pY28/MDkyYSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/lucide-react","vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/tailwind-merge","vendor-chunks/react-hot-toast","vendor-chunks/cookie","vendor-chunks/class-variance-authority","vendor-chunks/goober","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvijayyaso%2FDocuments%2FDevelopment%2Fcymatics%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();