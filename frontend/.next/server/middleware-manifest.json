{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "LZ/kq/h9H2ZFi3gvWvcBT3hp6Y5Qj2BddGAU2HEyU0o="}}}, "functions": {}, "sortedMiddleware": ["/"]}