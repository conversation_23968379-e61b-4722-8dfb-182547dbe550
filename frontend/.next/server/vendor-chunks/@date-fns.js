"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@date-fns";
exports.ids = ["vendor-chunks/@date-fns"];
exports.modules = {

/***/ "(ssr)/./node_modules/@date-fns/tz/constants/index.js":
/*!******************************************************!*\
  !*** ./node_modules/@date-fns/tz/constants/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constructFromSymbol: () => (/* binding */ constructFromSymbol)\n/* harmony export */ });\n/**\n * The symbol to access the `TZDate`'s function to construct a new instance from\n * the provided value. It helps date-fns to inherit the time zone.\n */\nconst constructFromSymbol = Symbol.for(\"constructDateFrom\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRhdGUtZm5zL3R6L2NvbnN0YW50cy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL2N5bWF0aWNzLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BkYXRlLWZucy90ei9jb25zdGFudHMvaW5kZXguanM/ODAyNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFRoZSBzeW1ib2wgdG8gYWNjZXNzIHRoZSBgVFpEYXRlYCdzIGZ1bmN0aW9uIHRvIGNvbnN0cnVjdCBhIG5ldyBpbnN0YW5jZSBmcm9tXG4gKiB0aGUgcHJvdmlkZWQgdmFsdWUuIEl0IGhlbHBzIGRhdGUtZm5zIHRvIGluaGVyaXQgdGhlIHRpbWUgem9uZS5cbiAqL1xuZXhwb3J0IGNvbnN0IGNvbnN0cnVjdEZyb21TeW1ib2wgPSBTeW1ib2wuZm9yKFwiY29uc3RydWN0RGF0ZUZyb21cIik7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/constants/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/date/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@date-fns/tz/date/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDate: () => (/* binding */ TZDate)\n/* harmony export */ });\n/* harmony import */ var _tzName_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../tzName/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzName/index.js\");\n/* harmony import */ var _mini_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mini.js */ \"(ssr)/./node_modules/@date-fns/tz/date/mini.js\");\n\n\nclass TZDate extends _mini_js__WEBPACK_IMPORTED_MODULE_1__.TZDateMini {\n  //#region static\n\n  static tz(tz, ...args) {\n    return args.length ? new TZDate(...args, tz) : new TZDate(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region representation\n\n  toISOString() {\n    const [sign, hours, minutes] = this.tzComponents();\n    const tz = `${sign}${hours}:${minutes}`;\n    return this.internal.toISOString().slice(0, -1) + tz;\n  }\n  toString() {\n    // \"Tue Aug 13 2024 07:50:19 GMT+0800 (Singapore Standard Time)\";\n    return `${this.toDateString()} ${this.toTimeString()}`;\n  }\n  toDateString() {\n    // toUTCString returns RFC 7231 (\"Mon, 12 Aug 2024 23:36:08 GMT\")\n    const [day, date, month, year] = this.internal.toUTCString().split(\" \");\n    // \"Tue Aug 13 2024\"\n    return `${day?.slice(0, -1) /* Remove \",\" */} ${month} ${date} ${year}`;\n  }\n  toTimeString() {\n    // toUTCString returns RFC 7231 (\"Mon, 12 Aug 2024 23:36:08 GMT\")\n    const time = this.internal.toUTCString().split(\" \")[4];\n    const [sign, hours, minutes] = this.tzComponents();\n    // \"07:42:23 GMT+0800 (Singapore Standard Time)\"\n    return `${time} GMT${sign}${hours}${minutes} (${(0,_tzName_index_js__WEBPACK_IMPORTED_MODULE_0__.tzName)(this.timeZone, this)})`;\n  }\n  toLocaleString(locales, options) {\n    return Date.prototype.toLocaleString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n  toLocaleDateString(locales, options) {\n    return Date.prototype.toLocaleDateString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n  toLocaleTimeString(locales, options) {\n    return Date.prototype.toLocaleTimeString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n\n  //#endregion\n\n  //#region private\n\n  tzComponents() {\n    const offset = this.getTimezoneOffset();\n    const sign = offset > 0 ? \"-\" : \"+\";\n    const hours = String(Math.floor(Math.abs(offset) / 60)).padStart(2, \"0\");\n    const minutes = String(Math.abs(offset) % 60).padStart(2, \"0\");\n    return [sign, hours, minutes];\n  }\n\n  //#endregion\n\n  withTimeZone(timeZone) {\n    return new TZDate(+this, timeZone);\n  }\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDate(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/date/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/date/mini.js":
/*!************************************************!*\
  !*** ./node_modules/@date-fns/tz/date/mini.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDateMini: () => (/* binding */ TZDateMini)\n/* harmony export */ });\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../tzOffset/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js\");\n\nclass TZDateMini extends Date {\n  //#region static\n\n  constructor(...args) {\n    super();\n    if (args.length > 1 && typeof args[args.length - 1] === \"string\") {\n      this.timeZone = args.pop();\n    }\n    this.internal = new Date();\n    if (isNaN((0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(this.timeZone, this))) {\n      this.setTime(NaN);\n    } else {\n      if (!args.length) {\n        this.setTime(Date.now());\n      } else if (typeof args[0] === \"number\" && (args.length === 1 || args.length === 2 && typeof args[1] !== \"number\")) {\n        this.setTime(args[0]);\n      } else if (typeof args[0] === \"string\") {\n        this.setTime(+new Date(args[0]));\n      } else if (args[0] instanceof Date) {\n        this.setTime(+args[0]);\n      } else {\n        this.setTime(+new Date(...args));\n        adjustToSystemTZ(this, NaN);\n        syncToInternal(this);\n      }\n    }\n  }\n  static tz(tz, ...args) {\n    return args.length ? new TZDateMini(...args, tz) : new TZDateMini(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region time zone\n\n  withTimeZone(timeZone) {\n    return new TZDateMini(+this, timeZone);\n  }\n  getTimezoneOffset() {\n    const offset = -(0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(this.timeZone, this);\n    // Remove the seconds offset\n    // use Math.floor for negative GMT timezones and Math.ceil for positive GMT timezones.\n    return offset > 0 ? Math.floor(offset) : Math.ceil(offset);\n  }\n\n  //#endregion\n\n  //#region time\n\n  setTime(time) {\n    Date.prototype.setTime.apply(this, arguments);\n    syncToInternal(this);\n    return +this;\n  }\n\n  //#endregion\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDateMini(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}\n\n// Assign getters and setters\nconst re = /^(get|set)(?!UTC)/;\nObject.getOwnPropertyNames(Date.prototype).forEach(method => {\n  if (!re.test(method)) return;\n  const utcMethod = method.replace(re, \"$1UTC\");\n  // Filter out methods without UTC counterparts\n  if (!TZDateMini.prototype[utcMethod]) return;\n  if (method.startsWith(\"get\")) {\n    // Delegate to internal date's UTC method\n    TZDateMini.prototype[method] = function () {\n      return this.internal[utcMethod]();\n    };\n  } else {\n    // Assign regular setter\n    TZDateMini.prototype[method] = function () {\n      Date.prototype[utcMethod].apply(this.internal, arguments);\n      syncFromInternal(this);\n      return +this;\n    };\n\n    // Assign UTC setter\n    TZDateMini.prototype[utcMethod] = function () {\n      Date.prototype[utcMethod].apply(this, arguments);\n      syncToInternal(this);\n      return +this;\n    };\n  }\n});\n\n/**\n * Function syncs time to internal date, applying the time zone offset.\n *\n * @param {Date} date - Date to sync\n */\nfunction syncToInternal(date) {\n  date.internal.setTime(+date);\n  date.internal.setUTCSeconds(date.internal.getUTCSeconds() - Math.round(-(0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date) * 60));\n}\n\n/**\n * Function syncs the internal date UTC values to the date. It allows to get\n * accurate timestamp value.\n *\n * @param {Date} date - The date to sync\n */\nfunction syncFromInternal(date) {\n  // First we transpose the internal values\n  Date.prototype.setFullYear.call(date, date.internal.getUTCFullYear(), date.internal.getUTCMonth(), date.internal.getUTCDate());\n  Date.prototype.setHours.call(date, date.internal.getUTCHours(), date.internal.getUTCMinutes(), date.internal.getUTCSeconds(), date.internal.getUTCMilliseconds());\n\n  // Now we have to adjust the date to the system time zone\n  adjustToSystemTZ(date);\n}\n\n/**\n * Function adjusts the date to the system time zone. It uses the time zone\n * differences to calculate the offset and adjust the date.\n *\n * @param {Date} date - Date to adjust\n */\nfunction adjustToSystemTZ(date) {\n  // Save the time zone offset before all the adjustments\n  const baseOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n  // Remove the seconds offset\n  // use Math.floor for negative GMT timezones and Math.ceil for positive GMT timezones.\n  const offset = baseOffset > 0 ? Math.floor(baseOffset) : Math.ceil(baseOffset);\n  //#region System DST adjustment\n\n  // The biggest problem with using the system time zone is that when we create\n  // a date from internal values stored in UTC, the system time zone might end\n  // up on the DST hour:\n  //\n  //   $ TZ=America/New_York node\n  //   > new Date(2020, 2, 8, 1).toString()\n  //   'Sun Mar 08 2020 01:00:00 GMT-0500 (Eastern Standard Time)'\n  //   > new Date(2020, 2, 8, 2).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 3).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 4).toString()\n  //   'Sun Mar 08 2020 04:00:00 GMT-0400 (Eastern Daylight Time)'\n  //\n  // Here we get the same hour for both 2 and 3, because the system time zone\n  // has DST beginning at 8 March 2020, 2 a.m. and jumps to 3 a.m. So we have\n  // to adjust the internal date to reflect that.\n  //\n  // However we want to adjust only if that's the DST hour the change happenes,\n  // not the hour where DST moves to.\n\n  // We calculate the previous hour to see if the time zone offset has changed\n  // and we have landed on the DST hour.\n  const prevHour = new Date(+date);\n  // We use UTC methods here as we don't want to land on the same hour again\n  // in case of DST.\n  prevHour.setUTCHours(prevHour.getUTCHours() - 1);\n\n  // Calculate if we are on the system DST hour.\n  const systemOffset = -new Date(+date).getTimezoneOffset();\n  const prevHourSystemOffset = -new Date(+prevHour).getTimezoneOffset();\n  const systemDSTChange = systemOffset - prevHourSystemOffset;\n  // Detect the DST shift. System DST change will occur both on\n  const dstShift = Date.prototype.getHours.apply(date) !== date.internal.getUTCHours();\n\n  // Move the internal date when we are on the system DST hour.\n  if (systemDSTChange && dstShift) date.internal.setUTCMinutes(date.internal.getUTCMinutes() + systemDSTChange);\n\n  //#endregion\n\n  //#region System diff adjustment\n\n  // Now we need to adjust the date, since we just applied internal values.\n  // We need to calculate the difference between the system and date time zones\n  // and apply it to the date.\n\n  const offsetDiff = systemOffset - offset;\n  if (offsetDiff) Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetDiff);\n\n  //#endregion\n\n  //#region Seconds System diff adjustment\n\n  const systemDate = new Date(+date);\n  // Set the UTC seconds to 0 to isolate the timezone offset in seconds.\n  systemDate.setUTCSeconds(0);\n  // For negative systemOffset, invert the seconds.\n  const systemSecondsOffset = systemOffset > 0 ? systemDate.getSeconds() : (systemDate.getSeconds() - 60) % 60;\n\n  // Calculate the seconds offset based on the timezone offset.\n  const secondsOffset = Math.round(-((0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date) * 60)) % 60;\n  if (secondsOffset || systemSecondsOffset) {\n    date.internal.setUTCSeconds(date.internal.getUTCSeconds() + secondsOffset);\n    Date.prototype.setUTCSeconds.call(date, Date.prototype.getUTCSeconds.call(date) + secondsOffset + systemSecondsOffset);\n  }\n\n  //#endregion\n\n  //#region Post-adjustment DST fix\n\n  const postBaseOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n  // Remove the seconds offset\n  // use Math.floor for negative GMT timezones and Math.ceil for positive GMT timezones.\n  const postOffset = postBaseOffset > 0 ? Math.floor(postBaseOffset) : Math.ceil(postBaseOffset);\n  const postSystemOffset = -new Date(+date).getTimezoneOffset();\n  const postOffsetDiff = postSystemOffset - postOffset;\n  const offsetChanged = postOffset !== offset;\n  const postDiff = postOffsetDiff - offsetDiff;\n  if (offsetChanged && postDiff) {\n    Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + postDiff);\n\n    // Now we need to check if got offset change during the post-adjustment.\n    // If so, we also need both dates to reflect that.\n\n    const newBaseOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n    // Remove the seconds offset\n    // use Math.floor for negative GMT timezones and Math.ceil for positive GMT timezones.\n    const newOffset = newBaseOffset > 0 ? Math.floor(newBaseOffset) : Math.ceil(newBaseOffset);\n    const offsetChange = postOffset - newOffset;\n    if (offsetChange) {\n      date.internal.setUTCMinutes(date.internal.getUTCMinutes() + offsetChange);\n      Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetChange);\n    }\n  }\n\n  //#endregion\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/date/mini.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/index.js":
/*!********************************************!*\
  !*** ./node_modules/@date-fns/tz/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDate: () => (/* reexport safe */ _date_index_js__WEBPACK_IMPORTED_MODULE_1__.TZDate),\n/* harmony export */   TZDateMini: () => (/* reexport safe */ _date_mini_js__WEBPACK_IMPORTED_MODULE_2__.TZDateMini),\n/* harmony export */   constructFromSymbol: () => (/* reexport safe */ _constants_index_js__WEBPACK_IMPORTED_MODULE_0__.constructFromSymbol),\n/* harmony export */   tz: () => (/* reexport safe */ _tz_index_js__WEBPACK_IMPORTED_MODULE_3__.tz),\n/* harmony export */   tzName: () => (/* reexport safe */ _tzName_index_js__WEBPACK_IMPORTED_MODULE_6__.tzName),\n/* harmony export */   tzOffset: () => (/* reexport safe */ _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_4__.tzOffset),\n/* harmony export */   tzScan: () => (/* reexport safe */ _tzScan_index_js__WEBPACK_IMPORTED_MODULE_5__.tzScan)\n/* harmony export */ });\n/* harmony import */ var _constants_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants/index.js */ \"(ssr)/./node_modules/@date-fns/tz/constants/index.js\");\n/* harmony import */ var _date_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./date/index.js */ \"(ssr)/./node_modules/@date-fns/tz/date/index.js\");\n/* harmony import */ var _date_mini_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./date/mini.js */ \"(ssr)/./node_modules/@date-fns/tz/date/mini.js\");\n/* harmony import */ var _tz_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tz/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tz/index.js\");\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tzOffset/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js\");\n/* harmony import */ var _tzScan_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tzScan/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzScan/index.js\");\n/* harmony import */ var _tzName_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tzName/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzName/index.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRhdGUtZm5zL3R6L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXFDO0FBQ0w7QUFDRDtBQUNEO0FBQ007QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2N5bWF0aWNzLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BkYXRlLWZucy90ei9pbmRleC5qcz9kOWMyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2NvbnN0YW50cy9pbmRleC5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vZGF0ZS9pbmRleC5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vZGF0ZS9taW5pLmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90ei9pbmRleC5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHpPZmZzZXQvaW5kZXguanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3R6U2Nhbi9pbmRleC5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHpOYW1lL2luZGV4LmpzXCI7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/tz/index.js":
/*!***********************************************!*\
  !*** ./node_modules/@date-fns/tz/tz/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tz: () => (/* binding */ tz)\n/* harmony export */ });\n/* harmony import */ var _date_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../date/index.js */ \"(ssr)/./node_modules/@date-fns/tz/date/index.js\");\n\n\n/**\n * The function creates accepts a time zone and returns a function that creates\n * a new `TZDate` instance in the time zone from the provided value. Use it to\n * provide the context for the date-fns functions, via the `in` option.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n *\n * @returns Function that creates a new `TZDate` instance in the time zone\n */\nconst tz = timeZone => value => _date_index_js__WEBPACK_IMPORTED_MODULE_0__.TZDate.tz(timeZone, +new Date(value));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRhdGUtZm5zL3R6L3R6L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDOztBQUUxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxnQ0FBZ0Msa0RBQU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jeW1hdGljcy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AZGF0ZS1mbnMvdHovdHovaW5kZXguanM/MzMwNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUWkRhdGUgfSBmcm9tIFwiLi4vZGF0ZS9pbmRleC5qc1wiO1xuXG4vKipcbiAqIFRoZSBmdW5jdGlvbiBjcmVhdGVzIGFjY2VwdHMgYSB0aW1lIHpvbmUgYW5kIHJldHVybnMgYSBmdW5jdGlvbiB0aGF0IGNyZWF0ZXNcbiAqIGEgbmV3IGBUWkRhdGVgIGluc3RhbmNlIGluIHRoZSB0aW1lIHpvbmUgZnJvbSB0aGUgcHJvdmlkZWQgdmFsdWUuIFVzZSBpdCB0b1xuICogcHJvdmlkZSB0aGUgY29udGV4dCBmb3IgdGhlIGRhdGUtZm5zIGZ1bmN0aW9ucywgdmlhIHRoZSBgaW5gIG9wdGlvbi5cbiAqXG4gKiBAcGFyYW0gdGltZVpvbmUgLSBUaW1lIHpvbmUgbmFtZSAoSUFOQSBvciBVVEMgb2Zmc2V0KVxuICpcbiAqIEByZXR1cm5zIEZ1bmN0aW9uIHRoYXQgY3JlYXRlcyBhIG5ldyBgVFpEYXRlYCBpbnN0YW5jZSBpbiB0aGUgdGltZSB6b25lXG4gKi9cbmV4cG9ydCBjb25zdCB0eiA9IHRpbWVab25lID0+IHZhbHVlID0+IFRaRGF0ZS50eih0aW1lWm9uZSwgK25ldyBEYXRlKHZhbHVlKSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/tz/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/tzName/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@date-fns/tz/tzName/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzName: () => (/* binding */ tzName)\n/* harmony export */ });\n/**\n * Time zone name format.\n */\n\n/**\n * The function returns the time zone name for the given date in the specified\n * time zone.\n *\n * It uses the `Intl.DateTimeFormat` API and by default outputs the time zone\n * name in a long format, e.g. \"Pacific Standard Time\" or\n * \"Singapore Standard Time\".\n *\n * It is possible to specify the format as the third argument using one of the following options\n *\n * - \"short\": e.g. \"EDT\" or \"GMT+8\".\n * - \"long\": e.g. \"Eastern Daylight Time\".\n * - \"shortGeneric\": e.g. \"ET\" or \"Singapore Time\".\n * - \"longGeneric\": e.g. \"Eastern Time\" or \"Singapore Standard Time\".\n *\n * These options correspond to TR35 tokens `z..zzz`, `zzzz`, `v`, and `vvvv` respectively: https://www.unicode.org/reports/tr35/tr35-dates.html#dfst-zone\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param date - Date object to get the time zone name for\n * @param format - Optional format of the time zone name. Defaults to \"long\". Can be \"short\", \"long\", \"shortGeneric\", or \"longGeneric\".\n *\n * @returns Time zone name (e.g. \"Singapore Standard Time\")\n */\nfunction tzName(timeZone, date, format = \"long\") {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    // Enforces engine to render the time. Without the option JavaScriptCore omits it.\n    hour: \"numeric\",\n    timeZone: timeZone,\n    timeZoneName: format\n  }).format(date).split(/\\s/g) // Format.JS uses non-breaking spaces\n  .slice(2) // Skip the hour and AM/PM parts\n  .join(\" \");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/tzName/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@date-fns/tz/tzOffset/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzOffset: () => (/* binding */ tzOffset)\n/* harmony export */ });\nconst offsetFormatCache = {};\nconst offsetCache = {};\n\n/**\n * The function extracts UTC offset in minutes from the given date in specified\n * time zone.\n *\n * Unlike `Date.prototype.getTimezoneOffset`, this function returns the value\n * mirrored to the sign of the offset in the time zone. For Asia/Singapore\n * (UTC+8), `tzOffset` returns 480, while `getTimezoneOffset` returns -480.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param date - Date to check the offset for\n *\n * @returns UTC offset in minutes\n */\nfunction tzOffset(timeZone, date) {\n  try {\n    const format = offsetFormatCache[timeZone] ||= new Intl.DateTimeFormat(\"en-US\", {\n      timeZone,\n      timeZoneName: \"longOffset\"\n    }).format;\n    const offsetStr = format(date).split(\"GMT\")[1];\n    if (offsetStr in offsetCache) return offsetCache[offsetStr];\n    return calcOffset(offsetStr, offsetStr.split(\":\"));\n  } catch {\n    // Fallback to manual parsing if the runtime doesn't support ±HH:MM/±HHMM/±HH\n    // See: https://github.com/nodejs/node/issues/53419\n    if (timeZone in offsetCache) return offsetCache[timeZone];\n    const captures = timeZone?.match(offsetRe);\n    if (captures) return calcOffset(timeZone, captures.slice(1));\n    return NaN;\n  }\n}\nconst offsetRe = /([+-]\\d\\d):?(\\d\\d)?/;\nfunction calcOffset(cacheStr, values) {\n  const hours = +(values[0] || 0);\n  const minutes = +(values[1] || 0);\n  // Convert seconds to minutes by dividing by 60 to keep the function return in minutes.\n  const seconds = +(values[2] || 0) / 60;\n  return offsetCache[cacheStr] = hours * 60 + minutes > 0 ? hours * 60 + minutes + seconds : hours * 60 - minutes - seconds;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/tzScan/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@date-fns/tz/tzScan/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzScan: () => (/* binding */ tzScan)\n/* harmony export */ });\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../tzOffset/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js\");\n\n\n/**\n * Time interval.\n */\n\n/**\n * Time zone change record.\n */\n\n/**\n * The function scans the time zone for changes in the given interval.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param interval - Time interval to scan for changes\n *\n * @returns Array of time zone changes\n */\nfunction tzScan(timeZone, interval) {\n  const changes = [];\n  const monthDate = new Date(interval.start);\n  monthDate.setUTCSeconds(0, 0);\n  const endDate = new Date(interval.end);\n  endDate.setUTCSeconds(0, 0);\n  const endMonthTime = +endDate;\n  let lastOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, monthDate);\n  while (+monthDate < endMonthTime) {\n    // Month forward\n    monthDate.setUTCMonth(monthDate.getUTCMonth() + 1);\n\n    // Find the month where the offset changes\n    const offset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, monthDate);\n    if (offset != lastOffset) {\n      // Rewind a month back to find the day where the offset changes\n      const dayDate = new Date(monthDate);\n      dayDate.setUTCMonth(dayDate.getUTCMonth() - 1);\n      const endDayTime = +monthDate;\n      lastOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, dayDate);\n      while (+dayDate < endDayTime) {\n        // Day forward\n        dayDate.setUTCDate(dayDate.getUTCDate() + 1);\n\n        // Find the day where the offset changes\n        const offset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, dayDate);\n        if (offset != lastOffset) {\n          // Rewind a day back to find the time where the offset changes\n          const hourDate = new Date(dayDate);\n          hourDate.setUTCDate(hourDate.getUTCDate() - 1);\n          const endHourTime = +dayDate;\n          lastOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, hourDate);\n          while (+hourDate < endHourTime) {\n            // Hour forward\n            hourDate.setUTCHours(hourDate.getUTCHours() + 1);\n\n            // Find the hour where the offset changes\n            const hourOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, hourDate);\n            if (hourOffset !== lastOffset) {\n              changes.push({\n                date: new Date(hourDate),\n                change: hourOffset - lastOffset,\n                offset: hourOffset\n              });\n            }\n            lastOffset = hourOffset;\n          }\n        }\n        lastOffset = offset;\n      }\n    }\n    lastOffset = offset;\n  }\n  return changes;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/tzScan/index.js\n");

/***/ })

};
;