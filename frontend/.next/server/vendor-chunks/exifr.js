"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/exifr";
exports.ids = ["vendor-chunks/exifr"];
exports.modules = {

/***/ "(ssr)/./node_modules/exifr/dist/full.esm.mjs":
/*!**********************************************!*\
  !*** ./node_modules/exifr/dist/full.esm.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Exifr: () => (/* binding */ te),\n/* harmony export */   Options: () => (/* binding */ q),\n/* harmony export */   allFormatters: () => (/* binding */ X),\n/* harmony export */   chunkedProps: () => (/* binding */ G),\n/* harmony export */   createDictionary: () => (/* binding */ U),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   extendDictionary: () => (/* binding */ F),\n/* harmony export */   fetchUrlAsArrayBuffer: () => (/* binding */ M),\n/* harmony export */   fileParsers: () => (/* binding */ w),\n/* harmony export */   fileReaders: () => (/* binding */ A),\n/* harmony export */   gps: () => (/* binding */ Se),\n/* harmony export */   gpsOnlyOptions: () => (/* binding */ me),\n/* harmony export */   inheritables: () => (/* binding */ K),\n/* harmony export */   orientation: () => (/* binding */ Pe),\n/* harmony export */   orientationOnlyOptions: () => (/* binding */ Ie),\n/* harmony export */   otherSegments: () => (/* binding */ V),\n/* harmony export */   parse: () => (/* binding */ ie),\n/* harmony export */   readBlobAsArrayBuffer: () => (/* binding */ R),\n/* harmony export */   rotateCanvas: () => (/* binding */ we),\n/* harmony export */   rotateCss: () => (/* binding */ Te),\n/* harmony export */   rotation: () => (/* binding */ Ae),\n/* harmony export */   rotations: () => (/* binding */ ke),\n/* harmony export */   segmentParsers: () => (/* binding */ T),\n/* harmony export */   segments: () => (/* binding */ z),\n/* harmony export */   segmentsAndBlocks: () => (/* binding */ j),\n/* harmony export */   sidecar: () => (/* binding */ st),\n/* harmony export */   tagKeys: () => (/* binding */ E),\n/* harmony export */   tagRevivers: () => (/* binding */ N),\n/* harmony export */   tagValues: () => (/* binding */ B),\n/* harmony export */   thumbnail: () => (/* binding */ ye),\n/* harmony export */   thumbnailOnlyOptions: () => (/* binding */ Ce),\n/* harmony export */   thumbnailUrl: () => (/* binding */ be),\n/* harmony export */   tiffBlocks: () => (/* binding */ H),\n/* harmony export */   tiffExtractables: () => (/* binding */ W)\n/* harmony export */ });\nvar e=\"undefined\"!=typeof self?self:global;const t=\"undefined\"!=typeof navigator,i=t&&\"undefined\"==typeof HTMLImageElement,n=!(\"undefined\"==typeof global||\"undefined\"==typeof process||!process.versions||!process.versions.node),s=e.Buffer,r=e.BigInt,a=!!s,o=e=>e;function l(e,t=o){if(n)try{return\"function\"==typeof require?Promise.resolve(t(require(e))):import(/* webpackIgnore: true */ e).then(t)}catch(t){console.warn(`Couldn't load ${e}`)}}let h=e.fetch;const u=e=>h=e;if(!e.fetch){const e=l(\"http\",(e=>e)),t=l(\"https\",(e=>e)),i=(n,{headers:s}={})=>new Promise((async(r,a)=>{let{port:o,hostname:l,pathname:h,protocol:u,search:c}=new URL(n);const f={method:\"GET\",hostname:l,path:encodeURI(h)+c,headers:s};\"\"!==o&&(f.port=Number(o));const d=(\"https:\"===u?await t:await e).request(f,(e=>{if(301===e.statusCode||302===e.statusCode){let t=new URL(e.headers.location,n).toString();return i(t,{headers:s}).then(r).catch(a)}r({status:e.statusCode,arrayBuffer:()=>new Promise((t=>{let i=[];e.on(\"data\",(e=>i.push(e))),e.on(\"end\",(()=>t(Buffer.concat(i))))}))})}));d.on(\"error\",a),d.end()}));u(i)}function c(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}const f=e=>p(e)?void 0:e,d=e=>void 0!==e;function p(e){return void 0===e||(e instanceof Map?0===e.size:0===Object.values(e).filter(d).length)}function g(e){let t=new Error(e);throw delete t.stack,t}function m(e){return\"\"===(e=function(e){for(;e.endsWith(\"\\0\");)e=e.slice(0,-1);return e}(e).trim())?void 0:e}function S(e){let t=function(e){let t=0;return e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048}(e);return e.jfif.enabled&&(t+=50),e.xmp.enabled&&(t+=2e4),e.iptc.enabled&&(t+=14e3),e.icc.enabled&&(t+=6e3),t}const C=e=>String.fromCharCode.apply(null,e),y=\"undefined\"!=typeof TextDecoder?new TextDecoder(\"utf-8\"):void 0;function b(e){return y?y.decode(e):a?Buffer.from(e).toString(\"utf8\"):decodeURIComponent(escape(C(e)))}class I{static from(e,t){return e instanceof this&&e.le===t?e:new I(e,void 0,void 0,t)}constructor(e,t=0,i,n){if(\"boolean\"==typeof n&&(this.le=n),Array.isArray(e)&&(e=new Uint8Array(e)),0===e)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){void 0===i&&(i=e.byteLength-t);let n=new DataView(e,t,i);this._swapDataView(n)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof I){void 0===i&&(i=e.byteLength-t),(t+=e.byteOffset)+i>e.byteOffset+e.byteLength&&g(\"Creating view outside of available memory in ArrayBuffer\");let n=new DataView(e.buffer,t,i);this._swapDataView(n)}else if(\"number\"==typeof e){let t=new DataView(new ArrayBuffer(e));this._swapDataView(t)}else g(\"Invalid input argument for BufferView: \"+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,i=I){return e instanceof DataView||e instanceof I?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||g(\"BufferView.set(): Invalid data argument.\"),this.toUint8().set(e,t),new i(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new I(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return b(this.getUint8Array(e,t))}getLatin1String(e=0,t=this.byteLength){let i=this.getUint8Array(e,t);return C(i)}getUnicodeString(e=0,t=this.byteLength){const i=[];for(let n=0;n<t&&e+n<this.byteLength;n+=2)i.push(this.getUint16(e+n));return C(i)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,i){switch(t){case 1:return this.getUint8(e,i);case 2:return this.getUint16(e,i);case 4:return this.getUint32(e,i);case 8:return this.getUint64&&this.getUint64(e,i)}}getUint(e,t,i){switch(t){case 8:return this.getUint8(e,i);case 16:return this.getUint16(e,i);case 32:return this.getUint32(e,i);case 64:return this.getUint64&&this.getUint64(e,i)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function P(e,t){g(`${e} '${t}' was not loaded, try using full build of exifr.`)}class k extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||P(this.kind,e),t&&(e in t||function(e,t){g(`Unknown ${e} '${t}'.`)}(this.kind,e),t[e].enabled||P(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var w=new k(\"file parser\"),T=new k(\"segment parser\"),A=new k(\"file reader\");function D(e,n){return\"string\"==typeof e?O(e,n):t&&!i&&e instanceof HTMLImageElement?O(e.src,n):e instanceof Uint8Array||e instanceof ArrayBuffer||e instanceof DataView?new I(e):t&&e instanceof Blob?x(e,n,\"blob\",R):void g(\"Invalid input argument\")}function O(e,i){return(s=e).startsWith(\"data:\")||s.length>1e4?v(e,i,\"base64\"):n&&e.includes(\"://\")?x(e,i,\"url\",M):n?v(e,i,\"fs\"):t?x(e,i,\"url\",M):void g(\"Invalid input argument\");var s}async function x(e,t,i,n){return A.has(i)?v(e,t,i):n?async function(e,t){let i=await t(e);return new I(i)}(e,n):void g(`Parser ${i} is not loaded`)}async function v(e,t,i){let n=new(A.get(i))(e,t);return await n.read(),n}const M=e=>h(e).then((e=>e.arrayBuffer())),R=e=>new Promise(((t,i)=>{let n=new FileReader;n.onloadend=()=>t(n.result||new ArrayBuffer),n.onerror=i,n.readAsArrayBuffer(e)}));class L extends Map{get tagKeys(){return this.allKeys||(this.allKeys=Array.from(this.keys())),this.allKeys}get tagValues(){return this.allValues||(this.allValues=Array.from(this.values())),this.allValues}}function U(e,t,i){let n=new L;for(let[e,t]of i)n.set(e,t);if(Array.isArray(t))for(let i of t)e.set(i,n);else e.set(t,n);return n}function F(e,t,i){let n,s=e.get(t);for(n of i)s.set(n[0],n[1])}const E=new Map,B=new Map,N=new Map,G=[\"chunked\",\"firstChunkSize\",\"firstChunkSizeNode\",\"firstChunkSizeBrowser\",\"chunkSize\",\"chunkLimit\"],V=[\"jfif\",\"xmp\",\"icc\",\"iptc\",\"ihdr\"],z=[\"tiff\",...V],H=[\"ifd0\",\"ifd1\",\"exif\",\"gps\",\"interop\"],j=[...z,...H],W=[\"makerNote\",\"userComment\"],K=[\"translateKeys\",\"translateValues\",\"reviveValues\",\"multiSegment\"],X=[...K,\"sanitize\",\"mergeOutput\",\"silentErrors\"];class _{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class Y extends _{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,i,n){if(super(),c(this,\"enabled\",!1),c(this,\"skip\",new Set),c(this,\"pick\",new Set),c(this,\"deps\",new Set),c(this,\"translateKeys\",!1),c(this,\"translateValues\",!1),c(this,\"reviveValues\",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=H.includes(e),this.canBeFiltered&&(this.dict=E.get(e)),void 0!==i)if(Array.isArray(i))this.parse=this.enabled=!0,this.canBeFiltered&&i.length>0&&this.translateTagSet(i,this.pick);else if(\"object\"==typeof i){if(this.enabled=!0,this.parse=!1!==i.parse,this.canBeFiltered){let{pick:e,skip:t}=i;e&&e.length>0&&this.translateTagSet(e,this.pick),t&&t.length>0&&this.translateTagSet(t,this.skip)}this.applyInheritables(i)}else!0===i||!1===i?this.parse=this.enabled=i:g(`Invalid options argument: ${i}`)}applyInheritables(e){let t,i;for(t of K)i=e[t],void 0!==i&&(this[t]=i)}translateTagSet(e,t){if(this.dict){let i,n,{tagKeys:s,tagValues:r}=this.dict;for(i of e)\"string\"==typeof i?(n=r.indexOf(i),-1===n&&(n=s.indexOf(Number(i))),-1!==n&&t.add(Number(s[n]))):t.add(i)}else for(let i of e)t.add(i)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,ee(this.pick,this.deps)):this.enabled&&this.pick.size>0&&ee(this.pick,this.deps)}}var $={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},J=new Map;class q extends _{static useCached(e){let t=J.get(e);return void 0!==t||(t=new this(e),J.set(e,t)),t}constructor(e){super(),!0===e?this.setupFromTrue():void 0===e?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):\"object\"==typeof e?this.setupFromObject(e):g(`Invalid options argument ${e}`),void 0===this.firstChunkSize&&(this.firstChunkSize=t?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=$[e];for(e of j)this[e]=new Y(e,$[e],void 0,this)}setupFromTrue(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=!0;for(e of j)this[e]=new Y(e,!0,void 0,this)}setupFromArray(e){let t;for(t of G)this[t]=$[t];for(t of X)this[t]=$[t];for(t of W)this[t]=$[t];for(t of j)this[t]=new Y(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,H)}setupFromObject(e){let t;for(t of(H.ifd0=H.ifd0||H.image,H.ifd1=H.ifd1||H.thumbnail,Object.assign(this,e),G))this[t]=Z(e[t],$[t]);for(t of X)this[t]=Z(e[t],$[t]);for(t of W)this[t]=Z(e[t],$[t]);for(t of z)this[t]=new Y(t,$[t],e[t],this);for(t of H)this[t]=new Y(t,$[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,H,j),!0===e.tiff?this.batchEnableWithBool(H,!0):!1===e.tiff?this.batchEnableWithUserValue(H,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,H):\"object\"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,H)}batchEnableWithBool(e,t){for(let i of e)this[i].enabled=t}batchEnableWithUserValue(e,t){for(let i of e){let e=t[i];this[i].enabled=!1!==e&&void 0!==e}}setupGlobalFilters(e,t,i,n=i){if(e&&e.length){for(let e of n)this[e].enabled=!1;let t=Q(e,i);for(let[e,i]of t)ee(this[e].pick,i),this[e].enabled=!0}else if(t&&t.length){let e=Q(t,i);for(let[t,i]of e)ee(this[t].skip,i)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:i,iptc:n,icc:s}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),i.enabled||e.skip.add(700),n.enabled||e.skip.add(33723),s.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:i,interop:n}=this;n.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),i.needed&&e.deps.add(34853),this.tiff.enabled=H.some((e=>!0===this[e].enabled))||this.makerNote||this.userComment;for(let e of H)this[e].finalizeFilters()}get onlyTiff(){return!V.map((e=>this[e].enabled)).some((e=>!0===e))&&this.tiff.enabled}checkLoadedPlugins(){for(let e of z)this[e].enabled&&!T.has(e)&&P(\"segment parser\",e)}}function Q(e,t){let i,n,s,r,a=[];for(s of t){for(r of(i=E.get(s),n=[],i))(e.includes(r[0])||e.includes(r[1]))&&n.push(r[0]);n.length&&a.push([s,n])}return a}function Z(e,t){return void 0!==e?e:void 0!==t?t:void 0}function ee(e,t){for(let i of t)e.add(i)}c(q,\"default\",$);class te{constructor(e){c(this,\"parsers\",{}),c(this,\"output\",{}),c(this,\"errors\",[]),c(this,\"pushToErrors\",(e=>this.errors.push(e))),this.options=q.useCached(e)}async read(e){this.file=await D(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[i,n]of w)if(n.canHandle(e,t))return this.fileParser=new n(this.options,this.file,this.parsers),e[i]=!0;this.file.close&&this.file.close(),g(\"Unknown file format\")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),f(e)}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map((async t=>{let i=await t.parse();t.assignToOutput(e,i)}));this.options.silentErrors&&(t=t.map((e=>e.catch(this.pushToErrors)))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,i=T.get(\"tiff\",e);var n;if(t.tiff?n={start:0,type:\"tiff\"}:t.jpeg&&(n=await this.fileParser.getOrFindSegment(\"tiff\")),void 0===n)return;let s=await this.fileParser.ensureSegmentChunk(n),r=this.parsers.tiff=new i(s,e,t),a=await r.extractThumbnail();return t.close&&t.close(),a}}async function ie(e,t){let i=new te(t);return await i.read(e),i.parse()}var ne=Object.freeze({__proto__:null,parse:ie,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q});class se{constructor(e,t,i){c(this,\"errors\",[]),c(this,\"ensureSegmentChunk\",(async e=>{let t=e.start,i=e.size||65536;if(this.file.chunked)if(this.file.available(t,i))e.chunk=this.file.subarray(t,i);else try{e.chunk=await this.file.readChunk(t,i)}catch(t){g(`Couldn't read segment: ${JSON.stringify(e)}. ${t.message}`)}else this.file.byteLength>t+i?e.chunk=this.file.subarray(t,i):void 0===e.size?e.chunk=this.file.subarray(t):g(\"Segment unreachable: \"+JSON.stringify(e));return e.chunk})),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=i}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let i=new(T.get(e))(t,this.options,this.file);return this.parsers[e]=i}createParsers(e){for(let t of e){let{type:e,chunk:i}=t,n=this.options[e];if(n&&n.enabled){let t=this.parsers[e];t&&t.append||t||this.createParser(e,i)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}class re{static findPosition(e,t){let i=e.getUint16(t+2)+2,n=\"function\"==typeof this.headerLength?this.headerLength(e,t,i):this.headerLength,s=t+n,r=i-n;return{offset:t,length:i,headerLength:n,start:s,size:r,end:s+r}}static parse(e,t={}){return new this(e,new q({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof I?e:new I(e)}constructor(e,t={},i){c(this,\"errors\",[]),c(this,\"raw\",new Map),c(this,\"handleError\",(e=>{if(!this.options.silentErrors)throw e;this.errors.push(e.message)})),this.chunk=this.normalizeInput(e),this.file=i,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let i=N.get(t),n=B.get(t),s=E.get(t),r=this.options[t],a=r.reviveValues&&!!i,o=r.translateValues&&!!n,l=r.translateKeys&&!!s,h={};for(let[t,r]of e)a&&i.has(t)?r=i.get(t)(r):o&&n.has(t)&&(r=this.translateValue(r,n.get(t))),l&&s.has(t)&&(t=s.get(t)||t),h[t]=r;return h}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,i){if(this.globalOptions.mergeOutput)return Object.assign(e,i);e[t]?Object.assign(e[t],i):e[t]=i}}c(re,\"headerLength\",4),c(re,\"type\",void 0),c(re,\"multiSegment\",!1),c(re,\"canHandle\",(()=>!1));function ae(e){return 192===e||194===e||196===e||219===e||221===e||218===e||254===e}function oe(e){return e>=224&&e<=239}function le(e,t,i){for(let[n,s]of T)if(s.canHandle(e,t,i))return n}class he extends se{constructor(...e){super(...e),c(this,\"appSegments\",[]),c(this,\"jpegSegments\",[]),c(this,\"unknownSegments\",[])}static canHandle(e,t){return 65496===t}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){!0===e?(this.findAll=!0,this.wanted=new Set(T.keyList())):(e=void 0===e?T.keyList().filter((e=>this.options[e].enabled)):e.filter((e=>this.options[e].enabled&&T.has(e))),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:i,findAll:n,wanted:s,remaining:r}=this;if(!n&&this.file.chunked&&(n=Array.from(s).some((e=>{let t=T.get(e),i=this.options[e];return t.multiSegment&&i.multiSegment})),n&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,i.byteLength),!this.options.onlyTiff&&i.chunked){let t=!1;for(;r.size>0&&!t&&(i.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:n}=i,s=this.appSegments.some((e=>!this.file.available(e.offset||e.start,e.length||e.size)));if(t=e>n&&!s?!await i.readNextChunk(e):!await i.readNextChunk(n),void 0===(e=this.findAppSegmentsInRange(e,i.byteLength)))return}}}findAppSegmentsInRange(e,t){t-=2;let i,n,s,r,a,o,{file:l,findAll:h,wanted:u,remaining:c,options:f}=this;for(;e<t;e++)if(255===l.getUint8(e))if(i=l.getUint8(e+1),oe(i)){if(n=l.getUint16(e+2),s=le(l,e,n),s&&u.has(s)&&(r=T.get(s),a=r.findPosition(l,e),o=f[s],a.type=s,this.appSegments.push(a),!h&&(r.multiSegment&&o.multiSegment?(this.unfinishedMultiSegment=a.chunkNumber<a.chunkCount,this.unfinishedMultiSegment||c.delete(s)):c.delete(s),0===c.size)))break;f.recordUnknownSegments&&(a=re.findPosition(l,e),a.marker=i,this.unknownSegments.push(a)),e+=n+1}else if(ae(i)){if(n=l.getUint16(e+2),218===i&&!1!==f.stopAfterSos)return;f.recordJpegSegments&&this.jpegSegments.push({offset:e,length:n,marker:i}),e+=n+1}return e}mergeMultiSegments(){if(!this.appSegments.some((e=>e.multiSegment)))return;let e=function(e,t){let i,n,s,r=new Map;for(let a=0;a<e.length;a++)i=e[a],n=i[t],r.has(n)?s=r.get(n):r.set(n,s=[]),s.push(i);return Array.from(r)}(this.appSegments,\"type\");this.mergedAppSegments=e.map((([e,t])=>{let i=T.get(e,this.options);if(i.handleMultiSegments){return{type:e,chunk:i.handleMultiSegments(t)}}return t[0]}))}getSegment(e){return this.appSegments.find((t=>t.type===e))}async getOrFindSegment(e){let t=this.getSegment(e);return void 0===t&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}c(he,\"type\",\"jpeg\"),w.set(\"jpeg\",he);const ue=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class ce extends re{parseHeader(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,i=new Map){let{pick:n,skip:s}=this.options[t];n=new Set(n);let r=n.size>0,a=0===s.size,o=this.chunk.getUint16(e);e+=2;for(let l=0;l<o;l++){let o=this.chunk.getUint16(e);if(r){if(n.has(o)&&(i.set(o,this.parseTag(e,o,t)),n.delete(o),0===n.size))break}else!a&&s.has(o)||i.set(o,this.parseTag(e,o,t));e+=12}return i}parseTag(e,t,i){let{chunk:n}=this,s=n.getUint16(e+2),r=n.getUint32(e+4),a=ue[s];if(a*r<=4?e+=8:e=n.getUint32(e+8),(s<1||s>13)&&g(`Invalid TIFF value type. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e}`),e>n.byteLength&&g(`Invalid TIFF value offset. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e} is outside of chunk size ${n.byteLength}`),1===s)return n.getUint8Array(e,r);if(2===s)return m(n.getString(e,r));if(7===s)return n.getUint8Array(e,r);if(1===r)return this.parseTagValue(s,e);{let t=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(s))(r),i=a;for(let n=0;n<r;n++)t[n]=this.parseTagValue(s,e),e+=i;return t}}parseTagValue(e,t){let{chunk:i}=this;switch(e){case 1:return i.getUint8(t);case 3:return i.getUint16(t);case 4:return i.getUint32(t);case 5:return i.getUint32(t)/i.getUint32(t+4);case 6:return i.getInt8(t);case 8:return i.getInt16(t);case 9:return i.getInt32(t);case 10:return i.getInt32(t)/i.getInt32(t+4);case 11:return i.getFloat(t);case 12:return i.getDouble(t);case 13:return i.getUint32(t);default:g(`Invalid tiff type ${e}`)}}}class fe extends ce{static canHandle(e,t){return 225===e.getUint8(t+1)&&1165519206===e.getUint32(t+4)&&0===e.getUint16(t+8)}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse(\"parseExifBlock\"),e.gps.enabled&&await this.safeParse(\"parseGpsBlock\"),e.interop.enabled&&await this.safeParse(\"parseInteropBlock\"),e.ifd1.enabled&&await this.safeParse(\"parseThumbnailBlock\"),this.createOutput()}safeParse(e){let t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}findIfd0Offset(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(void 0===this.ifd1Offset){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let i=new Map;return this[t]=i,this.parseTags(e,t,i),i}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&g(\"Malformed EXIF data\"),!e.chunked&&this.ifd0Offset>e.byteLength&&g(`IFD0 offset points to outside of file.\\nthis.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,S(this.options));let t=this.parseBlock(this.ifd0Offset,\"ifd0\");return 0!==t.size?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.exifOffset)return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,S(this.options));let e=this.parseBlock(this.exifOffset,\"exif\");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let i=e.get(t);i&&1===i.length&&e.set(t,i[0])}async parseGpsBlock(){if(this.gps)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.gpsOffset)return;let e=this.parseBlock(this.gpsOffset,\"gps\");return e&&e.has(2)&&e.has(4)&&(e.set(\"latitude\",de(...e.get(2),e.get(1))),e.set(\"longitude\",de(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),void 0!==this.interopOffset||this.exif||await this.parseExifBlock(),void 0!==this.interopOffset))return this.parseBlock(this.interopOffset,\"interop\")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,\"ifd1\"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),void 0===this.ifd1)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,i,n={};for(t of H)if(e=this[t],!p(e))if(i=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if(\"ifd1\"===t)continue;Object.assign(n,i)}else n[t]=i;return this.makerNote&&(n.makerNote=this.makerNote),this.userComment&&(n.userComment=this.userComment),n}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[i,n]of Object.entries(t))this.assignObjectToOutput(e,i,n)}}function de(e,t,i,n){var s=e+t/60+i/3600;return\"S\"!==n&&\"W\"!==n||(s*=-1),s}c(fe,\"type\",\"tiff\"),c(fe,\"headerLength\",10),T.set(\"tiff\",fe);var pe=Object.freeze({__proto__:null,default:ne,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie});const ge={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1},me=Object.assign({},ge,{firstChunkSize:4e4,gps:[1,2,3,4]});async function Se(e){let t=new te(me);await t.read(e);let i=await t.parse();if(i&&i.gps){let{latitude:e,longitude:t}=i.gps;return{latitude:e,longitude:t}}}const Ce=Object.assign({},ge,{tiff:!1,ifd1:!0,mergeOutput:!1});async function ye(e){let t=new te(Ce);await t.read(e);let i=await t.extractThumbnail();return i&&a?s.from(i):i}async function be(e){let t=await this.thumbnail(e);if(void 0!==t){let e=new Blob([t]);return URL.createObjectURL(e)}}const Ie=Object.assign({},ge,{firstChunkSize:4e4,ifd0:[274]});async function Pe(e){let t=new te(Ie);await t.read(e);let i=await t.parse();if(i&&i.ifd0)return i.ifd0[274]}const ke=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let we=!0,Te=!0;if(\"object\"==typeof navigator){let e=navigator.userAgent;if(e.includes(\"iPad\")||e.includes(\"iPhone\")){let t=e.match(/OS (\\d+)_(\\d+)/);if(t){let[,e,i]=t,n=Number(e)+.1*Number(i);we=n<13.4,Te=!1}}else if(e.includes(\"OS X 10\")){let[,t]=e.match(/OS X 10[_.](\\d+)/);we=Te=Number(t)<15}if(e.includes(\"Chrome/\")){let[,t]=e.match(/Chrome\\/(\\d+)/);we=Te=Number(t)<81}else if(e.includes(\"Firefox/\")){let[,t]=e.match(/Firefox\\/(\\d+)/);we=Te=Number(t)<77}}async function Ae(e){let t=await Pe(e);return Object.assign({canvas:we,css:Te},ke[t])}class De extends I{constructor(...e){super(...e),c(this,\"ranges\",new Oe),0!==this.byteLength&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,i){if(0===e&&0===this.byteLength&&i){let e=new DataView(i.buffer||i,i.byteOffset,i.byteLength);this._swapDataView(e)}else{let i=e+t;if(i>this.byteLength){let{dataView:e}=this._extend(i);this._swapDataView(e)}}}_extend(e){let t;t=a?s.allocUnsafe(e):new Uint8Array(e);let i=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:i}}subarray(e,t,i=!1){return t=t||this._lengthToEnd(e),i&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,i=!1){i&&this._tryExtend(t,e.byteLength,e);let n=super.set(e,t);return this.ranges.add(t,n.byteLength),n}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class Oe{constructor(){c(this,\"list\",[])}get length(){return this.list.length}add(e,t,i=0){let n=e+t,s=this.list.filter((t=>xe(e,t.offset,n)||xe(e,t.end,n)));if(s.length>0){e=Math.min(e,...s.map((e=>e.offset))),n=Math.max(n,...s.map((e=>e.end))),t=n-e;let i=s.shift();i.offset=e,i.length=t,i.end=n,this.list=this.list.filter((e=>!s.includes(e)))}else this.list.push({offset:e,length:t,end:n})}available(e,t){let i=e+t;return this.list.some((t=>t.offset<=e&&i<=t.end))}}function xe(e,t,i){return e<=t&&t<=i}class ve extends De{constructor(e,t){super(0),c(this,\"chunksRead\",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,i=await this.readChunk(e,t);return!!i&&i.byteLength===t}async readChunk(e,t){if(this.chunksRead++,0!==(t=this.safeWrapAddress(e,t)))return this._readChunk(e,t)}safeWrapAddress(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return void 0!==this.size&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}A.set(\"blob\",class extends ve{async readWhole(){this.chunked=!1;let e=await R(this.input);this._swapArrayBuffer(e)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(e,t){let i=t?e+t:void 0,n=this.input.slice(e,i),s=await R(n);return this.set(s,e,!0)}});var Me=Object.freeze({__proto__:null,default:pe,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});A.set(\"url\",class extends ve{async readWhole(){this.chunked=!1;let e=await M(this.input);e instanceof ArrayBuffer?this._swapArrayBuffer(e):e instanceof Uint8Array&&this._swapBuffer(e)}async _readChunk(e,t){let i=t?e+t-1:void 0,n=this.options.httpHeaders||{};(e||i)&&(n.range=`bytes=${[e,i].join(\"-\")}`);let s=await h(this.input,{headers:n}),r=await s.arrayBuffer(),a=r.byteLength;if(416!==s.status)return a!==t&&(this.size=e+a),this.set(r,e,!0)}});I.prototype.getUint64=function(e){let t=this.getUint32(e),i=this.getUint32(e+4);return t<1048575?t<<32|i:void 0!==typeof r?(console.warn(\"Using BigInt because of type 64uint but JS can only handle 53b numbers.\"),r(t)<<r(32)|r(i)):void g(\"Trying to read 64b value but JS can only handle 53b numbers.\")};class Re extends se{parseBoxes(e=0){let t=[];for(;e<this.file.byteLength-4;){let i=this.parseBoxHead(e);if(t.push(i),0===i.length)break;e+=i.length}return t}parseSubBoxes(e){e.boxes=this.parseBoxes(e.start)}findBox(e,t){return void 0===e.boxes&&this.parseSubBoxes(e),e.boxes.find((e=>e.kind===t))}parseBoxHead(e){let t=this.file.getUint32(e),i=this.file.getString(e+4,4),n=e+8;return 1===t&&(t=this.file.getUint64(e+8),n+=8),{offset:e,length:t,kind:i,start:n}}parseBoxFullHead(e){if(void 0!==e.version)return;let t=this.file.getUint32(e.start);e.version=t>>24,e.start+=4}}class Le extends Re{static canHandle(e,t){if(0!==t)return!1;let i=e.getUint16(2);if(i>50)return!1;let n=16,s=[];for(;n<i;)s.push(e.getString(n,4)),n+=4;return s.includes(this.type)}async parse(){let e=this.file.getUint32(0),t=this.parseBoxHead(e);for(;\"meta\"!==t.kind;)e+=t.length,await this.file.ensureChunk(e,16),t=this.parseBoxHead(e);await this.file.ensureChunk(t.offset,t.length),this.parseBoxFullHead(t),this.parseSubBoxes(t),this.options.icc.enabled&&await this.findIcc(t),this.options.tiff.enabled&&await this.findExif(t)}async registerSegment(e,t,i){await this.file.ensureChunk(t,i);let n=this.file.subarray(t,i);this.createParser(e,n)}async findIcc(e){let t=this.findBox(e,\"iprp\");if(void 0===t)return;let i=this.findBox(t,\"ipco\");if(void 0===i)return;let n=this.findBox(i,\"colr\");void 0!==n&&await this.registerSegment(\"icc\",n.offset+12,n.length)}async findExif(e){let t=this.findBox(e,\"iinf\");if(void 0===t)return;let i=this.findBox(e,\"iloc\");if(void 0===i)return;let n=this.findExifLocIdInIinf(t),s=this.findExtentInIloc(i,n);if(void 0===s)return;let[r,a]=s;await this.file.ensureChunk(r,a);let o=4+this.file.getUint32(r);r+=o,a-=o,await this.registerSegment(\"tiff\",r,a)}findExifLocIdInIinf(e){this.parseBoxFullHead(e);let t,i,n,s,r=e.start,a=this.file.getUint16(r);for(r+=2;a--;){if(t=this.parseBoxHead(r),this.parseBoxFullHead(t),i=t.start,t.version>=2&&(n=3===t.version?4:2,s=this.file.getString(i+n+2,4),\"Exif\"===s))return this.file.getUintBytes(i,n);r+=t.length}}get8bits(e){let t=this.file.getUint8(e);return[t>>4,15&t]}findExtentInIloc(e,t){this.parseBoxFullHead(e);let i=e.start,[n,s]=this.get8bits(i++),[r,a]=this.get8bits(i++),o=2===e.version?4:2,l=1===e.version||2===e.version?2:0,h=a+n+s,u=2===e.version?4:2,c=this.file.getUintBytes(i,u);for(i+=u;c--;){let e=this.file.getUintBytes(i,o);i+=o+l+2+r;let u=this.file.getUint16(i);if(i+=2,e===t)return u>1&&console.warn(\"ILOC box has more than one extent but we're only processing one\\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file\"),[this.file.getUintBytes(i+a,n),this.file.getUintBytes(i+a+n,s)];i+=u*h}}}class Ue extends Le{}c(Ue,\"type\",\"heic\");class Fe extends Le{}c(Fe,\"type\",\"avif\"),w.set(\"heic\",Ue),w.set(\"avif\",Fe),U(E,[\"ifd0\",\"ifd1\"],[[256,\"ImageWidth\"],[257,\"ImageHeight\"],[258,\"BitsPerSample\"],[259,\"Compression\"],[262,\"PhotometricInterpretation\"],[270,\"ImageDescription\"],[271,\"Make\"],[272,\"Model\"],[273,\"StripOffsets\"],[274,\"Orientation\"],[277,\"SamplesPerPixel\"],[278,\"RowsPerStrip\"],[279,\"StripByteCounts\"],[282,\"XResolution\"],[283,\"YResolution\"],[284,\"PlanarConfiguration\"],[296,\"ResolutionUnit\"],[301,\"TransferFunction\"],[305,\"Software\"],[306,\"ModifyDate\"],[315,\"Artist\"],[316,\"HostComputer\"],[317,\"Predictor\"],[318,\"WhitePoint\"],[319,\"PrimaryChromaticities\"],[513,\"ThumbnailOffset\"],[514,\"ThumbnailLength\"],[529,\"YCbCrCoefficients\"],[530,\"YCbCrSubSampling\"],[531,\"YCbCrPositioning\"],[532,\"ReferenceBlackWhite\"],[700,\"ApplicationNotes\"],[33432,\"Copyright\"],[33723,\"IPTC\"],[34665,\"ExifIFD\"],[34675,\"ICC\"],[34853,\"GpsIFD\"],[330,\"SubIFD\"],[40965,\"InteropIFD\"],[40091,\"XPTitle\"],[40092,\"XPComment\"],[40093,\"XPAuthor\"],[40094,\"XPKeywords\"],[40095,\"XPSubject\"]]),U(E,\"exif\",[[33434,\"ExposureTime\"],[33437,\"FNumber\"],[34850,\"ExposureProgram\"],[34852,\"SpectralSensitivity\"],[34855,\"ISO\"],[34858,\"TimeZoneOffset\"],[34859,\"SelfTimerMode\"],[34864,\"SensitivityType\"],[34865,\"StandardOutputSensitivity\"],[34866,\"RecommendedExposureIndex\"],[34867,\"ISOSpeed\"],[34868,\"ISOSpeedLatitudeyyy\"],[34869,\"ISOSpeedLatitudezzz\"],[36864,\"ExifVersion\"],[36867,\"DateTimeOriginal\"],[36868,\"CreateDate\"],[36873,\"GooglePlusUploadCode\"],[36880,\"OffsetTime\"],[36881,\"OffsetTimeOriginal\"],[36882,\"OffsetTimeDigitized\"],[37121,\"ComponentsConfiguration\"],[37122,\"CompressedBitsPerPixel\"],[37377,\"ShutterSpeedValue\"],[37378,\"ApertureValue\"],[37379,\"BrightnessValue\"],[37380,\"ExposureCompensation\"],[37381,\"MaxApertureValue\"],[37382,\"SubjectDistance\"],[37383,\"MeteringMode\"],[37384,\"LightSource\"],[37385,\"Flash\"],[37386,\"FocalLength\"],[37393,\"ImageNumber\"],[37394,\"SecurityClassification\"],[37395,\"ImageHistory\"],[37396,\"SubjectArea\"],[37500,\"MakerNote\"],[37510,\"UserComment\"],[37520,\"SubSecTime\"],[37521,\"SubSecTimeOriginal\"],[37522,\"SubSecTimeDigitized\"],[37888,\"AmbientTemperature\"],[37889,\"Humidity\"],[37890,\"Pressure\"],[37891,\"WaterDepth\"],[37892,\"Acceleration\"],[37893,\"CameraElevationAngle\"],[40960,\"FlashpixVersion\"],[40961,\"ColorSpace\"],[40962,\"ExifImageWidth\"],[40963,\"ExifImageHeight\"],[40964,\"RelatedSoundFile\"],[41483,\"FlashEnergy\"],[41486,\"FocalPlaneXResolution\"],[41487,\"FocalPlaneYResolution\"],[41488,\"FocalPlaneResolutionUnit\"],[41492,\"SubjectLocation\"],[41493,\"ExposureIndex\"],[41495,\"SensingMethod\"],[41728,\"FileSource\"],[41729,\"SceneType\"],[41730,\"CFAPattern\"],[41985,\"CustomRendered\"],[41986,\"ExposureMode\"],[41987,\"WhiteBalance\"],[41988,\"DigitalZoomRatio\"],[41989,\"FocalLengthIn35mmFormat\"],[41990,\"SceneCaptureType\"],[41991,\"GainControl\"],[41992,\"Contrast\"],[41993,\"Saturation\"],[41994,\"Sharpness\"],[41996,\"SubjectDistanceRange\"],[42016,\"ImageUniqueID\"],[42032,\"OwnerName\"],[42033,\"SerialNumber\"],[42034,\"LensInfo\"],[42035,\"LensMake\"],[42036,\"LensModel\"],[42037,\"LensSerialNumber\"],[42080,\"CompositeImage\"],[42081,\"CompositeImageCount\"],[42082,\"CompositeImageExposureTimes\"],[42240,\"Gamma\"],[59932,\"Padding\"],[59933,\"OffsetSchema\"],[65e3,\"OwnerName\"],[65001,\"SerialNumber\"],[65002,\"Lens\"],[65100,\"RawFile\"],[65101,\"Converter\"],[65102,\"WhiteBalance\"],[65105,\"Exposure\"],[65106,\"Shadows\"],[65107,\"Brightness\"],[65108,\"Contrast\"],[65109,\"Saturation\"],[65110,\"Sharpness\"],[65111,\"Smoothness\"],[65112,\"MoireFilter\"],[40965,\"InteropIFD\"]]),U(E,\"gps\",[[0,\"GPSVersionID\"],[1,\"GPSLatitudeRef\"],[2,\"GPSLatitude\"],[3,\"GPSLongitudeRef\"],[4,\"GPSLongitude\"],[5,\"GPSAltitudeRef\"],[6,\"GPSAltitude\"],[7,\"GPSTimeStamp\"],[8,\"GPSSatellites\"],[9,\"GPSStatus\"],[10,\"GPSMeasureMode\"],[11,\"GPSDOP\"],[12,\"GPSSpeedRef\"],[13,\"GPSSpeed\"],[14,\"GPSTrackRef\"],[15,\"GPSTrack\"],[16,\"GPSImgDirectionRef\"],[17,\"GPSImgDirection\"],[18,\"GPSMapDatum\"],[19,\"GPSDestLatitudeRef\"],[20,\"GPSDestLatitude\"],[21,\"GPSDestLongitudeRef\"],[22,\"GPSDestLongitude\"],[23,\"GPSDestBearingRef\"],[24,\"GPSDestBearing\"],[25,\"GPSDestDistanceRef\"],[26,\"GPSDestDistance\"],[27,\"GPSProcessingMethod\"],[28,\"GPSAreaInformation\"],[29,\"GPSDateStamp\"],[30,\"GPSDifferential\"],[31,\"GPSHPositioningError\"]]),U(B,[\"ifd0\",\"ifd1\"],[[274,{1:\"Horizontal (normal)\",2:\"Mirror horizontal\",3:\"Rotate 180\",4:\"Mirror vertical\",5:\"Mirror horizontal and rotate 270 CW\",6:\"Rotate 90 CW\",7:\"Mirror horizontal and rotate 90 CW\",8:\"Rotate 270 CW\"}],[296,{1:\"None\",2:\"inches\",3:\"cm\"}]]);let Ee=U(B,\"exif\",[[34850,{0:\"Not defined\",1:\"Manual\",2:\"Normal program\",3:\"Aperture priority\",4:\"Shutter priority\",5:\"Creative program\",6:\"Action program\",7:\"Portrait mode\",8:\"Landscape mode\"}],[37121,{0:\"-\",1:\"Y\",2:\"Cb\",3:\"Cr\",4:\"R\",5:\"G\",6:\"B\"}],[37383,{0:\"Unknown\",1:\"Average\",2:\"CenterWeightedAverage\",3:\"Spot\",4:\"MultiSpot\",5:\"Pattern\",6:\"Partial\",255:\"Other\"}],[37384,{0:\"Unknown\",1:\"Daylight\",2:\"Fluorescent\",3:\"Tungsten (incandescent light)\",4:\"Flash\",9:\"Fine weather\",10:\"Cloudy weather\",11:\"Shade\",12:\"Daylight fluorescent (D 5700 - 7100K)\",13:\"Day white fluorescent (N 4600 - 5400K)\",14:\"Cool white fluorescent (W 3900 - 4500K)\",15:\"White fluorescent (WW 3200 - 3700K)\",17:\"Standard light A\",18:\"Standard light B\",19:\"Standard light C\",20:\"D55\",21:\"D65\",22:\"D75\",23:\"D50\",24:\"ISO studio tungsten\",255:\"Other\"}],[37385,{0:\"Flash did not fire\",1:\"Flash fired\",5:\"Strobe return light not detected\",7:\"Strobe return light detected\",9:\"Flash fired, compulsory flash mode\",13:\"Flash fired, compulsory flash mode, return light not detected\",15:\"Flash fired, compulsory flash mode, return light detected\",16:\"Flash did not fire, compulsory flash mode\",24:\"Flash did not fire, auto mode\",25:\"Flash fired, auto mode\",29:\"Flash fired, auto mode, return light not detected\",31:\"Flash fired, auto mode, return light detected\",32:\"No flash function\",65:\"Flash fired, red-eye reduction mode\",69:\"Flash fired, red-eye reduction mode, return light not detected\",71:\"Flash fired, red-eye reduction mode, return light detected\",73:\"Flash fired, compulsory flash mode, red-eye reduction mode\",77:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected\",79:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected\",89:\"Flash fired, auto mode, red-eye reduction mode\",93:\"Flash fired, auto mode, return light not detected, red-eye reduction mode\",95:\"Flash fired, auto mode, return light detected, red-eye reduction mode\"}],[41495,{1:\"Not defined\",2:\"One-chip color area sensor\",3:\"Two-chip color area sensor\",4:\"Three-chip color area sensor\",5:\"Color sequential area sensor\",7:\"Trilinear sensor\",8:\"Color sequential linear sensor\"}],[41728,{1:\"Film Scanner\",2:\"Reflection Print Scanner\",3:\"Digital Camera\"}],[41729,{1:\"Directly photographed\"}],[41985,{0:\"Normal\",1:\"Custom\",2:\"HDR (no original saved)\",3:\"HDR (original saved)\",4:\"Original (for HDR)\",6:\"Panorama\",7:\"Portrait HDR\",8:\"Portrait\"}],[41986,{0:\"Auto\",1:\"Manual\",2:\"Auto bracket\"}],[41987,{0:\"Auto\",1:\"Manual\"}],[41990,{0:\"Standard\",1:\"Landscape\",2:\"Portrait\",3:\"Night\",4:\"Other\"}],[41991,{0:\"None\",1:\"Low gain up\",2:\"High gain up\",3:\"Low gain down\",4:\"High gain down\"}],[41996,{0:\"Unknown\",1:\"Macro\",2:\"Close\",3:\"Distant\"}],[42080,{0:\"Unknown\",1:\"Not a Composite Image\",2:\"General Composite Image\",3:\"Composite Image Captured While Shooting\"}]]);const Be={1:\"No absolute unit of measurement\",2:\"Inch\",3:\"Centimeter\"};Ee.set(37392,Be),Ee.set(41488,Be);const Ne={0:\"Normal\",1:\"Low\",2:\"High\"};function Ge(e){return\"object\"==typeof e&&void 0!==e.length?e[0]:e}function Ve(e){let t=Array.from(e).slice(1);return t[1]>15&&(t=t.map((e=>String.fromCharCode(e)))),\"0\"!==t[2]&&0!==t[2]||t.pop(),t.join(\".\")}function ze(e){if(\"string\"==typeof e){var[t,i,n,s,r,a]=e.trim().split(/[-: ]/g).map(Number),o=new Date(t,i-1,n);return Number.isNaN(s)||Number.isNaN(r)||Number.isNaN(a)||(o.setHours(s),o.setMinutes(r),o.setSeconds(a)),Number.isNaN(+o)?e:o}}function He(e){if(\"string\"==typeof e)return e;let t=[];if(0===e[1]&&0===e[e.length-1])for(let i=0;i<e.length;i+=2)t.push(je(e[i+1],e[i]));else for(let i=0;i<e.length;i+=2)t.push(je(e[i],e[i+1]));return m(String.fromCodePoint(...t))}function je(e,t){return e<<8|t}Ee.set(41992,Ne),Ee.set(41993,Ne),Ee.set(41994,Ne),U(N,[\"ifd0\",\"ifd1\"],[[50827,function(e){return\"string\"!=typeof e?b(e):e}],[306,ze],[40091,He],[40092,He],[40093,He],[40094,He],[40095,He]]),U(N,\"exif\",[[40960,Ve],[36864,Ve],[36867,ze],[36868,ze],[40962,Ge],[40963,Ge]]),U(N,\"gps\",[[0,e=>Array.from(e).join(\".\")],[7,e=>Array.from(e).join(\":\")]]);class We extends re{static canHandle(e,t){return 225===e.getUint8(t+1)&&1752462448===e.getUint32(t+4)&&\"http://ns.adobe.com/\"===e.getString(t+4,\"http://ns.adobe.com/\".length)}static headerLength(e,t){return\"http://ns.adobe.com/xmp/extension/\"===e.getString(t+4,\"http://ns.adobe.com/xmp/extension/\".length)?79:4+\"http://ns.adobe.com/xap/1.0/\".length+1}static findPosition(e,t){let i=super.findPosition(e,t);return i.multiSegment=i.extended=79===i.headerLength,i.multiSegment?(i.chunkCount=e.getUint8(t+72),i.chunkNumber=e.getUint8(t+76),0!==e.getUint8(t+77)&&i.chunkNumber++):(i.chunkCount=1/0,i.chunkNumber=-1),i}static handleMultiSegments(e){return e.map((e=>e.chunk.getString())).join(\"\")}normalizeInput(e){return\"string\"==typeof e?e:I.from(e).getString()}parse(e=this.chunk){if(!this.localOptions.parse)return e;e=function(e){let t={},i={};for(let e of Ze)t[e]=[],i[e]=0;return e.replace(et,((e,n,s)=>{if(\"<\"===n){let n=++i[s];return t[s].push(n),`${e}#${n}`}return`${e}#${t[s].pop()}`}))}(e);let t=Xe.findAll(e,\"rdf\",\"Description\");0===t.length&&t.push(new Xe(\"rdf\",\"Description\",void 0,e));let i,n={};for(let e of t)for(let t of e.properties)i=Je(t.ns,n),_e(t,i);return function(e){let t;for(let i in e)t=e[i]=f(e[i]),void 0===t&&delete e[i];return f(e)}(n)}assignToOutput(e,t){if(this.localOptions.parse)for(let[i,n]of Object.entries(t))switch(i){case\"tiff\":this.assignObjectToOutput(e,\"ifd0\",n);break;case\"exif\":this.assignObjectToOutput(e,\"exif\",n);break;case\"xmlns\":break;default:this.assignObjectToOutput(e,i,n)}else e.xmp=t}}c(We,\"type\",\"xmp\"),c(We,\"multiSegment\",!0),T.set(\"xmp\",We);class Ke{static findAll(e){return qe(e,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=(\"[^\"]*\"|'[^']*')/gm).map(Ke.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[3].slice(1,-1);return n=Qe(n),new Ke(t,i,n)}constructor(e,t,i){this.ns=e,this.name=t,this.value=i}serialize(){return this.value}}class Xe{static findAll(e,t,i){if(void 0!==t||void 0!==i){t=t||\"[\\\\w\\\\d-]+\",i=i||\"[\\\\w\\\\d-]+\";var n=new RegExp(`<(${t}):(${i})(#\\\\d+)?((\\\\s+?[\\\\w\\\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\\\s*)(\\\\/>|>([\\\\s\\\\S]*?)<\\\\/\\\\1:\\\\2\\\\3>)`,\"gm\")}else n=/<([\\w\\d-]+):([\\w\\d-]+)(#\\d+)?((\\s+?[\\w\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)/gm;return qe(e,n).map(Xe.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[4],s=e[8];return new Xe(t,i,n,s)}constructor(e,t,i,n){this.ns=e,this.name=t,this.attrString=i,this.innerXml=n,this.attrs=Ke.findAll(i),this.children=Xe.findAll(n),this.value=0===this.children.length?Qe(n):void 0,this.properties=[...this.attrs,...this.children]}get isPrimitive(){return void 0!==this.value&&0===this.attrs.length&&0===this.children.length}get isListContainer(){return 1===this.children.length&&this.children[0].isList}get isList(){let{ns:e,name:t}=this;return\"rdf\"===e&&(\"Seq\"===t||\"Bag\"===t||\"Alt\"===t)}get isListItem(){return\"rdf\"===this.ns&&\"li\"===this.name}serialize(){if(0===this.properties.length&&void 0===this.value)return;if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return $e(this.children.map(Ye));if(this.isListItem&&1===this.children.length&&0===this.attrs.length)return this.children[0].serialize();let e={};for(let t of this.properties)_e(t,e);return void 0!==this.value&&(e.value=this.value),f(e)}}function _e(e,t){let i=e.serialize();void 0!==i&&(t[e.name]=i)}var Ye=e=>e.serialize(),$e=e=>1===e.length?e[0]:e,Je=(e,t)=>t[e]?t[e]:t[e]={};function qe(e,t){let i,n=[];if(!e)return n;for(;null!==(i=t.exec(e));)n.push(i);return n}function Qe(e){if(function(e){return null==e||\"null\"===e||\"undefined\"===e||\"\"===e||\"\"===e.trim()}(e))return;let t=Number(e);if(!Number.isNaN(t))return t;let i=e.toLowerCase();return\"true\"===i||\"false\"!==i&&e.trim()}const Ze=[\"rdf:li\",\"rdf:Seq\",\"rdf:Bag\",\"rdf:Alt\",\"rdf:Description\"],et=new RegExp(`(<|\\\\/)(${Ze.join(\"|\")})`,\"g\");var tt=Object.freeze({__proto__:null,default:Me,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});const it=[\"xmp\",\"icc\",\"iptc\",\"tiff\"],nt=()=>{};async function st(e,t,i){let n=new q(t);n.chunked=!1,void 0===i&&\"string\"==typeof e&&(i=function(e){let t=e.toLowerCase().split(\".\").pop();if(function(e){return\"exif\"===e||\"tiff\"===e||\"tif\"===e}(t))return\"tiff\";if(it.includes(t))return t}(e));let s=await D(e,n);if(i){if(it.includes(i))return rt(i,s,n);g(\"Invalid segment type\")}else{if(function(e){let t=e.getString(0,50).trim();return t.includes(\"<?xpacket\")||t.includes(\"<x:\")}(s))return rt(\"xmp\",s,n);for(let[e]of T){if(!it.includes(e))continue;let t=await rt(e,s,n).catch(nt);if(t)return t}g(\"Unknown file format\")}}async function rt(e,t,i){let n=i[e];return n.enabled=!0,n.parse=!0,T.get(e).parse(t,n)}let at=l(\"fs\",(e=>e.promises));A.set(\"fs\",class extends ve{async readWhole(){this.chunked=!1,this.fs=await at;let e=await this.fs.readFile(this.input);this._swapBuffer(e)}async readChunked(){this.chunked=!0,this.fs=await at,await this.open(),await this.readChunk(0,this.options.firstChunkSize)}async open(){void 0===this.fh&&(this.fh=await this.fs.open(this.input,\"r\"),this.size=(await this.fh.stat(this.input)).size)}async _readChunk(e,t){void 0===this.fh&&await this.open(),e+t>this.size&&(t=this.size-e);var i=this.subarray(e,t,!0);return await this.fh.read(i.dataView,0,t,e),i}async close(){if(this.fh){let e=this.fh;this.fh=void 0,await e.close()}}});A.set(\"base64\",class extends ve{constructor(...e){super(...e),this.input=this.input.replace(/^data:([^;]+);base64,/gim,\"\"),this.size=this.input.length/4*3,this.input.endsWith(\"==\")?this.size-=2:this.input.endsWith(\"=\")&&(this.size-=1)}async _readChunk(e,t){let i,n,r=this.input;void 0===e?(e=0,i=0,n=0):(i=4*Math.floor(e/3),n=e-i/4*3),void 0===t&&(t=this.size);let o=e+t,l=i+4*Math.ceil(o/3);r=r.slice(i,l);let h=Math.min(t,this.size-e);if(a){let t=s.from(r,\"base64\").slice(n,n+h);return this.set(t,e,!0)}{let t=this.subarray(e,h,!0),i=atob(r),s=t.toUint8();for(let e=0;e<h;e++)s[e]=i.charCodeAt(n+e);return t}}});class ot extends se{static canHandle(e,t){return 18761===t||19789===t}extendOptions(e){let{ifd0:t,xmp:i,iptc:n,icc:s}=e;i.enabled&&t.deps.add(700),n.enabled&&t.deps.add(33723),s.enabled&&t.deps.add(34675),t.finalizeFilters()}async parse(){let{tiff:e,xmp:t,iptc:i,icc:n}=this.options;if(e.enabled||t.enabled||i.enabled||n.enabled){let e=Math.max(S(this.options),this.options.chunkSize);await this.file.ensureChunk(0,e),this.createParser(\"tiff\",this.file),this.parsers.tiff.parseHeader(),await this.parsers.tiff.parseIfd0Block(),this.adaptTiffPropAsSegment(\"xmp\"),this.adaptTiffPropAsSegment(\"iptc\"),this.adaptTiffPropAsSegment(\"icc\")}}adaptTiffPropAsSegment(e){if(this.parsers.tiff[e]){let t=this.parsers.tiff[e];this.injectSegment(e,t)}}}c(ot,\"type\",\"tiff\"),w.set(\"tiff\",ot);let lt=l(\"zlib\");const ht=[\"ihdr\",\"iccp\",\"text\",\"itxt\",\"exif\"];class ut extends se{constructor(...e){super(...e),c(this,\"catchError\",(e=>this.errors.push(e))),c(this,\"metaChunks\",[]),c(this,\"unknownChunks\",[])}static canHandle(e,t){return 35152===t&&2303741511===e.getUint32(0)&&218765834===e.getUint32(4)}async parse(){let{file:e}=this;await this.findPngChunksInRange(\"PNG\\r\\n\u001a\\n\".length,e.byteLength),await this.readSegments(this.metaChunks),this.findIhdr(),this.parseTextChunks(),await this.findExif().catch(this.catchError),await this.findXmp().catch(this.catchError),await this.findIcc().catch(this.catchError)}async findPngChunksInRange(e,t){let{file:i}=this;for(;e<t;){let t=i.getUint32(e),n=i.getUint32(e+4),s=i.getString(e+4,4).toLowerCase(),r=t+4+4+4,a={type:s,offset:e,length:r,start:e+4+4,size:t,marker:n};ht.includes(s)?this.metaChunks.push(a):this.unknownChunks.push(a),e+=r}}parseTextChunks(){let e=this.metaChunks.filter((e=>\"text\"===e.type));for(let t of e){let[e,i]=this.file.getString(t.start,t.size).split(\"\\0\");this.injectKeyValToIhdr(e,i)}}injectKeyValToIhdr(e,t){let i=this.parsers.ihdr;i&&i.raw.set(e,t)}findIhdr(){let e=this.metaChunks.find((e=>\"ihdr\"===e.type));e&&!1!==this.options.ihdr.enabled&&this.createParser(\"ihdr\",e.chunk)}async findExif(){let e=this.metaChunks.find((e=>\"exif\"===e.type));e&&this.injectSegment(\"tiff\",e.chunk)}async findXmp(){let e=this.metaChunks.filter((e=>\"itxt\"===e.type));for(let t of e){\"XML:com.adobe.xmp\"===t.chunk.getString(0,\"XML:com.adobe.xmp\".length)&&this.injectSegment(\"xmp\",t.chunk)}}async findIcc(){let e=this.metaChunks.find((e=>\"iccp\"===e.type));if(!e)return;let{chunk:t}=e,i=t.getUint8Array(0,81),s=0;for(;s<80&&0!==i[s];)s++;let r=s+2,a=t.getString(0,s);if(this.injectKeyValToIhdr(\"ProfileName\",a),n){let e=await lt,i=t.getUint8Array(r);i=e.inflateSync(i),this.injectSegment(\"icc\",i)}}}c(ut,\"type\",\"png\"),w.set(\"png\",ut),U(E,\"interop\",[[1,\"InteropIndex\"],[2,\"InteropVersion\"],[4096,\"RelatedImageFileFormat\"],[4097,\"RelatedImageWidth\"],[4098,\"RelatedImageHeight\"]]),F(E,\"ifd0\",[[11,\"ProcessingSoftware\"],[254,\"SubfileType\"],[255,\"OldSubfileType\"],[263,\"Thresholding\"],[264,\"CellWidth\"],[265,\"CellLength\"],[266,\"FillOrder\"],[269,\"DocumentName\"],[280,\"MinSampleValue\"],[281,\"MaxSampleValue\"],[285,\"PageName\"],[286,\"XPosition\"],[287,\"YPosition\"],[290,\"GrayResponseUnit\"],[297,\"PageNumber\"],[321,\"HalftoneHints\"],[322,\"TileWidth\"],[323,\"TileLength\"],[332,\"InkSet\"],[337,\"TargetPrinter\"],[18246,\"Rating\"],[18249,\"RatingPercent\"],[33550,\"PixelScale\"],[34264,\"ModelTransform\"],[34377,\"PhotoshopSettings\"],[50706,\"DNGVersion\"],[50707,\"DNGBackwardVersion\"],[50708,\"UniqueCameraModel\"],[50709,\"LocalizedCameraModel\"],[50736,\"DNGLensInfo\"],[50739,\"ShadowScale\"],[50740,\"DNGPrivateData\"],[33920,\"IntergraphMatrix\"],[33922,\"ModelTiePoint\"],[34118,\"SEMInfo\"],[34735,\"GeoTiffDirectory\"],[34736,\"GeoTiffDoubleParams\"],[34737,\"GeoTiffAsciiParams\"],[50341,\"PrintIM\"],[50721,\"ColorMatrix1\"],[50722,\"ColorMatrix2\"],[50723,\"CameraCalibration1\"],[50724,\"CameraCalibration2\"],[50725,\"ReductionMatrix1\"],[50726,\"ReductionMatrix2\"],[50727,\"AnalogBalance\"],[50728,\"AsShotNeutral\"],[50729,\"AsShotWhiteXY\"],[50730,\"BaselineExposure\"],[50731,\"BaselineNoise\"],[50732,\"BaselineSharpness\"],[50734,\"LinearResponseLimit\"],[50735,\"CameraSerialNumber\"],[50741,\"MakerNoteSafety\"],[50778,\"CalibrationIlluminant1\"],[50779,\"CalibrationIlluminant2\"],[50781,\"RawDataUniqueID\"],[50827,\"OriginalRawFileName\"],[50828,\"OriginalRawFileData\"],[50831,\"AsShotICCProfile\"],[50832,\"AsShotPreProfileMatrix\"],[50833,\"CurrentICCProfile\"],[50834,\"CurrentPreProfileMatrix\"],[50879,\"ColorimetricReference\"],[50885,\"SRawType\"],[50898,\"PanasonicTitle\"],[50899,\"PanasonicTitle2\"],[50931,\"CameraCalibrationSig\"],[50932,\"ProfileCalibrationSig\"],[50933,\"ProfileIFD\"],[50934,\"AsShotProfileName\"],[50936,\"ProfileName\"],[50937,\"ProfileHueSatMapDims\"],[50938,\"ProfileHueSatMapData1\"],[50939,\"ProfileHueSatMapData2\"],[50940,\"ProfileToneCurve\"],[50941,\"ProfileEmbedPolicy\"],[50942,\"ProfileCopyright\"],[50964,\"ForwardMatrix1\"],[50965,\"ForwardMatrix2\"],[50966,\"PreviewApplicationName\"],[50967,\"PreviewApplicationVersion\"],[50968,\"PreviewSettingsName\"],[50969,\"PreviewSettingsDigest\"],[50970,\"PreviewColorSpace\"],[50971,\"PreviewDateTime\"],[50972,\"RawImageDigest\"],[50973,\"OriginalRawFileDigest\"],[50981,\"ProfileLookTableDims\"],[50982,\"ProfileLookTableData\"],[51043,\"TimeCodes\"],[51044,\"FrameRate\"],[51058,\"TStop\"],[51081,\"ReelName\"],[51089,\"OriginalDefaultFinalSize\"],[51090,\"OriginalBestQualitySize\"],[51091,\"OriginalDefaultCropSize\"],[51105,\"CameraLabel\"],[51107,\"ProfileHueSatMapEncoding\"],[51108,\"ProfileLookTableEncoding\"],[51109,\"BaselineExposureOffset\"],[51110,\"DefaultBlackRender\"],[51111,\"NewRawImageDigest\"],[51112,\"RawToPreviewGain\"]]);let ct=[[273,\"StripOffsets\"],[279,\"StripByteCounts\"],[288,\"FreeOffsets\"],[289,\"FreeByteCounts\"],[291,\"GrayResponseCurve\"],[292,\"T4Options\"],[293,\"T6Options\"],[300,\"ColorResponseUnit\"],[320,\"ColorMap\"],[324,\"TileOffsets\"],[325,\"TileByteCounts\"],[326,\"BadFaxLines\"],[327,\"CleanFaxData\"],[328,\"ConsecutiveBadFaxLines\"],[330,\"SubIFD\"],[333,\"InkNames\"],[334,\"NumberofInks\"],[336,\"DotRange\"],[338,\"ExtraSamples\"],[339,\"SampleFormat\"],[340,\"SMinSampleValue\"],[341,\"SMaxSampleValue\"],[342,\"TransferRange\"],[343,\"ClipPath\"],[344,\"XClipPathUnits\"],[345,\"YClipPathUnits\"],[346,\"Indexed\"],[347,\"JPEGTables\"],[351,\"OPIProxy\"],[400,\"GlobalParametersIFD\"],[401,\"ProfileType\"],[402,\"FaxProfile\"],[403,\"CodingMethods\"],[404,\"VersionYear\"],[405,\"ModeNumber\"],[433,\"Decode\"],[434,\"DefaultImageColor\"],[435,\"T82Options\"],[437,\"JPEGTables\"],[512,\"JPEGProc\"],[515,\"JPEGRestartInterval\"],[517,\"JPEGLosslessPredictors\"],[518,\"JPEGPointTransforms\"],[519,\"JPEGQTables\"],[520,\"JPEGDCTables\"],[521,\"JPEGACTables\"],[559,\"StripRowCounts\"],[999,\"USPTOMiscellaneous\"],[18247,\"XP_DIP_XML\"],[18248,\"StitchInfo\"],[28672,\"SonyRawFileType\"],[28688,\"SonyToneCurve\"],[28721,\"VignettingCorrection\"],[28722,\"VignettingCorrParams\"],[28724,\"ChromaticAberrationCorrection\"],[28725,\"ChromaticAberrationCorrParams\"],[28726,\"DistortionCorrection\"],[28727,\"DistortionCorrParams\"],[29895,\"SonyCropTopLeft\"],[29896,\"SonyCropSize\"],[32781,\"ImageID\"],[32931,\"WangTag1\"],[32932,\"WangAnnotation\"],[32933,\"WangTag3\"],[32934,\"WangTag4\"],[32953,\"ImageReferencePoints\"],[32954,\"RegionXformTackPoint\"],[32955,\"WarpQuadrilateral\"],[32956,\"AffineTransformMat\"],[32995,\"Matteing\"],[32996,\"DataType\"],[32997,\"ImageDepth\"],[32998,\"TileDepth\"],[33300,\"ImageFullWidth\"],[33301,\"ImageFullHeight\"],[33302,\"TextureFormat\"],[33303,\"WrapModes\"],[33304,\"FovCot\"],[33305,\"MatrixWorldToScreen\"],[33306,\"MatrixWorldToCamera\"],[33405,\"Model2\"],[33421,\"CFARepeatPatternDim\"],[33422,\"CFAPattern2\"],[33423,\"BatteryLevel\"],[33424,\"KodakIFD\"],[33445,\"MDFileTag\"],[33446,\"MDScalePixel\"],[33447,\"MDColorTable\"],[33448,\"MDLabName\"],[33449,\"MDSampleInfo\"],[33450,\"MDPrepDate\"],[33451,\"MDPrepTime\"],[33452,\"MDFileUnits\"],[33589,\"AdventScale\"],[33590,\"AdventRevision\"],[33628,\"UIC1Tag\"],[33629,\"UIC2Tag\"],[33630,\"UIC3Tag\"],[33631,\"UIC4Tag\"],[33918,\"IntergraphPacketData\"],[33919,\"IntergraphFlagRegisters\"],[33921,\"INGRReserved\"],[34016,\"Site\"],[34017,\"ColorSequence\"],[34018,\"IT8Header\"],[34019,\"RasterPadding\"],[34020,\"BitsPerRunLength\"],[34021,\"BitsPerExtendedRunLength\"],[34022,\"ColorTable\"],[34023,\"ImageColorIndicator\"],[34024,\"BackgroundColorIndicator\"],[34025,\"ImageColorValue\"],[34026,\"BackgroundColorValue\"],[34027,\"PixelIntensityRange\"],[34028,\"TransparencyIndicator\"],[34029,\"ColorCharacterization\"],[34030,\"HCUsage\"],[34031,\"TrapIndicator\"],[34032,\"CMYKEquivalent\"],[34152,\"AFCP_IPTC\"],[34232,\"PixelMagicJBIGOptions\"],[34263,\"JPLCartoIFD\"],[34306,\"WB_GRGBLevels\"],[34310,\"LeafData\"],[34687,\"TIFF_FXExtensions\"],[34688,\"MultiProfiles\"],[34689,\"SharedData\"],[34690,\"T88Options\"],[34732,\"ImageLayer\"],[34750,\"JBIGOptions\"],[34856,\"Opto-ElectricConvFactor\"],[34857,\"Interlace\"],[34908,\"FaxRecvParams\"],[34909,\"FaxSubAddress\"],[34910,\"FaxRecvTime\"],[34929,\"FedexEDR\"],[34954,\"LeafSubIFD\"],[37387,\"FlashEnergy\"],[37388,\"SpatialFrequencyResponse\"],[37389,\"Noise\"],[37390,\"FocalPlaneXResolution\"],[37391,\"FocalPlaneYResolution\"],[37392,\"FocalPlaneResolutionUnit\"],[37397,\"ExposureIndex\"],[37398,\"TIFF-EPStandardID\"],[37399,\"SensingMethod\"],[37434,\"CIP3DataFile\"],[37435,\"CIP3Sheet\"],[37436,\"CIP3Side\"],[37439,\"StoNits\"],[37679,\"MSDocumentText\"],[37680,\"MSPropertySetStorage\"],[37681,\"MSDocumentTextPosition\"],[37724,\"ImageSourceData\"],[40965,\"InteropIFD\"],[40976,\"SamsungRawPointersOffset\"],[40977,\"SamsungRawPointersLength\"],[41217,\"SamsungRawByteOrder\"],[41218,\"SamsungRawUnknown\"],[41484,\"SpatialFrequencyResponse\"],[41485,\"Noise\"],[41489,\"ImageNumber\"],[41490,\"SecurityClassification\"],[41491,\"ImageHistory\"],[41494,\"TIFF-EPStandardID\"],[41995,\"DeviceSettingDescription\"],[42112,\"GDALMetadata\"],[42113,\"GDALNoData\"],[44992,\"ExpandSoftware\"],[44993,\"ExpandLens\"],[44994,\"ExpandFilm\"],[44995,\"ExpandFilterLens\"],[44996,\"ExpandScanner\"],[44997,\"ExpandFlashLamp\"],[46275,\"HasselbladRawImage\"],[48129,\"PixelFormat\"],[48130,\"Transformation\"],[48131,\"Uncompressed\"],[48132,\"ImageType\"],[48256,\"ImageWidth\"],[48257,\"ImageHeight\"],[48258,\"WidthResolution\"],[48259,\"HeightResolution\"],[48320,\"ImageOffset\"],[48321,\"ImageByteCount\"],[48322,\"AlphaOffset\"],[48323,\"AlphaByteCount\"],[48324,\"ImageDataDiscard\"],[48325,\"AlphaDataDiscard\"],[50215,\"OceScanjobDesc\"],[50216,\"OceApplicationSelector\"],[50217,\"OceIDNumber\"],[50218,\"OceImageLogic\"],[50255,\"Annotations\"],[50459,\"HasselbladExif\"],[50547,\"OriginalFileName\"],[50560,\"USPTOOriginalContentType\"],[50656,\"CR2CFAPattern\"],[50710,\"CFAPlaneColor\"],[50711,\"CFALayout\"],[50712,\"LinearizationTable\"],[50713,\"BlackLevelRepeatDim\"],[50714,\"BlackLevel\"],[50715,\"BlackLevelDeltaH\"],[50716,\"BlackLevelDeltaV\"],[50717,\"WhiteLevel\"],[50718,\"DefaultScale\"],[50719,\"DefaultCropOrigin\"],[50720,\"DefaultCropSize\"],[50733,\"BayerGreenSplit\"],[50737,\"ChromaBlurRadius\"],[50738,\"AntiAliasStrength\"],[50752,\"RawImageSegmentation\"],[50780,\"BestQualityScale\"],[50784,\"AliasLayerMetadata\"],[50829,\"ActiveArea\"],[50830,\"MaskedAreas\"],[50935,\"NoiseReductionApplied\"],[50974,\"SubTileBlockSize\"],[50975,\"RowInterleaveFactor\"],[51008,\"OpcodeList1\"],[51009,\"OpcodeList2\"],[51022,\"OpcodeList3\"],[51041,\"NoiseProfile\"],[51114,\"CacheVersion\"],[51125,\"DefaultUserCrop\"],[51157,\"NikonNEFInfo\"],[65024,\"KdcIFD\"]];F(E,\"ifd0\",ct),F(E,\"exif\",ct),U(B,\"gps\",[[23,{M:\"Magnetic North\",T:\"True North\"}],[25,{K:\"Kilometers\",M:\"Miles\",N:\"Nautical Miles\"}]]);class ft extends re{static canHandle(e,t){return 224===e.getUint8(t+1)&&1246120262===e.getUint32(t+4)&&0===e.getUint8(t+8)}parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint16(0)],[2,this.chunk.getUint8(2)],[3,this.chunk.getUint16(3)],[5,this.chunk.getUint16(5)],[7,this.chunk.getUint8(7)],[8,this.chunk.getUint8(8)]])}}c(ft,\"type\",\"jfif\"),c(ft,\"headerLength\",9),T.set(\"jfif\",ft),U(E,\"jfif\",[[0,\"JFIFVersion\"],[2,\"ResolutionUnit\"],[3,\"XResolution\"],[5,\"YResolution\"],[7,\"ThumbnailWidth\"],[8,\"ThumbnailHeight\"]]);class dt extends re{parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint32(0)],[4,this.chunk.getUint32(4)],[8,this.chunk.getUint8(8)],[9,this.chunk.getUint8(9)],[10,this.chunk.getUint8(10)],[11,this.chunk.getUint8(11)],[12,this.chunk.getUint8(12)],...Array.from(this.raw)])}}c(dt,\"type\",\"ihdr\"),T.set(\"ihdr\",dt),U(E,\"ihdr\",[[0,\"ImageWidth\"],[4,\"ImageHeight\"],[8,\"BitDepth\"],[9,\"ColorType\"],[10,\"Compression\"],[11,\"Filter\"],[12,\"Interlace\"]]),U(B,\"ihdr\",[[9,{0:\"Grayscale\",2:\"RGB\",3:\"Palette\",4:\"Grayscale with Alpha\",6:\"RGB with Alpha\",DEFAULT:\"Unknown\"}],[10,{0:\"Deflate/Inflate\",DEFAULT:\"Unknown\"}],[11,{0:\"Adaptive\",DEFAULT:\"Unknown\"}],[12,{0:\"Noninterlaced\",1:\"Adam7 Interlace\",DEFAULT:\"Unknown\"}]]);class pt extends re{static canHandle(e,t){return 226===e.getUint8(t+1)&&1229144927===e.getUint32(t+4)}static findPosition(e,t){let i=super.findPosition(e,t);return i.chunkNumber=e.getUint8(t+16),i.chunkCount=e.getUint8(t+17),i.multiSegment=i.chunkCount>1,i}static handleMultiSegments(e){return function(e){let t=function(e){let t=e[0].constructor,i=0;for(let t of e)i+=t.length;let n=new t(i),s=0;for(let t of e)n.set(t,s),s+=t.length;return n}(e.map((e=>e.chunk.toUint8())));return new I(t)}(e)}parse(){return this.raw=new Map,this.parseHeader(),this.parseTags(),this.translate(),this.output}parseHeader(){let{raw:e}=this;this.chunk.byteLength<84&&g(\"ICC header is too short\");for(let[t,i]of Object.entries(gt)){t=parseInt(t,10);let n=i(this.chunk,t);\"\\0\\0\\0\\0\"!==n&&e.set(t,n)}}parseTags(){let e,t,i,n,s,{raw:r}=this,a=this.chunk.getUint32(128),o=132,l=this.chunk.byteLength;for(;a--;){if(e=this.chunk.getString(o,4),t=this.chunk.getUint32(o+4),i=this.chunk.getUint32(o+8),n=this.chunk.getString(t,4),t+i>l)return void console.warn(\"reached the end of the first ICC chunk. Enable options.tiff.multiSegment to read all ICC segments.\");s=this.parseTag(n,t,i),void 0!==s&&\"\\0\\0\\0\\0\"!==s&&r.set(e,s),o+=12}}parseTag(e,t,i){switch(e){case\"desc\":return this.parseDesc(t);case\"mluc\":return this.parseMluc(t);case\"text\":return this.parseText(t,i);case\"sig \":return this.parseSig(t)}if(!(t+i>this.chunk.byteLength))return this.chunk.getUint8Array(t,i)}parseDesc(e){let t=this.chunk.getUint32(e+8)-1;return m(this.chunk.getString(e+12,t))}parseText(e,t){return m(this.chunk.getString(e+8,t-8))}parseSig(e){return m(this.chunk.getString(e+8,4))}parseMluc(e){let{chunk:t}=this,i=t.getUint32(e+8),n=t.getUint32(e+12),s=e+16,r=[];for(let a=0;a<i;a++){let i=t.getString(s+0,2),a=t.getString(s+2,2),o=t.getUint32(s+4),l=t.getUint32(s+8)+e,h=m(t.getUnicodeString(l,o));r.push({lang:i,country:a,text:h}),s+=n}return 1===i?r[0].text:r}translateValue(e,t){return\"string\"==typeof e?t[e]||t[e.toLowerCase()]||e:t[e]||e}}c(pt,\"type\",\"icc\"),c(pt,\"multiSegment\",!0),c(pt,\"headerLength\",18);const gt={4:mt,8:function(e,t){return[e.getUint8(t),e.getUint8(t+1)>>4,e.getUint8(t+1)%16].map((e=>e.toString(10))).join(\".\")},12:mt,16:mt,20:mt,24:function(e,t){const i=e.getUint16(t),n=e.getUint16(t+2)-1,s=e.getUint16(t+4),r=e.getUint16(t+6),a=e.getUint16(t+8),o=e.getUint16(t+10);return new Date(Date.UTC(i,n,s,r,a,o))},36:mt,40:mt,48:mt,52:mt,64:(e,t)=>e.getUint32(t),80:mt};function mt(e,t){return m(e.getString(t,4))}T.set(\"icc\",pt),U(E,\"icc\",[[4,\"ProfileCMMType\"],[8,\"ProfileVersion\"],[12,\"ProfileClass\"],[16,\"ColorSpaceData\"],[20,\"ProfileConnectionSpace\"],[24,\"ProfileDateTime\"],[36,\"ProfileFileSignature\"],[40,\"PrimaryPlatform\"],[44,\"CMMFlags\"],[48,\"DeviceManufacturer\"],[52,\"DeviceModel\"],[56,\"DeviceAttributes\"],[64,\"RenderingIntent\"],[68,\"ConnectionSpaceIlluminant\"],[80,\"ProfileCreator\"],[84,\"ProfileID\"],[\"Header\",\"ProfileHeader\"],[\"MS00\",\"WCSProfiles\"],[\"bTRC\",\"BlueTRC\"],[\"bXYZ\",\"BlueMatrixColumn\"],[\"bfd\",\"UCRBG\"],[\"bkpt\",\"MediaBlackPoint\"],[\"calt\",\"CalibrationDateTime\"],[\"chad\",\"ChromaticAdaptation\"],[\"chrm\",\"Chromaticity\"],[\"ciis\",\"ColorimetricIntentImageState\"],[\"clot\",\"ColorantTableOut\"],[\"clro\",\"ColorantOrder\"],[\"clrt\",\"ColorantTable\"],[\"cprt\",\"ProfileCopyright\"],[\"crdi\",\"CRDInfo\"],[\"desc\",\"ProfileDescription\"],[\"devs\",\"DeviceSettings\"],[\"dmdd\",\"DeviceModelDesc\"],[\"dmnd\",\"DeviceMfgDesc\"],[\"dscm\",\"ProfileDescriptionML\"],[\"fpce\",\"FocalPlaneColorimetryEstimates\"],[\"gTRC\",\"GreenTRC\"],[\"gXYZ\",\"GreenMatrixColumn\"],[\"gamt\",\"Gamut\"],[\"kTRC\",\"GrayTRC\"],[\"lumi\",\"Luminance\"],[\"meas\",\"Measurement\"],[\"meta\",\"Metadata\"],[\"mmod\",\"MakeAndModel\"],[\"ncl2\",\"NamedColor2\"],[\"ncol\",\"NamedColor\"],[\"ndin\",\"NativeDisplayInfo\"],[\"pre0\",\"Preview0\"],[\"pre1\",\"Preview1\"],[\"pre2\",\"Preview2\"],[\"ps2i\",\"PS2RenderingIntent\"],[\"ps2s\",\"PostScript2CSA\"],[\"psd0\",\"PostScript2CRD0\"],[\"psd1\",\"PostScript2CRD1\"],[\"psd2\",\"PostScript2CRD2\"],[\"psd3\",\"PostScript2CRD3\"],[\"pseq\",\"ProfileSequenceDesc\"],[\"psid\",\"ProfileSequenceIdentifier\"],[\"psvm\",\"PS2CRDVMSize\"],[\"rTRC\",\"RedTRC\"],[\"rXYZ\",\"RedMatrixColumn\"],[\"resp\",\"OutputResponse\"],[\"rhoc\",\"ReflectionHardcopyOrigColorimetry\"],[\"rig0\",\"PerceptualRenderingIntentGamut\"],[\"rig2\",\"SaturationRenderingIntentGamut\"],[\"rpoc\",\"ReflectionPrintOutputColorimetry\"],[\"sape\",\"SceneAppearanceEstimates\"],[\"scoe\",\"SceneColorimetryEstimates\"],[\"scrd\",\"ScreeningDesc\"],[\"scrn\",\"Screening\"],[\"targ\",\"CharTarget\"],[\"tech\",\"Technology\"],[\"vcgt\",\"VideoCardGamma\"],[\"view\",\"ViewingConditions\"],[\"vued\",\"ViewingCondDesc\"],[\"wtpt\",\"MediaWhitePoint\"]]);const St={\"4d2p\":\"Erdt Systems\",AAMA:\"Aamazing Technologies\",ACER:\"Acer\",ACLT:\"Acolyte Color Research\",ACTI:\"Actix Sytems\",ADAR:\"Adara Technology\",ADBE:\"Adobe\",ADI:\"ADI Systems\",AGFA:\"Agfa Graphics\",ALMD:\"Alps Electric\",ALPS:\"Alps Electric\",ALWN:\"Alwan Color Expertise\",AMTI:\"Amiable Technologies\",AOC:\"AOC International\",APAG:\"Apago\",APPL:\"Apple Computer\",AST:\"AST\",\"AT&T\":\"AT&T\",BAEL:\"BARBIERI electronic\",BRCO:\"Barco NV\",BRKP:\"Breakpoint\",BROT:\"Brother\",BULL:\"Bull\",BUS:\"Bus Computer Systems\",\"C-IT\":\"C-Itoh\",CAMR:\"Intel\",CANO:\"Canon\",CARR:\"Carroll Touch\",CASI:\"Casio\",CBUS:\"Colorbus PL\",CEL:\"Crossfield\",CELx:\"Crossfield\",CGS:\"CGS Publishing Technologies International\",CHM:\"Rochester Robotics\",CIGL:\"Colour Imaging Group, London\",CITI:\"Citizen\",CL00:\"Candela\",CLIQ:\"Color IQ\",CMCO:\"Chromaco\",CMiX:\"CHROMiX\",COLO:\"Colorgraphic Communications\",COMP:\"Compaq\",COMp:\"Compeq/Focus Technology\",CONR:\"Conrac Display Products\",CORD:\"Cordata Technologies\",CPQ:\"Compaq\",CPRO:\"ColorPro\",CRN:\"Cornerstone\",CTX:\"CTX International\",CVIS:\"ColorVision\",CWC:\"Fujitsu Laboratories\",DARI:\"Darius Technology\",DATA:\"Dataproducts\",DCP:\"Dry Creek Photo\",DCRC:\"Digital Contents Resource Center, Chung-Ang University\",DELL:\"Dell Computer\",DIC:\"Dainippon Ink and Chemicals\",DICO:\"Diconix\",DIGI:\"Digital\",\"DL&C\":\"Digital Light & Color\",DPLG:\"Doppelganger\",DS:\"Dainippon Screen\",DSOL:\"DOOSOL\",DUPN:\"DuPont\",EPSO:\"Epson\",ESKO:\"Esko-Graphics\",ETRI:\"Electronics and Telecommunications Research Institute\",EVER:\"Everex Systems\",EXAC:\"ExactCODE\",Eizo:\"Eizo\",FALC:\"Falco Data Products\",FF:\"Fuji Photo Film\",FFEI:\"FujiFilm Electronic Imaging\",FNRD:\"Fnord Software\",FORA:\"Fora\",FORE:\"Forefront Technology\",FP:\"Fujitsu\",FPA:\"WayTech Development\",FUJI:\"Fujitsu\",FX:\"Fuji Xerox\",GCC:\"GCC Technologies\",GGSL:\"Global Graphics Software\",GMB:\"Gretagmacbeth\",GMG:\"GMG\",GOLD:\"GoldStar Technology\",GOOG:\"Google\",GPRT:\"Giantprint\",GTMB:\"Gretagmacbeth\",GVC:\"WayTech Development\",GW2K:\"Sony\",HCI:\"HCI\",HDM:\"Heidelberger Druckmaschinen\",HERM:\"Hermes\",HITA:\"Hitachi America\",HP:\"Hewlett-Packard\",HTC:\"Hitachi\",HiTi:\"HiTi Digital\",IBM:\"IBM\",IDNT:\"Scitex\",IEC:\"Hewlett-Packard\",IIYA:\"Iiyama North America\",IKEG:\"Ikegami Electronics\",IMAG:\"Image Systems\",IMI:\"Ingram Micro\",INTC:\"Intel\",INTL:\"N/A (INTL)\",INTR:\"Intra Electronics\",IOCO:\"Iocomm International Technology\",IPS:\"InfoPrint Solutions Company\",IRIS:\"Scitex\",ISL:\"Ichikawa Soft Laboratory\",ITNL:\"N/A (ITNL)\",IVM:\"IVM\",IWAT:\"Iwatsu Electric\",Idnt:\"Scitex\",Inca:\"Inca Digital Printers\",Iris:\"Scitex\",JPEG:\"Joint Photographic Experts Group\",JSFT:\"Jetsoft Development\",JVC:\"JVC Information Products\",KART:\"Scitex\",KFC:\"KFC Computek Components\",KLH:\"KLH Computers\",KMHD:\"Konica Minolta\",KNCA:\"Konica\",KODA:\"Kodak\",KYOC:\"Kyocera\",Kart:\"Scitex\",LCAG:\"Leica\",LCCD:\"Leeds Colour\",LDAK:\"Left Dakota\",LEAD:\"Leading Technology\",LEXM:\"Lexmark International\",LINK:\"Link Computer\",LINO:\"Linotronic\",LITE:\"Lite-On\",Leaf:\"Leaf\",Lino:\"Linotronic\",MAGC:\"Mag Computronic\",MAGI:\"MAG Innovision\",MANN:\"Mannesmann\",MICN:\"Micron Technology\",MICR:\"Microtek\",MICV:\"Microvitec\",MINO:\"Minolta\",MITS:\"Mitsubishi Electronics America\",MITs:\"Mitsuba\",MNLT:\"Minolta\",MODG:\"Modgraph\",MONI:\"Monitronix\",MONS:\"Monaco Systems\",MORS:\"Morse Technology\",MOTI:\"Motive Systems\",MSFT:\"Microsoft\",MUTO:\"MUTOH INDUSTRIES\",Mits:\"Mitsubishi Electric\",NANA:\"NANAO\",NEC:\"NEC\",NEXP:\"NexPress Solutions\",NISS:\"Nissei Sangyo America\",NKON:\"Nikon\",NONE:\"none\",OCE:\"Oce Technologies\",OCEC:\"OceColor\",OKI:\"Oki\",OKID:\"Okidata\",OKIP:\"Okidata\",OLIV:\"Olivetti\",OLYM:\"Olympus\",ONYX:\"Onyx Graphics\",OPTI:\"Optiquest\",PACK:\"Packard Bell\",PANA:\"Matsushita Electric Industrial\",PANT:\"Pantone\",PBN:\"Packard Bell\",PFU:\"PFU\",PHIL:\"Philips Consumer Electronics\",PNTX:\"HOYA\",POne:\"Phase One A/S\",PREM:\"Premier Computer Innovations\",PRIN:\"Princeton Graphic Systems\",PRIP:\"Princeton Publishing Labs\",QLUX:\"Hong Kong\",QMS:\"QMS\",QPCD:\"QPcard AB\",QUAD:\"QuadLaser\",QUME:\"Qume\",RADI:\"Radius\",RDDx:\"Integrated Color Solutions\",RDG:\"Roland DG\",REDM:\"REDMS Group\",RELI:\"Relisys\",RGMS:\"Rolf Gierling Multitools\",RICO:\"Ricoh\",RNLD:\"Edmund Ronald\",ROYA:\"Royal\",RPC:\"Ricoh Printing Systems\",RTL:\"Royal Information Electronics\",SAMP:\"Sampo\",SAMS:\"Samsung\",SANT:\"Jaime Santana Pomares\",SCIT:\"Scitex\",SCRN:\"Dainippon Screen\",SDP:\"Scitex\",SEC:\"Samsung\",SEIK:\"Seiko Instruments\",SEIk:\"Seikosha\",SGUY:\"ScanGuy.com\",SHAR:\"Sharp Laboratories\",SICC:\"International Color Consortium\",SONY:\"Sony\",SPCL:\"SpectraCal\",STAR:\"Star\",STC:\"Sampo Technology\",Scit:\"Scitex\",Sdp:\"Scitex\",Sony:\"Sony\",TALO:\"Talon Technology\",TAND:\"Tandy\",TATU:\"Tatung\",TAXA:\"TAXAN America\",TDS:\"Tokyo Denshi Sekei\",TECO:\"TECO Information Systems\",TEGR:\"Tegra\",TEKT:\"Tektronix\",TI:\"Texas Instruments\",TMKR:\"TypeMaker\",TOSB:\"Toshiba\",TOSH:\"Toshiba\",TOTK:\"TOTOKU ELECTRIC\",TRIU:\"Triumph\",TSBT:\"Toshiba\",TTX:\"TTX Computer Products\",TVM:\"TVM Professional Monitor\",TW:\"TW Casper\",ULSX:\"Ulead Systems\",UNIS:\"Unisys\",UTZF:\"Utz Fehlau & Sohn\",VARI:\"Varityper\",VIEW:\"Viewsonic\",VISL:\"Visual communication\",VIVO:\"Vivo Mobile Communication\",WANG:\"Wang\",WLBR:\"Wilbur Imaging\",WTG2:\"Ware To Go\",WYSE:\"WYSE Technology\",XERX:\"Xerox\",XRIT:\"X-Rite\",ZRAN:\"Zoran\",Zebr:\"Zebra Technologies\",appl:\"Apple Computer\",bICC:\"basICColor\",berg:\"bergdesign\",ceyd:\"Integrated Color Solutions\",clsp:\"MacDermid ColorSpan\",ds:\"Dainippon Screen\",dupn:\"DuPont\",ffei:\"FujiFilm Electronic Imaging\",flux:\"FluxData\",iris:\"Scitex\",kart:\"Scitex\",lcms:\"Little CMS\",lino:\"Linotronic\",none:\"none\",ob4d:\"Erdt Systems\",obic:\"Medigraph\",quby:\"Qubyx Sarl\",scit:\"Scitex\",scrn:\"Dainippon Screen\",sdp:\"Scitex\",siwi:\"SIWI GRAFIKA\",yxym:\"YxyMaster\"},Ct={scnr:\"Scanner\",mntr:\"Monitor\",prtr:\"Printer\",link:\"Device Link\",abst:\"Abstract\",spac:\"Color Space Conversion Profile\",nmcl:\"Named Color\",cenc:\"ColorEncodingSpace profile\",mid:\"MultiplexIdentification profile\",mlnk:\"MultiplexLink profile\",mvis:\"MultiplexVisualization profile\",nkpf:\"Nikon Input Device Profile (NON-STANDARD!)\"};U(B,\"icc\",[[4,St],[12,Ct],[40,Object.assign({},St,Ct)],[48,St],[80,St],[64,{0:\"Perceptual\",1:\"Relative Colorimetric\",2:\"Saturation\",3:\"Absolute Colorimetric\"}],[\"tech\",{amd:\"Active Matrix Display\",crt:\"Cathode Ray Tube Display\",kpcd:\"Photo CD\",pmd:\"Passive Matrix Display\",dcam:\"Digital Camera\",dcpj:\"Digital Cinema Projector\",dmpc:\"Digital Motion Picture Camera\",dsub:\"Dye Sublimation Printer\",epho:\"Electrophotographic Printer\",esta:\"Electrostatic Printer\",flex:\"Flexography\",fprn:\"Film Writer\",fscn:\"Film Scanner\",grav:\"Gravure\",ijet:\"Ink Jet Printer\",imgs:\"Photo Image Setter\",mpfr:\"Motion Picture Film Recorder\",mpfs:\"Motion Picture Film Scanner\",offs:\"Offset Lithography\",pjtv:\"Projection Television\",rpho:\"Photographic Paper Printer\",rscn:\"Reflective Scanner\",silk:\"Silkscreen\",twax:\"Thermal Wax Printer\",vidc:\"Video Camera\",vidm:\"Video Monitor\"}]]);class yt extends re{static canHandle(e,t,i){return 237===e.getUint8(t+1)&&\"Photoshop\"===e.getString(t+4,9)&&void 0!==this.containsIptc8bim(e,t,i)}static headerLength(e,t,i){let n,s=this.containsIptc8bim(e,t,i);if(void 0!==s)return n=e.getUint8(t+s+7),n%2!=0&&(n+=1),0===n&&(n=4),s+8+n}static containsIptc8bim(e,t,i){for(let n=0;n<i;n++)if(this.isIptcSegmentHead(e,t+n))return n}static isIptcSegmentHead(e,t){return 56===e.getUint8(t)&&943868237===e.getUint32(t)&&1028===e.getUint16(t+4)}parse(){let{raw:e}=this,t=this.chunk.byteLength-1,i=!1;for(let n=0;n<t;n++)if(28===this.chunk.getUint8(n)&&2===this.chunk.getUint8(n+1)){i=!0;let t=this.chunk.getUint16(n+3),s=this.chunk.getUint8(n+2),r=this.chunk.getLatin1String(n+5,t);e.set(s,this.pluralizeValue(e.get(s),r)),n+=4+t}else if(i)break;return this.translate(),this.output}pluralizeValue(e,t){return void 0!==e?e instanceof Array?(e.push(t),e):[e,t]:t}}c(yt,\"type\",\"iptc\"),c(yt,\"translateValues\",!1),c(yt,\"reviveValues\",!1),T.set(\"iptc\",yt),U(E,\"iptc\",[[0,\"ApplicationRecordVersion\"],[3,\"ObjectTypeReference\"],[4,\"ObjectAttributeReference\"],[5,\"ObjectName\"],[7,\"EditStatus\"],[8,\"EditorialUpdate\"],[10,\"Urgency\"],[12,\"SubjectReference\"],[15,\"Category\"],[20,\"SupplementalCategories\"],[22,\"FixtureIdentifier\"],[25,\"Keywords\"],[26,\"ContentLocationCode\"],[27,\"ContentLocationName\"],[30,\"ReleaseDate\"],[35,\"ReleaseTime\"],[37,\"ExpirationDate\"],[38,\"ExpirationTime\"],[40,\"SpecialInstructions\"],[42,\"ActionAdvised\"],[45,\"ReferenceService\"],[47,\"ReferenceDate\"],[50,\"ReferenceNumber\"],[55,\"DateCreated\"],[60,\"TimeCreated\"],[62,\"DigitalCreationDate\"],[63,\"DigitalCreationTime\"],[65,\"OriginatingProgram\"],[70,\"ProgramVersion\"],[75,\"ObjectCycle\"],[80,\"Byline\"],[85,\"BylineTitle\"],[90,\"City\"],[92,\"Sublocation\"],[95,\"State\"],[100,\"CountryCode\"],[101,\"Country\"],[103,\"OriginalTransmissionReference\"],[105,\"Headline\"],[110,\"Credit\"],[115,\"Source\"],[116,\"CopyrightNotice\"],[118,\"Contact\"],[120,\"Caption\"],[121,\"LocalCaption\"],[122,\"Writer\"],[125,\"RasterizedCaption\"],[130,\"ImageType\"],[131,\"ImageOrientation\"],[135,\"LanguageIdentifier\"],[150,\"AudioType\"],[151,\"AudioSamplingRate\"],[152,\"AudioSamplingResolution\"],[153,\"AudioDuration\"],[154,\"AudioOutcue\"],[184,\"JobID\"],[185,\"MasterDocumentID\"],[186,\"ShortDocumentID\"],[187,\"UniqueDocumentID\"],[188,\"OwnerID\"],[200,\"ObjectPreviewFileFormat\"],[201,\"ObjectPreviewFileVersion\"],[202,\"ObjectPreviewData\"],[221,\"Prefs\"],[225,\"ClassifyState\"],[228,\"SimilarityIndex\"],[230,\"DocumentNotes\"],[231,\"DocumentHistory\"],[232,\"ExifCameraInfo\"],[255,\"CatalogSets\"]]),U(B,\"iptc\",[[10,{0:\"0 (reserved)\",1:\"1 (most urgent)\",2:\"2\",3:\"3\",4:\"4\",5:\"5 (normal urgency)\",6:\"6\",7:\"7\",8:\"8 (least urgent)\",9:\"9 (user-defined priority)\"}],[75,{a:\"Morning\",b:\"Both Morning and Evening\",p:\"Evening\"}],[131,{L:\"Landscape\",P:\"Portrait\",S:\"Square\"}]]);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/exifr/dist/full.esm.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/exifr/dist/full.esm.mjs":
/*!**********************************************!*\
  !*** ./node_modules/exifr/dist/full.esm.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Exifr: () => (/* binding */ te),\n/* harmony export */   Options: () => (/* binding */ q),\n/* harmony export */   allFormatters: () => (/* binding */ X),\n/* harmony export */   chunkedProps: () => (/* binding */ G),\n/* harmony export */   createDictionary: () => (/* binding */ U),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   extendDictionary: () => (/* binding */ F),\n/* harmony export */   fetchUrlAsArrayBuffer: () => (/* binding */ M),\n/* harmony export */   fileParsers: () => (/* binding */ w),\n/* harmony export */   fileReaders: () => (/* binding */ A),\n/* harmony export */   gps: () => (/* binding */ Se),\n/* harmony export */   gpsOnlyOptions: () => (/* binding */ me),\n/* harmony export */   inheritables: () => (/* binding */ K),\n/* harmony export */   orientation: () => (/* binding */ Pe),\n/* harmony export */   orientationOnlyOptions: () => (/* binding */ Ie),\n/* harmony export */   otherSegments: () => (/* binding */ V),\n/* harmony export */   parse: () => (/* binding */ ie),\n/* harmony export */   readBlobAsArrayBuffer: () => (/* binding */ R),\n/* harmony export */   rotateCanvas: () => (/* binding */ we),\n/* harmony export */   rotateCss: () => (/* binding */ Te),\n/* harmony export */   rotation: () => (/* binding */ Ae),\n/* harmony export */   rotations: () => (/* binding */ ke),\n/* harmony export */   segmentParsers: () => (/* binding */ T),\n/* harmony export */   segments: () => (/* binding */ z),\n/* harmony export */   segmentsAndBlocks: () => (/* binding */ j),\n/* harmony export */   sidecar: () => (/* binding */ st),\n/* harmony export */   tagKeys: () => (/* binding */ E),\n/* harmony export */   tagRevivers: () => (/* binding */ N),\n/* harmony export */   tagValues: () => (/* binding */ B),\n/* harmony export */   thumbnail: () => (/* binding */ ye),\n/* harmony export */   thumbnailOnlyOptions: () => (/* binding */ Ce),\n/* harmony export */   thumbnailUrl: () => (/* binding */ be),\n/* harmony export */   tiffBlocks: () => (/* binding */ H),\n/* harmony export */   tiffExtractables: () => (/* binding */ W)\n/* harmony export */ });\nvar e=\"undefined\"!=typeof self?self:global;const t=\"undefined\"!=typeof navigator,i=t&&\"undefined\"==typeof HTMLImageElement,n=!(\"undefined\"==typeof global||\"undefined\"==typeof process||!process.versions||!process.versions.node),s=e.Buffer,r=e.BigInt,a=!!s,o=e=>e;function l(e,t=o){if(n)try{return\"function\"==typeof require?Promise.resolve(t(require(e))):import(/* webpackIgnore: true */ e).then(t)}catch(t){console.warn(`Couldn't load ${e}`)}}let h=e.fetch;const u=e=>h=e;if(!e.fetch){const e=l(\"http\",(e=>e)),t=l(\"https\",(e=>e)),i=(n,{headers:s}={})=>new Promise((async(r,a)=>{let{port:o,hostname:l,pathname:h,protocol:u,search:c}=new URL(n);const f={method:\"GET\",hostname:l,path:encodeURI(h)+c,headers:s};\"\"!==o&&(f.port=Number(o));const d=(\"https:\"===u?await t:await e).request(f,(e=>{if(301===e.statusCode||302===e.statusCode){let t=new URL(e.headers.location,n).toString();return i(t,{headers:s}).then(r).catch(a)}r({status:e.statusCode,arrayBuffer:()=>new Promise((t=>{let i=[];e.on(\"data\",(e=>i.push(e))),e.on(\"end\",(()=>t(Buffer.concat(i))))}))})}));d.on(\"error\",a),d.end()}));u(i)}function c(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}const f=e=>p(e)?void 0:e,d=e=>void 0!==e;function p(e){return void 0===e||(e instanceof Map?0===e.size:0===Object.values(e).filter(d).length)}function g(e){let t=new Error(e);throw delete t.stack,t}function m(e){return\"\"===(e=function(e){for(;e.endsWith(\"\\0\");)e=e.slice(0,-1);return e}(e).trim())?void 0:e}function S(e){let t=function(e){let t=0;return e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048}(e);return e.jfif.enabled&&(t+=50),e.xmp.enabled&&(t+=2e4),e.iptc.enabled&&(t+=14e3),e.icc.enabled&&(t+=6e3),t}const C=e=>String.fromCharCode.apply(null,e),y=\"undefined\"!=typeof TextDecoder?new TextDecoder(\"utf-8\"):void 0;function b(e){return y?y.decode(e):a?Buffer.from(e).toString(\"utf8\"):decodeURIComponent(escape(C(e)))}class I{static from(e,t){return e instanceof this&&e.le===t?e:new I(e,void 0,void 0,t)}constructor(e,t=0,i,n){if(\"boolean\"==typeof n&&(this.le=n),Array.isArray(e)&&(e=new Uint8Array(e)),0===e)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){void 0===i&&(i=e.byteLength-t);let n=new DataView(e,t,i);this._swapDataView(n)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof I){void 0===i&&(i=e.byteLength-t),(t+=e.byteOffset)+i>e.byteOffset+e.byteLength&&g(\"Creating view outside of available memory in ArrayBuffer\");let n=new DataView(e.buffer,t,i);this._swapDataView(n)}else if(\"number\"==typeof e){let t=new DataView(new ArrayBuffer(e));this._swapDataView(t)}else g(\"Invalid input argument for BufferView: \"+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,i=I){return e instanceof DataView||e instanceof I?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||g(\"BufferView.set(): Invalid data argument.\"),this.toUint8().set(e,t),new i(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new I(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return b(this.getUint8Array(e,t))}getLatin1String(e=0,t=this.byteLength){let i=this.getUint8Array(e,t);return C(i)}getUnicodeString(e=0,t=this.byteLength){const i=[];for(let n=0;n<t&&e+n<this.byteLength;n+=2)i.push(this.getUint16(e+n));return C(i)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,i){switch(t){case 1:return this.getUint8(e,i);case 2:return this.getUint16(e,i);case 4:return this.getUint32(e,i);case 8:return this.getUint64&&this.getUint64(e,i)}}getUint(e,t,i){switch(t){case 8:return this.getUint8(e,i);case 16:return this.getUint16(e,i);case 32:return this.getUint32(e,i);case 64:return this.getUint64&&this.getUint64(e,i)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function P(e,t){g(`${e} '${t}' was not loaded, try using full build of exifr.`)}class k extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||P(this.kind,e),t&&(e in t||function(e,t){g(`Unknown ${e} '${t}'.`)}(this.kind,e),t[e].enabled||P(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var w=new k(\"file parser\"),T=new k(\"segment parser\"),A=new k(\"file reader\");function D(e,n){return\"string\"==typeof e?O(e,n):t&&!i&&e instanceof HTMLImageElement?O(e.src,n):e instanceof Uint8Array||e instanceof ArrayBuffer||e instanceof DataView?new I(e):t&&e instanceof Blob?x(e,n,\"blob\",R):void g(\"Invalid input argument\")}function O(e,i){return(s=e).startsWith(\"data:\")||s.length>1e4?v(e,i,\"base64\"):n&&e.includes(\"://\")?x(e,i,\"url\",M):n?v(e,i,\"fs\"):t?x(e,i,\"url\",M):void g(\"Invalid input argument\");var s}async function x(e,t,i,n){return A.has(i)?v(e,t,i):n?async function(e,t){let i=await t(e);return new I(i)}(e,n):void g(`Parser ${i} is not loaded`)}async function v(e,t,i){let n=new(A.get(i))(e,t);return await n.read(),n}const M=e=>h(e).then((e=>e.arrayBuffer())),R=e=>new Promise(((t,i)=>{let n=new FileReader;n.onloadend=()=>t(n.result||new ArrayBuffer),n.onerror=i,n.readAsArrayBuffer(e)}));class L extends Map{get tagKeys(){return this.allKeys||(this.allKeys=Array.from(this.keys())),this.allKeys}get tagValues(){return this.allValues||(this.allValues=Array.from(this.values())),this.allValues}}function U(e,t,i){let n=new L;for(let[e,t]of i)n.set(e,t);if(Array.isArray(t))for(let i of t)e.set(i,n);else e.set(t,n);return n}function F(e,t,i){let n,s=e.get(t);for(n of i)s.set(n[0],n[1])}const E=new Map,B=new Map,N=new Map,G=[\"chunked\",\"firstChunkSize\",\"firstChunkSizeNode\",\"firstChunkSizeBrowser\",\"chunkSize\",\"chunkLimit\"],V=[\"jfif\",\"xmp\",\"icc\",\"iptc\",\"ihdr\"],z=[\"tiff\",...V],H=[\"ifd0\",\"ifd1\",\"exif\",\"gps\",\"interop\"],j=[...z,...H],W=[\"makerNote\",\"userComment\"],K=[\"translateKeys\",\"translateValues\",\"reviveValues\",\"multiSegment\"],X=[...K,\"sanitize\",\"mergeOutput\",\"silentErrors\"];class _{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class Y extends _{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,i,n){if(super(),c(this,\"enabled\",!1),c(this,\"skip\",new Set),c(this,\"pick\",new Set),c(this,\"deps\",new Set),c(this,\"translateKeys\",!1),c(this,\"translateValues\",!1),c(this,\"reviveValues\",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=H.includes(e),this.canBeFiltered&&(this.dict=E.get(e)),void 0!==i)if(Array.isArray(i))this.parse=this.enabled=!0,this.canBeFiltered&&i.length>0&&this.translateTagSet(i,this.pick);else if(\"object\"==typeof i){if(this.enabled=!0,this.parse=!1!==i.parse,this.canBeFiltered){let{pick:e,skip:t}=i;e&&e.length>0&&this.translateTagSet(e,this.pick),t&&t.length>0&&this.translateTagSet(t,this.skip)}this.applyInheritables(i)}else!0===i||!1===i?this.parse=this.enabled=i:g(`Invalid options argument: ${i}`)}applyInheritables(e){let t,i;for(t of K)i=e[t],void 0!==i&&(this[t]=i)}translateTagSet(e,t){if(this.dict){let i,n,{tagKeys:s,tagValues:r}=this.dict;for(i of e)\"string\"==typeof i?(n=r.indexOf(i),-1===n&&(n=s.indexOf(Number(i))),-1!==n&&t.add(Number(s[n]))):t.add(i)}else for(let i of e)t.add(i)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,ee(this.pick,this.deps)):this.enabled&&this.pick.size>0&&ee(this.pick,this.deps)}}var $={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},J=new Map;class q extends _{static useCached(e){let t=J.get(e);return void 0!==t||(t=new this(e),J.set(e,t)),t}constructor(e){super(),!0===e?this.setupFromTrue():void 0===e?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):\"object\"==typeof e?this.setupFromObject(e):g(`Invalid options argument ${e}`),void 0===this.firstChunkSize&&(this.firstChunkSize=t?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=$[e];for(e of j)this[e]=new Y(e,$[e],void 0,this)}setupFromTrue(){let e;for(e of G)this[e]=$[e];for(e of X)this[e]=$[e];for(e of W)this[e]=!0;for(e of j)this[e]=new Y(e,!0,void 0,this)}setupFromArray(e){let t;for(t of G)this[t]=$[t];for(t of X)this[t]=$[t];for(t of W)this[t]=$[t];for(t of j)this[t]=new Y(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,H)}setupFromObject(e){let t;for(t of(H.ifd0=H.ifd0||H.image,H.ifd1=H.ifd1||H.thumbnail,Object.assign(this,e),G))this[t]=Z(e[t],$[t]);for(t of X)this[t]=Z(e[t],$[t]);for(t of W)this[t]=Z(e[t],$[t]);for(t of z)this[t]=new Y(t,$[t],e[t],this);for(t of H)this[t]=new Y(t,$[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,H,j),!0===e.tiff?this.batchEnableWithBool(H,!0):!1===e.tiff?this.batchEnableWithUserValue(H,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,H):\"object\"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,H)}batchEnableWithBool(e,t){for(let i of e)this[i].enabled=t}batchEnableWithUserValue(e,t){for(let i of e){let e=t[i];this[i].enabled=!1!==e&&void 0!==e}}setupGlobalFilters(e,t,i,n=i){if(e&&e.length){for(let e of n)this[e].enabled=!1;let t=Q(e,i);for(let[e,i]of t)ee(this[e].pick,i),this[e].enabled=!0}else if(t&&t.length){let e=Q(t,i);for(let[t,i]of e)ee(this[t].skip,i)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:i,iptc:n,icc:s}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),i.enabled||e.skip.add(700),n.enabled||e.skip.add(33723),s.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:i,interop:n}=this;n.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),i.needed&&e.deps.add(34853),this.tiff.enabled=H.some((e=>!0===this[e].enabled))||this.makerNote||this.userComment;for(let e of H)this[e].finalizeFilters()}get onlyTiff(){return!V.map((e=>this[e].enabled)).some((e=>!0===e))&&this.tiff.enabled}checkLoadedPlugins(){for(let e of z)this[e].enabled&&!T.has(e)&&P(\"segment parser\",e)}}function Q(e,t){let i,n,s,r,a=[];for(s of t){for(r of(i=E.get(s),n=[],i))(e.includes(r[0])||e.includes(r[1]))&&n.push(r[0]);n.length&&a.push([s,n])}return a}function Z(e,t){return void 0!==e?e:void 0!==t?t:void 0}function ee(e,t){for(let i of t)e.add(i)}c(q,\"default\",$);class te{constructor(e){c(this,\"parsers\",{}),c(this,\"output\",{}),c(this,\"errors\",[]),c(this,\"pushToErrors\",(e=>this.errors.push(e))),this.options=q.useCached(e)}async read(e){this.file=await D(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[i,n]of w)if(n.canHandle(e,t))return this.fileParser=new n(this.options,this.file,this.parsers),e[i]=!0;this.file.close&&this.file.close(),g(\"Unknown file format\")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),f(e)}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map((async t=>{let i=await t.parse();t.assignToOutput(e,i)}));this.options.silentErrors&&(t=t.map((e=>e.catch(this.pushToErrors)))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,i=T.get(\"tiff\",e);var n;if(t.tiff?n={start:0,type:\"tiff\"}:t.jpeg&&(n=await this.fileParser.getOrFindSegment(\"tiff\")),void 0===n)return;let s=await this.fileParser.ensureSegmentChunk(n),r=this.parsers.tiff=new i(s,e,t),a=await r.extractThumbnail();return t.close&&t.close(),a}}async function ie(e,t){let i=new te(t);return await i.read(e),i.parse()}var ne=Object.freeze({__proto__:null,parse:ie,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q});class se{constructor(e,t,i){c(this,\"errors\",[]),c(this,\"ensureSegmentChunk\",(async e=>{let t=e.start,i=e.size||65536;if(this.file.chunked)if(this.file.available(t,i))e.chunk=this.file.subarray(t,i);else try{e.chunk=await this.file.readChunk(t,i)}catch(t){g(`Couldn't read segment: ${JSON.stringify(e)}. ${t.message}`)}else this.file.byteLength>t+i?e.chunk=this.file.subarray(t,i):void 0===e.size?e.chunk=this.file.subarray(t):g(\"Segment unreachable: \"+JSON.stringify(e));return e.chunk})),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=i}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let i=new(T.get(e))(t,this.options,this.file);return this.parsers[e]=i}createParsers(e){for(let t of e){let{type:e,chunk:i}=t,n=this.options[e];if(n&&n.enabled){let t=this.parsers[e];t&&t.append||t||this.createParser(e,i)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}class re{static findPosition(e,t){let i=e.getUint16(t+2)+2,n=\"function\"==typeof this.headerLength?this.headerLength(e,t,i):this.headerLength,s=t+n,r=i-n;return{offset:t,length:i,headerLength:n,start:s,size:r,end:s+r}}static parse(e,t={}){return new this(e,new q({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof I?e:new I(e)}constructor(e,t={},i){c(this,\"errors\",[]),c(this,\"raw\",new Map),c(this,\"handleError\",(e=>{if(!this.options.silentErrors)throw e;this.errors.push(e.message)})),this.chunk=this.normalizeInput(e),this.file=i,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let i=N.get(t),n=B.get(t),s=E.get(t),r=this.options[t],a=r.reviveValues&&!!i,o=r.translateValues&&!!n,l=r.translateKeys&&!!s,h={};for(let[t,r]of e)a&&i.has(t)?r=i.get(t)(r):o&&n.has(t)&&(r=this.translateValue(r,n.get(t))),l&&s.has(t)&&(t=s.get(t)||t),h[t]=r;return h}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,i){if(this.globalOptions.mergeOutput)return Object.assign(e,i);e[t]?Object.assign(e[t],i):e[t]=i}}c(re,\"headerLength\",4),c(re,\"type\",void 0),c(re,\"multiSegment\",!1),c(re,\"canHandle\",(()=>!1));function ae(e){return 192===e||194===e||196===e||219===e||221===e||218===e||254===e}function oe(e){return e>=224&&e<=239}function le(e,t,i){for(let[n,s]of T)if(s.canHandle(e,t,i))return n}class he extends se{constructor(...e){super(...e),c(this,\"appSegments\",[]),c(this,\"jpegSegments\",[]),c(this,\"unknownSegments\",[])}static canHandle(e,t){return 65496===t}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){!0===e?(this.findAll=!0,this.wanted=new Set(T.keyList())):(e=void 0===e?T.keyList().filter((e=>this.options[e].enabled)):e.filter((e=>this.options[e].enabled&&T.has(e))),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:i,findAll:n,wanted:s,remaining:r}=this;if(!n&&this.file.chunked&&(n=Array.from(s).some((e=>{let t=T.get(e),i=this.options[e];return t.multiSegment&&i.multiSegment})),n&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,i.byteLength),!this.options.onlyTiff&&i.chunked){let t=!1;for(;r.size>0&&!t&&(i.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:n}=i,s=this.appSegments.some((e=>!this.file.available(e.offset||e.start,e.length||e.size)));if(t=e>n&&!s?!await i.readNextChunk(e):!await i.readNextChunk(n),void 0===(e=this.findAppSegmentsInRange(e,i.byteLength)))return}}}findAppSegmentsInRange(e,t){t-=2;let i,n,s,r,a,o,{file:l,findAll:h,wanted:u,remaining:c,options:f}=this;for(;e<t;e++)if(255===l.getUint8(e))if(i=l.getUint8(e+1),oe(i)){if(n=l.getUint16(e+2),s=le(l,e,n),s&&u.has(s)&&(r=T.get(s),a=r.findPosition(l,e),o=f[s],a.type=s,this.appSegments.push(a),!h&&(r.multiSegment&&o.multiSegment?(this.unfinishedMultiSegment=a.chunkNumber<a.chunkCount,this.unfinishedMultiSegment||c.delete(s)):c.delete(s),0===c.size)))break;f.recordUnknownSegments&&(a=re.findPosition(l,e),a.marker=i,this.unknownSegments.push(a)),e+=n+1}else if(ae(i)){if(n=l.getUint16(e+2),218===i&&!1!==f.stopAfterSos)return;f.recordJpegSegments&&this.jpegSegments.push({offset:e,length:n,marker:i}),e+=n+1}return e}mergeMultiSegments(){if(!this.appSegments.some((e=>e.multiSegment)))return;let e=function(e,t){let i,n,s,r=new Map;for(let a=0;a<e.length;a++)i=e[a],n=i[t],r.has(n)?s=r.get(n):r.set(n,s=[]),s.push(i);return Array.from(r)}(this.appSegments,\"type\");this.mergedAppSegments=e.map((([e,t])=>{let i=T.get(e,this.options);if(i.handleMultiSegments){return{type:e,chunk:i.handleMultiSegments(t)}}return t[0]}))}getSegment(e){return this.appSegments.find((t=>t.type===e))}async getOrFindSegment(e){let t=this.getSegment(e);return void 0===t&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}c(he,\"type\",\"jpeg\"),w.set(\"jpeg\",he);const ue=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class ce extends re{parseHeader(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,i=new Map){let{pick:n,skip:s}=this.options[t];n=new Set(n);let r=n.size>0,a=0===s.size,o=this.chunk.getUint16(e);e+=2;for(let l=0;l<o;l++){let o=this.chunk.getUint16(e);if(r){if(n.has(o)&&(i.set(o,this.parseTag(e,o,t)),n.delete(o),0===n.size))break}else!a&&s.has(o)||i.set(o,this.parseTag(e,o,t));e+=12}return i}parseTag(e,t,i){let{chunk:n}=this,s=n.getUint16(e+2),r=n.getUint32(e+4),a=ue[s];if(a*r<=4?e+=8:e=n.getUint32(e+8),(s<1||s>13)&&g(`Invalid TIFF value type. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e}`),e>n.byteLength&&g(`Invalid TIFF value offset. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e} is outside of chunk size ${n.byteLength}`),1===s)return n.getUint8Array(e,r);if(2===s)return m(n.getString(e,r));if(7===s)return n.getUint8Array(e,r);if(1===r)return this.parseTagValue(s,e);{let t=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(s))(r),i=a;for(let n=0;n<r;n++)t[n]=this.parseTagValue(s,e),e+=i;return t}}parseTagValue(e,t){let{chunk:i}=this;switch(e){case 1:return i.getUint8(t);case 3:return i.getUint16(t);case 4:return i.getUint32(t);case 5:return i.getUint32(t)/i.getUint32(t+4);case 6:return i.getInt8(t);case 8:return i.getInt16(t);case 9:return i.getInt32(t);case 10:return i.getInt32(t)/i.getInt32(t+4);case 11:return i.getFloat(t);case 12:return i.getDouble(t);case 13:return i.getUint32(t);default:g(`Invalid tiff type ${e}`)}}}class fe extends ce{static canHandle(e,t){return 225===e.getUint8(t+1)&&1165519206===e.getUint32(t+4)&&0===e.getUint16(t+8)}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse(\"parseExifBlock\"),e.gps.enabled&&await this.safeParse(\"parseGpsBlock\"),e.interop.enabled&&await this.safeParse(\"parseInteropBlock\"),e.ifd1.enabled&&await this.safeParse(\"parseThumbnailBlock\"),this.createOutput()}safeParse(e){let t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}findIfd0Offset(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(void 0===this.ifd1Offset){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let i=new Map;return this[t]=i,this.parseTags(e,t,i),i}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&g(\"Malformed EXIF data\"),!e.chunked&&this.ifd0Offset>e.byteLength&&g(`IFD0 offset points to outside of file.\\nthis.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,S(this.options));let t=this.parseBlock(this.ifd0Offset,\"ifd0\");return 0!==t.size?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.exifOffset)return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,S(this.options));let e=this.parseBlock(this.exifOffset,\"exif\");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let i=e.get(t);i&&1===i.length&&e.set(t,i[0])}async parseGpsBlock(){if(this.gps)return;if(this.ifd0||await this.parseIfd0Block(),void 0===this.gpsOffset)return;let e=this.parseBlock(this.gpsOffset,\"gps\");return e&&e.has(2)&&e.has(4)&&(e.set(\"latitude\",de(...e.get(2),e.get(1))),e.set(\"longitude\",de(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),void 0!==this.interopOffset||this.exif||await this.parseExifBlock(),void 0!==this.interopOffset))return this.parseBlock(this.interopOffset,\"interop\")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,\"ifd1\"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),void 0===this.ifd1)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,i,n={};for(t of H)if(e=this[t],!p(e))if(i=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if(\"ifd1\"===t)continue;Object.assign(n,i)}else n[t]=i;return this.makerNote&&(n.makerNote=this.makerNote),this.userComment&&(n.userComment=this.userComment),n}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[i,n]of Object.entries(t))this.assignObjectToOutput(e,i,n)}}function de(e,t,i,n){var s=e+t/60+i/3600;return\"S\"!==n&&\"W\"!==n||(s*=-1),s}c(fe,\"type\",\"tiff\"),c(fe,\"headerLength\",10),T.set(\"tiff\",fe);var pe=Object.freeze({__proto__:null,default:ne,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie});const ge={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1},me=Object.assign({},ge,{firstChunkSize:4e4,gps:[1,2,3,4]});async function Se(e){let t=new te(me);await t.read(e);let i=await t.parse();if(i&&i.gps){let{latitude:e,longitude:t}=i.gps;return{latitude:e,longitude:t}}}const Ce=Object.assign({},ge,{tiff:!1,ifd1:!0,mergeOutput:!1});async function ye(e){let t=new te(Ce);await t.read(e);let i=await t.extractThumbnail();return i&&a?s.from(i):i}async function be(e){let t=await this.thumbnail(e);if(void 0!==t){let e=new Blob([t]);return URL.createObjectURL(e)}}const Ie=Object.assign({},ge,{firstChunkSize:4e4,ifd0:[274]});async function Pe(e){let t=new te(Ie);await t.read(e);let i=await t.parse();if(i&&i.ifd0)return i.ifd0[274]}const ke=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let we=!0,Te=!0;if(\"object\"==typeof navigator){let e=navigator.userAgent;if(e.includes(\"iPad\")||e.includes(\"iPhone\")){let t=e.match(/OS (\\d+)_(\\d+)/);if(t){let[,e,i]=t,n=Number(e)+.1*Number(i);we=n<13.4,Te=!1}}else if(e.includes(\"OS X 10\")){let[,t]=e.match(/OS X 10[_.](\\d+)/);we=Te=Number(t)<15}if(e.includes(\"Chrome/\")){let[,t]=e.match(/Chrome\\/(\\d+)/);we=Te=Number(t)<81}else if(e.includes(\"Firefox/\")){let[,t]=e.match(/Firefox\\/(\\d+)/);we=Te=Number(t)<77}}async function Ae(e){let t=await Pe(e);return Object.assign({canvas:we,css:Te},ke[t])}class De extends I{constructor(...e){super(...e),c(this,\"ranges\",new Oe),0!==this.byteLength&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,i){if(0===e&&0===this.byteLength&&i){let e=new DataView(i.buffer||i,i.byteOffset,i.byteLength);this._swapDataView(e)}else{let i=e+t;if(i>this.byteLength){let{dataView:e}=this._extend(i);this._swapDataView(e)}}}_extend(e){let t;t=a?s.allocUnsafe(e):new Uint8Array(e);let i=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:i}}subarray(e,t,i=!1){return t=t||this._lengthToEnd(e),i&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,i=!1){i&&this._tryExtend(t,e.byteLength,e);let n=super.set(e,t);return this.ranges.add(t,n.byteLength),n}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class Oe{constructor(){c(this,\"list\",[])}get length(){return this.list.length}add(e,t,i=0){let n=e+t,s=this.list.filter((t=>xe(e,t.offset,n)||xe(e,t.end,n)));if(s.length>0){e=Math.min(e,...s.map((e=>e.offset))),n=Math.max(n,...s.map((e=>e.end))),t=n-e;let i=s.shift();i.offset=e,i.length=t,i.end=n,this.list=this.list.filter((e=>!s.includes(e)))}else this.list.push({offset:e,length:t,end:n})}available(e,t){let i=e+t;return this.list.some((t=>t.offset<=e&&i<=t.end))}}function xe(e,t,i){return e<=t&&t<=i}class ve extends De{constructor(e,t){super(0),c(this,\"chunksRead\",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,i=await this.readChunk(e,t);return!!i&&i.byteLength===t}async readChunk(e,t){if(this.chunksRead++,0!==(t=this.safeWrapAddress(e,t)))return this._readChunk(e,t)}safeWrapAddress(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return void 0!==this.size&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}A.set(\"blob\",class extends ve{async readWhole(){this.chunked=!1;let e=await R(this.input);this._swapArrayBuffer(e)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(e,t){let i=t?e+t:void 0,n=this.input.slice(e,i),s=await R(n);return this.set(s,e,!0)}});var Me=Object.freeze({__proto__:null,default:pe,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});A.set(\"url\",class extends ve{async readWhole(){this.chunked=!1;let e=await M(this.input);e instanceof ArrayBuffer?this._swapArrayBuffer(e):e instanceof Uint8Array&&this._swapBuffer(e)}async _readChunk(e,t){let i=t?e+t-1:void 0,n=this.options.httpHeaders||{};(e||i)&&(n.range=`bytes=${[e,i].join(\"-\")}`);let s=await h(this.input,{headers:n}),r=await s.arrayBuffer(),a=r.byteLength;if(416!==s.status)return a!==t&&(this.size=e+a),this.set(r,e,!0)}});I.prototype.getUint64=function(e){let t=this.getUint32(e),i=this.getUint32(e+4);return t<1048575?t<<32|i:void 0!==typeof r?(console.warn(\"Using BigInt because of type 64uint but JS can only handle 53b numbers.\"),r(t)<<r(32)|r(i)):void g(\"Trying to read 64b value but JS can only handle 53b numbers.\")};class Re extends se{parseBoxes(e=0){let t=[];for(;e<this.file.byteLength-4;){let i=this.parseBoxHead(e);if(t.push(i),0===i.length)break;e+=i.length}return t}parseSubBoxes(e){e.boxes=this.parseBoxes(e.start)}findBox(e,t){return void 0===e.boxes&&this.parseSubBoxes(e),e.boxes.find((e=>e.kind===t))}parseBoxHead(e){let t=this.file.getUint32(e),i=this.file.getString(e+4,4),n=e+8;return 1===t&&(t=this.file.getUint64(e+8),n+=8),{offset:e,length:t,kind:i,start:n}}parseBoxFullHead(e){if(void 0!==e.version)return;let t=this.file.getUint32(e.start);e.version=t>>24,e.start+=4}}class Le extends Re{static canHandle(e,t){if(0!==t)return!1;let i=e.getUint16(2);if(i>50)return!1;let n=16,s=[];for(;n<i;)s.push(e.getString(n,4)),n+=4;return s.includes(this.type)}async parse(){let e=this.file.getUint32(0),t=this.parseBoxHead(e);for(;\"meta\"!==t.kind;)e+=t.length,await this.file.ensureChunk(e,16),t=this.parseBoxHead(e);await this.file.ensureChunk(t.offset,t.length),this.parseBoxFullHead(t),this.parseSubBoxes(t),this.options.icc.enabled&&await this.findIcc(t),this.options.tiff.enabled&&await this.findExif(t)}async registerSegment(e,t,i){await this.file.ensureChunk(t,i);let n=this.file.subarray(t,i);this.createParser(e,n)}async findIcc(e){let t=this.findBox(e,\"iprp\");if(void 0===t)return;let i=this.findBox(t,\"ipco\");if(void 0===i)return;let n=this.findBox(i,\"colr\");void 0!==n&&await this.registerSegment(\"icc\",n.offset+12,n.length)}async findExif(e){let t=this.findBox(e,\"iinf\");if(void 0===t)return;let i=this.findBox(e,\"iloc\");if(void 0===i)return;let n=this.findExifLocIdInIinf(t),s=this.findExtentInIloc(i,n);if(void 0===s)return;let[r,a]=s;await this.file.ensureChunk(r,a);let o=4+this.file.getUint32(r);r+=o,a-=o,await this.registerSegment(\"tiff\",r,a)}findExifLocIdInIinf(e){this.parseBoxFullHead(e);let t,i,n,s,r=e.start,a=this.file.getUint16(r);for(r+=2;a--;){if(t=this.parseBoxHead(r),this.parseBoxFullHead(t),i=t.start,t.version>=2&&(n=3===t.version?4:2,s=this.file.getString(i+n+2,4),\"Exif\"===s))return this.file.getUintBytes(i,n);r+=t.length}}get8bits(e){let t=this.file.getUint8(e);return[t>>4,15&t]}findExtentInIloc(e,t){this.parseBoxFullHead(e);let i=e.start,[n,s]=this.get8bits(i++),[r,a]=this.get8bits(i++),o=2===e.version?4:2,l=1===e.version||2===e.version?2:0,h=a+n+s,u=2===e.version?4:2,c=this.file.getUintBytes(i,u);for(i+=u;c--;){let e=this.file.getUintBytes(i,o);i+=o+l+2+r;let u=this.file.getUint16(i);if(i+=2,e===t)return u>1&&console.warn(\"ILOC box has more than one extent but we're only processing one\\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file\"),[this.file.getUintBytes(i+a,n),this.file.getUintBytes(i+a+n,s)];i+=u*h}}}class Ue extends Le{}c(Ue,\"type\",\"heic\");class Fe extends Le{}c(Fe,\"type\",\"avif\"),w.set(\"heic\",Ue),w.set(\"avif\",Fe),U(E,[\"ifd0\",\"ifd1\"],[[256,\"ImageWidth\"],[257,\"ImageHeight\"],[258,\"BitsPerSample\"],[259,\"Compression\"],[262,\"PhotometricInterpretation\"],[270,\"ImageDescription\"],[271,\"Make\"],[272,\"Model\"],[273,\"StripOffsets\"],[274,\"Orientation\"],[277,\"SamplesPerPixel\"],[278,\"RowsPerStrip\"],[279,\"StripByteCounts\"],[282,\"XResolution\"],[283,\"YResolution\"],[284,\"PlanarConfiguration\"],[296,\"ResolutionUnit\"],[301,\"TransferFunction\"],[305,\"Software\"],[306,\"ModifyDate\"],[315,\"Artist\"],[316,\"HostComputer\"],[317,\"Predictor\"],[318,\"WhitePoint\"],[319,\"PrimaryChromaticities\"],[513,\"ThumbnailOffset\"],[514,\"ThumbnailLength\"],[529,\"YCbCrCoefficients\"],[530,\"YCbCrSubSampling\"],[531,\"YCbCrPositioning\"],[532,\"ReferenceBlackWhite\"],[700,\"ApplicationNotes\"],[33432,\"Copyright\"],[33723,\"IPTC\"],[34665,\"ExifIFD\"],[34675,\"ICC\"],[34853,\"GpsIFD\"],[330,\"SubIFD\"],[40965,\"InteropIFD\"],[40091,\"XPTitle\"],[40092,\"XPComment\"],[40093,\"XPAuthor\"],[40094,\"XPKeywords\"],[40095,\"XPSubject\"]]),U(E,\"exif\",[[33434,\"ExposureTime\"],[33437,\"FNumber\"],[34850,\"ExposureProgram\"],[34852,\"SpectralSensitivity\"],[34855,\"ISO\"],[34858,\"TimeZoneOffset\"],[34859,\"SelfTimerMode\"],[34864,\"SensitivityType\"],[34865,\"StandardOutputSensitivity\"],[34866,\"RecommendedExposureIndex\"],[34867,\"ISOSpeed\"],[34868,\"ISOSpeedLatitudeyyy\"],[34869,\"ISOSpeedLatitudezzz\"],[36864,\"ExifVersion\"],[36867,\"DateTimeOriginal\"],[36868,\"CreateDate\"],[36873,\"GooglePlusUploadCode\"],[36880,\"OffsetTime\"],[36881,\"OffsetTimeOriginal\"],[36882,\"OffsetTimeDigitized\"],[37121,\"ComponentsConfiguration\"],[37122,\"CompressedBitsPerPixel\"],[37377,\"ShutterSpeedValue\"],[37378,\"ApertureValue\"],[37379,\"BrightnessValue\"],[37380,\"ExposureCompensation\"],[37381,\"MaxApertureValue\"],[37382,\"SubjectDistance\"],[37383,\"MeteringMode\"],[37384,\"LightSource\"],[37385,\"Flash\"],[37386,\"FocalLength\"],[37393,\"ImageNumber\"],[37394,\"SecurityClassification\"],[37395,\"ImageHistory\"],[37396,\"SubjectArea\"],[37500,\"MakerNote\"],[37510,\"UserComment\"],[37520,\"SubSecTime\"],[37521,\"SubSecTimeOriginal\"],[37522,\"SubSecTimeDigitized\"],[37888,\"AmbientTemperature\"],[37889,\"Humidity\"],[37890,\"Pressure\"],[37891,\"WaterDepth\"],[37892,\"Acceleration\"],[37893,\"CameraElevationAngle\"],[40960,\"FlashpixVersion\"],[40961,\"ColorSpace\"],[40962,\"ExifImageWidth\"],[40963,\"ExifImageHeight\"],[40964,\"RelatedSoundFile\"],[41483,\"FlashEnergy\"],[41486,\"FocalPlaneXResolution\"],[41487,\"FocalPlaneYResolution\"],[41488,\"FocalPlaneResolutionUnit\"],[41492,\"SubjectLocation\"],[41493,\"ExposureIndex\"],[41495,\"SensingMethod\"],[41728,\"FileSource\"],[41729,\"SceneType\"],[41730,\"CFAPattern\"],[41985,\"CustomRendered\"],[41986,\"ExposureMode\"],[41987,\"WhiteBalance\"],[41988,\"DigitalZoomRatio\"],[41989,\"FocalLengthIn35mmFormat\"],[41990,\"SceneCaptureType\"],[41991,\"GainControl\"],[41992,\"Contrast\"],[41993,\"Saturation\"],[41994,\"Sharpness\"],[41996,\"SubjectDistanceRange\"],[42016,\"ImageUniqueID\"],[42032,\"OwnerName\"],[42033,\"SerialNumber\"],[42034,\"LensInfo\"],[42035,\"LensMake\"],[42036,\"LensModel\"],[42037,\"LensSerialNumber\"],[42080,\"CompositeImage\"],[42081,\"CompositeImageCount\"],[42082,\"CompositeImageExposureTimes\"],[42240,\"Gamma\"],[59932,\"Padding\"],[59933,\"OffsetSchema\"],[65e3,\"OwnerName\"],[65001,\"SerialNumber\"],[65002,\"Lens\"],[65100,\"RawFile\"],[65101,\"Converter\"],[65102,\"WhiteBalance\"],[65105,\"Exposure\"],[65106,\"Shadows\"],[65107,\"Brightness\"],[65108,\"Contrast\"],[65109,\"Saturation\"],[65110,\"Sharpness\"],[65111,\"Smoothness\"],[65112,\"MoireFilter\"],[40965,\"InteropIFD\"]]),U(E,\"gps\",[[0,\"GPSVersionID\"],[1,\"GPSLatitudeRef\"],[2,\"GPSLatitude\"],[3,\"GPSLongitudeRef\"],[4,\"GPSLongitude\"],[5,\"GPSAltitudeRef\"],[6,\"GPSAltitude\"],[7,\"GPSTimeStamp\"],[8,\"GPSSatellites\"],[9,\"GPSStatus\"],[10,\"GPSMeasureMode\"],[11,\"GPSDOP\"],[12,\"GPSSpeedRef\"],[13,\"GPSSpeed\"],[14,\"GPSTrackRef\"],[15,\"GPSTrack\"],[16,\"GPSImgDirectionRef\"],[17,\"GPSImgDirection\"],[18,\"GPSMapDatum\"],[19,\"GPSDestLatitudeRef\"],[20,\"GPSDestLatitude\"],[21,\"GPSDestLongitudeRef\"],[22,\"GPSDestLongitude\"],[23,\"GPSDestBearingRef\"],[24,\"GPSDestBearing\"],[25,\"GPSDestDistanceRef\"],[26,\"GPSDestDistance\"],[27,\"GPSProcessingMethod\"],[28,\"GPSAreaInformation\"],[29,\"GPSDateStamp\"],[30,\"GPSDifferential\"],[31,\"GPSHPositioningError\"]]),U(B,[\"ifd0\",\"ifd1\"],[[274,{1:\"Horizontal (normal)\",2:\"Mirror horizontal\",3:\"Rotate 180\",4:\"Mirror vertical\",5:\"Mirror horizontal and rotate 270 CW\",6:\"Rotate 90 CW\",7:\"Mirror horizontal and rotate 90 CW\",8:\"Rotate 270 CW\"}],[296,{1:\"None\",2:\"inches\",3:\"cm\"}]]);let Ee=U(B,\"exif\",[[34850,{0:\"Not defined\",1:\"Manual\",2:\"Normal program\",3:\"Aperture priority\",4:\"Shutter priority\",5:\"Creative program\",6:\"Action program\",7:\"Portrait mode\",8:\"Landscape mode\"}],[37121,{0:\"-\",1:\"Y\",2:\"Cb\",3:\"Cr\",4:\"R\",5:\"G\",6:\"B\"}],[37383,{0:\"Unknown\",1:\"Average\",2:\"CenterWeightedAverage\",3:\"Spot\",4:\"MultiSpot\",5:\"Pattern\",6:\"Partial\",255:\"Other\"}],[37384,{0:\"Unknown\",1:\"Daylight\",2:\"Fluorescent\",3:\"Tungsten (incandescent light)\",4:\"Flash\",9:\"Fine weather\",10:\"Cloudy weather\",11:\"Shade\",12:\"Daylight fluorescent (D 5700 - 7100K)\",13:\"Day white fluorescent (N 4600 - 5400K)\",14:\"Cool white fluorescent (W 3900 - 4500K)\",15:\"White fluorescent (WW 3200 - 3700K)\",17:\"Standard light A\",18:\"Standard light B\",19:\"Standard light C\",20:\"D55\",21:\"D65\",22:\"D75\",23:\"D50\",24:\"ISO studio tungsten\",255:\"Other\"}],[37385,{0:\"Flash did not fire\",1:\"Flash fired\",5:\"Strobe return light not detected\",7:\"Strobe return light detected\",9:\"Flash fired, compulsory flash mode\",13:\"Flash fired, compulsory flash mode, return light not detected\",15:\"Flash fired, compulsory flash mode, return light detected\",16:\"Flash did not fire, compulsory flash mode\",24:\"Flash did not fire, auto mode\",25:\"Flash fired, auto mode\",29:\"Flash fired, auto mode, return light not detected\",31:\"Flash fired, auto mode, return light detected\",32:\"No flash function\",65:\"Flash fired, red-eye reduction mode\",69:\"Flash fired, red-eye reduction mode, return light not detected\",71:\"Flash fired, red-eye reduction mode, return light detected\",73:\"Flash fired, compulsory flash mode, red-eye reduction mode\",77:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected\",79:\"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected\",89:\"Flash fired, auto mode, red-eye reduction mode\",93:\"Flash fired, auto mode, return light not detected, red-eye reduction mode\",95:\"Flash fired, auto mode, return light detected, red-eye reduction mode\"}],[41495,{1:\"Not defined\",2:\"One-chip color area sensor\",3:\"Two-chip color area sensor\",4:\"Three-chip color area sensor\",5:\"Color sequential area sensor\",7:\"Trilinear sensor\",8:\"Color sequential linear sensor\"}],[41728,{1:\"Film Scanner\",2:\"Reflection Print Scanner\",3:\"Digital Camera\"}],[41729,{1:\"Directly photographed\"}],[41985,{0:\"Normal\",1:\"Custom\",2:\"HDR (no original saved)\",3:\"HDR (original saved)\",4:\"Original (for HDR)\",6:\"Panorama\",7:\"Portrait HDR\",8:\"Portrait\"}],[41986,{0:\"Auto\",1:\"Manual\",2:\"Auto bracket\"}],[41987,{0:\"Auto\",1:\"Manual\"}],[41990,{0:\"Standard\",1:\"Landscape\",2:\"Portrait\",3:\"Night\",4:\"Other\"}],[41991,{0:\"None\",1:\"Low gain up\",2:\"High gain up\",3:\"Low gain down\",4:\"High gain down\"}],[41996,{0:\"Unknown\",1:\"Macro\",2:\"Close\",3:\"Distant\"}],[42080,{0:\"Unknown\",1:\"Not a Composite Image\",2:\"General Composite Image\",3:\"Composite Image Captured While Shooting\"}]]);const Be={1:\"No absolute unit of measurement\",2:\"Inch\",3:\"Centimeter\"};Ee.set(37392,Be),Ee.set(41488,Be);const Ne={0:\"Normal\",1:\"Low\",2:\"High\"};function Ge(e){return\"object\"==typeof e&&void 0!==e.length?e[0]:e}function Ve(e){let t=Array.from(e).slice(1);return t[1]>15&&(t=t.map((e=>String.fromCharCode(e)))),\"0\"!==t[2]&&0!==t[2]||t.pop(),t.join(\".\")}function ze(e){if(\"string\"==typeof e){var[t,i,n,s,r,a]=e.trim().split(/[-: ]/g).map(Number),o=new Date(t,i-1,n);return Number.isNaN(s)||Number.isNaN(r)||Number.isNaN(a)||(o.setHours(s),o.setMinutes(r),o.setSeconds(a)),Number.isNaN(+o)?e:o}}function He(e){if(\"string\"==typeof e)return e;let t=[];if(0===e[1]&&0===e[e.length-1])for(let i=0;i<e.length;i+=2)t.push(je(e[i+1],e[i]));else for(let i=0;i<e.length;i+=2)t.push(je(e[i],e[i+1]));return m(String.fromCodePoint(...t))}function je(e,t){return e<<8|t}Ee.set(41992,Ne),Ee.set(41993,Ne),Ee.set(41994,Ne),U(N,[\"ifd0\",\"ifd1\"],[[50827,function(e){return\"string\"!=typeof e?b(e):e}],[306,ze],[40091,He],[40092,He],[40093,He],[40094,He],[40095,He]]),U(N,\"exif\",[[40960,Ve],[36864,Ve],[36867,ze],[36868,ze],[40962,Ge],[40963,Ge]]),U(N,\"gps\",[[0,e=>Array.from(e).join(\".\")],[7,e=>Array.from(e).join(\":\")]]);class We extends re{static canHandle(e,t){return 225===e.getUint8(t+1)&&1752462448===e.getUint32(t+4)&&\"http://ns.adobe.com/\"===e.getString(t+4,\"http://ns.adobe.com/\".length)}static headerLength(e,t){return\"http://ns.adobe.com/xmp/extension/\"===e.getString(t+4,\"http://ns.adobe.com/xmp/extension/\".length)?79:4+\"http://ns.adobe.com/xap/1.0/\".length+1}static findPosition(e,t){let i=super.findPosition(e,t);return i.multiSegment=i.extended=79===i.headerLength,i.multiSegment?(i.chunkCount=e.getUint8(t+72),i.chunkNumber=e.getUint8(t+76),0!==e.getUint8(t+77)&&i.chunkNumber++):(i.chunkCount=1/0,i.chunkNumber=-1),i}static handleMultiSegments(e){return e.map((e=>e.chunk.getString())).join(\"\")}normalizeInput(e){return\"string\"==typeof e?e:I.from(e).getString()}parse(e=this.chunk){if(!this.localOptions.parse)return e;e=function(e){let t={},i={};for(let e of Ze)t[e]=[],i[e]=0;return e.replace(et,((e,n,s)=>{if(\"<\"===n){let n=++i[s];return t[s].push(n),`${e}#${n}`}return`${e}#${t[s].pop()}`}))}(e);let t=Xe.findAll(e,\"rdf\",\"Description\");0===t.length&&t.push(new Xe(\"rdf\",\"Description\",void 0,e));let i,n={};for(let e of t)for(let t of e.properties)i=Je(t.ns,n),_e(t,i);return function(e){let t;for(let i in e)t=e[i]=f(e[i]),void 0===t&&delete e[i];return f(e)}(n)}assignToOutput(e,t){if(this.localOptions.parse)for(let[i,n]of Object.entries(t))switch(i){case\"tiff\":this.assignObjectToOutput(e,\"ifd0\",n);break;case\"exif\":this.assignObjectToOutput(e,\"exif\",n);break;case\"xmlns\":break;default:this.assignObjectToOutput(e,i,n)}else e.xmp=t}}c(We,\"type\",\"xmp\"),c(We,\"multiSegment\",!0),T.set(\"xmp\",We);class Ke{static findAll(e){return qe(e,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=(\"[^\"]*\"|'[^']*')/gm).map(Ke.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[3].slice(1,-1);return n=Qe(n),new Ke(t,i,n)}constructor(e,t,i){this.ns=e,this.name=t,this.value=i}serialize(){return this.value}}class Xe{static findAll(e,t,i){if(void 0!==t||void 0!==i){t=t||\"[\\\\w\\\\d-]+\",i=i||\"[\\\\w\\\\d-]+\";var n=new RegExp(`<(${t}):(${i})(#\\\\d+)?((\\\\s+?[\\\\w\\\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\\\s*)(\\\\/>|>([\\\\s\\\\S]*?)<\\\\/\\\\1:\\\\2\\\\3>)`,\"gm\")}else n=/<([\\w\\d-]+):([\\w\\d-]+)(#\\d+)?((\\s+?[\\w\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)/gm;return qe(e,n).map(Xe.unpackMatch)}static unpackMatch(e){let t=e[1],i=e[2],n=e[4],s=e[8];return new Xe(t,i,n,s)}constructor(e,t,i,n){this.ns=e,this.name=t,this.attrString=i,this.innerXml=n,this.attrs=Ke.findAll(i),this.children=Xe.findAll(n),this.value=0===this.children.length?Qe(n):void 0,this.properties=[...this.attrs,...this.children]}get isPrimitive(){return void 0!==this.value&&0===this.attrs.length&&0===this.children.length}get isListContainer(){return 1===this.children.length&&this.children[0].isList}get isList(){let{ns:e,name:t}=this;return\"rdf\"===e&&(\"Seq\"===t||\"Bag\"===t||\"Alt\"===t)}get isListItem(){return\"rdf\"===this.ns&&\"li\"===this.name}serialize(){if(0===this.properties.length&&void 0===this.value)return;if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return $e(this.children.map(Ye));if(this.isListItem&&1===this.children.length&&0===this.attrs.length)return this.children[0].serialize();let e={};for(let t of this.properties)_e(t,e);return void 0!==this.value&&(e.value=this.value),f(e)}}function _e(e,t){let i=e.serialize();void 0!==i&&(t[e.name]=i)}var Ye=e=>e.serialize(),$e=e=>1===e.length?e[0]:e,Je=(e,t)=>t[e]?t[e]:t[e]={};function qe(e,t){let i,n=[];if(!e)return n;for(;null!==(i=t.exec(e));)n.push(i);return n}function Qe(e){if(function(e){return null==e||\"null\"===e||\"undefined\"===e||\"\"===e||\"\"===e.trim()}(e))return;let t=Number(e);if(!Number.isNaN(t))return t;let i=e.toLowerCase();return\"true\"===i||\"false\"!==i&&e.trim()}const Ze=[\"rdf:li\",\"rdf:Seq\",\"rdf:Bag\",\"rdf:Alt\",\"rdf:Description\"],et=new RegExp(`(<|\\\\/)(${Ze.join(\"|\")})`,\"g\");var tt=Object.freeze({__proto__:null,default:Me,Exifr:te,fileParsers:w,segmentParsers:T,fileReaders:A,tagKeys:E,tagValues:B,tagRevivers:N,createDictionary:U,extendDictionary:F,fetchUrlAsArrayBuffer:M,readBlobAsArrayBuffer:R,chunkedProps:G,otherSegments:V,segments:z,tiffBlocks:H,segmentsAndBlocks:j,tiffExtractables:W,inheritables:K,allFormatters:X,Options:q,parse:ie,gpsOnlyOptions:me,gps:Se,thumbnailOnlyOptions:Ce,thumbnail:ye,thumbnailUrl:be,orientationOnlyOptions:Ie,orientation:Pe,rotations:ke,get rotateCanvas(){return we},get rotateCss(){return Te},rotation:Ae});const it=[\"xmp\",\"icc\",\"iptc\",\"tiff\"],nt=()=>{};async function st(e,t,i){let n=new q(t);n.chunked=!1,void 0===i&&\"string\"==typeof e&&(i=function(e){let t=e.toLowerCase().split(\".\").pop();if(function(e){return\"exif\"===e||\"tiff\"===e||\"tif\"===e}(t))return\"tiff\";if(it.includes(t))return t}(e));let s=await D(e,n);if(i){if(it.includes(i))return rt(i,s,n);g(\"Invalid segment type\")}else{if(function(e){let t=e.getString(0,50).trim();return t.includes(\"<?xpacket\")||t.includes(\"<x:\")}(s))return rt(\"xmp\",s,n);for(let[e]of T){if(!it.includes(e))continue;let t=await rt(e,s,n).catch(nt);if(t)return t}g(\"Unknown file format\")}}async function rt(e,t,i){let n=i[e];return n.enabled=!0,n.parse=!0,T.get(e).parse(t,n)}let at=l(\"fs\",(e=>e.promises));A.set(\"fs\",class extends ve{async readWhole(){this.chunked=!1,this.fs=await at;let e=await this.fs.readFile(this.input);this._swapBuffer(e)}async readChunked(){this.chunked=!0,this.fs=await at,await this.open(),await this.readChunk(0,this.options.firstChunkSize)}async open(){void 0===this.fh&&(this.fh=await this.fs.open(this.input,\"r\"),this.size=(await this.fh.stat(this.input)).size)}async _readChunk(e,t){void 0===this.fh&&await this.open(),e+t>this.size&&(t=this.size-e);var i=this.subarray(e,t,!0);return await this.fh.read(i.dataView,0,t,e),i}async close(){if(this.fh){let e=this.fh;this.fh=void 0,await e.close()}}});A.set(\"base64\",class extends ve{constructor(...e){super(...e),this.input=this.input.replace(/^data:([^;]+);base64,/gim,\"\"),this.size=this.input.length/4*3,this.input.endsWith(\"==\")?this.size-=2:this.input.endsWith(\"=\")&&(this.size-=1)}async _readChunk(e,t){let i,n,r=this.input;void 0===e?(e=0,i=0,n=0):(i=4*Math.floor(e/3),n=e-i/4*3),void 0===t&&(t=this.size);let o=e+t,l=i+4*Math.ceil(o/3);r=r.slice(i,l);let h=Math.min(t,this.size-e);if(a){let t=s.from(r,\"base64\").slice(n,n+h);return this.set(t,e,!0)}{let t=this.subarray(e,h,!0),i=atob(r),s=t.toUint8();for(let e=0;e<h;e++)s[e]=i.charCodeAt(n+e);return t}}});class ot extends se{static canHandle(e,t){return 18761===t||19789===t}extendOptions(e){let{ifd0:t,xmp:i,iptc:n,icc:s}=e;i.enabled&&t.deps.add(700),n.enabled&&t.deps.add(33723),s.enabled&&t.deps.add(34675),t.finalizeFilters()}async parse(){let{tiff:e,xmp:t,iptc:i,icc:n}=this.options;if(e.enabled||t.enabled||i.enabled||n.enabled){let e=Math.max(S(this.options),this.options.chunkSize);await this.file.ensureChunk(0,e),this.createParser(\"tiff\",this.file),this.parsers.tiff.parseHeader(),await this.parsers.tiff.parseIfd0Block(),this.adaptTiffPropAsSegment(\"xmp\"),this.adaptTiffPropAsSegment(\"iptc\"),this.adaptTiffPropAsSegment(\"icc\")}}adaptTiffPropAsSegment(e){if(this.parsers.tiff[e]){let t=this.parsers.tiff[e];this.injectSegment(e,t)}}}c(ot,\"type\",\"tiff\"),w.set(\"tiff\",ot);let lt=l(\"zlib\");const ht=[\"ihdr\",\"iccp\",\"text\",\"itxt\",\"exif\"];class ut extends se{constructor(...e){super(...e),c(this,\"catchError\",(e=>this.errors.push(e))),c(this,\"metaChunks\",[]),c(this,\"unknownChunks\",[])}static canHandle(e,t){return 35152===t&&2303741511===e.getUint32(0)&&218765834===e.getUint32(4)}async parse(){let{file:e}=this;await this.findPngChunksInRange(\"PNG\\r\\n\u001a\\n\".length,e.byteLength),await this.readSegments(this.metaChunks),this.findIhdr(),this.parseTextChunks(),await this.findExif().catch(this.catchError),await this.findXmp().catch(this.catchError),await this.findIcc().catch(this.catchError)}async findPngChunksInRange(e,t){let{file:i}=this;for(;e<t;){let t=i.getUint32(e),n=i.getUint32(e+4),s=i.getString(e+4,4).toLowerCase(),r=t+4+4+4,a={type:s,offset:e,length:r,start:e+4+4,size:t,marker:n};ht.includes(s)?this.metaChunks.push(a):this.unknownChunks.push(a),e+=r}}parseTextChunks(){let e=this.metaChunks.filter((e=>\"text\"===e.type));for(let t of e){let[e,i]=this.file.getString(t.start,t.size).split(\"\\0\");this.injectKeyValToIhdr(e,i)}}injectKeyValToIhdr(e,t){let i=this.parsers.ihdr;i&&i.raw.set(e,t)}findIhdr(){let e=this.metaChunks.find((e=>\"ihdr\"===e.type));e&&!1!==this.options.ihdr.enabled&&this.createParser(\"ihdr\",e.chunk)}async findExif(){let e=this.metaChunks.find((e=>\"exif\"===e.type));e&&this.injectSegment(\"tiff\",e.chunk)}async findXmp(){let e=this.metaChunks.filter((e=>\"itxt\"===e.type));for(let t of e){\"XML:com.adobe.xmp\"===t.chunk.getString(0,\"XML:com.adobe.xmp\".length)&&this.injectSegment(\"xmp\",t.chunk)}}async findIcc(){let e=this.metaChunks.find((e=>\"iccp\"===e.type));if(!e)return;let{chunk:t}=e,i=t.getUint8Array(0,81),s=0;for(;s<80&&0!==i[s];)s++;let r=s+2,a=t.getString(0,s);if(this.injectKeyValToIhdr(\"ProfileName\",a),n){let e=await lt,i=t.getUint8Array(r);i=e.inflateSync(i),this.injectSegment(\"icc\",i)}}}c(ut,\"type\",\"png\"),w.set(\"png\",ut),U(E,\"interop\",[[1,\"InteropIndex\"],[2,\"InteropVersion\"],[4096,\"RelatedImageFileFormat\"],[4097,\"RelatedImageWidth\"],[4098,\"RelatedImageHeight\"]]),F(E,\"ifd0\",[[11,\"ProcessingSoftware\"],[254,\"SubfileType\"],[255,\"OldSubfileType\"],[263,\"Thresholding\"],[264,\"CellWidth\"],[265,\"CellLength\"],[266,\"FillOrder\"],[269,\"DocumentName\"],[280,\"MinSampleValue\"],[281,\"MaxSampleValue\"],[285,\"PageName\"],[286,\"XPosition\"],[287,\"YPosition\"],[290,\"GrayResponseUnit\"],[297,\"PageNumber\"],[321,\"HalftoneHints\"],[322,\"TileWidth\"],[323,\"TileLength\"],[332,\"InkSet\"],[337,\"TargetPrinter\"],[18246,\"Rating\"],[18249,\"RatingPercent\"],[33550,\"PixelScale\"],[34264,\"ModelTransform\"],[34377,\"PhotoshopSettings\"],[50706,\"DNGVersion\"],[50707,\"DNGBackwardVersion\"],[50708,\"UniqueCameraModel\"],[50709,\"LocalizedCameraModel\"],[50736,\"DNGLensInfo\"],[50739,\"ShadowScale\"],[50740,\"DNGPrivateData\"],[33920,\"IntergraphMatrix\"],[33922,\"ModelTiePoint\"],[34118,\"SEMInfo\"],[34735,\"GeoTiffDirectory\"],[34736,\"GeoTiffDoubleParams\"],[34737,\"GeoTiffAsciiParams\"],[50341,\"PrintIM\"],[50721,\"ColorMatrix1\"],[50722,\"ColorMatrix2\"],[50723,\"CameraCalibration1\"],[50724,\"CameraCalibration2\"],[50725,\"ReductionMatrix1\"],[50726,\"ReductionMatrix2\"],[50727,\"AnalogBalance\"],[50728,\"AsShotNeutral\"],[50729,\"AsShotWhiteXY\"],[50730,\"BaselineExposure\"],[50731,\"BaselineNoise\"],[50732,\"BaselineSharpness\"],[50734,\"LinearResponseLimit\"],[50735,\"CameraSerialNumber\"],[50741,\"MakerNoteSafety\"],[50778,\"CalibrationIlluminant1\"],[50779,\"CalibrationIlluminant2\"],[50781,\"RawDataUniqueID\"],[50827,\"OriginalRawFileName\"],[50828,\"OriginalRawFileData\"],[50831,\"AsShotICCProfile\"],[50832,\"AsShotPreProfileMatrix\"],[50833,\"CurrentICCProfile\"],[50834,\"CurrentPreProfileMatrix\"],[50879,\"ColorimetricReference\"],[50885,\"SRawType\"],[50898,\"PanasonicTitle\"],[50899,\"PanasonicTitle2\"],[50931,\"CameraCalibrationSig\"],[50932,\"ProfileCalibrationSig\"],[50933,\"ProfileIFD\"],[50934,\"AsShotProfileName\"],[50936,\"ProfileName\"],[50937,\"ProfileHueSatMapDims\"],[50938,\"ProfileHueSatMapData1\"],[50939,\"ProfileHueSatMapData2\"],[50940,\"ProfileToneCurve\"],[50941,\"ProfileEmbedPolicy\"],[50942,\"ProfileCopyright\"],[50964,\"ForwardMatrix1\"],[50965,\"ForwardMatrix2\"],[50966,\"PreviewApplicationName\"],[50967,\"PreviewApplicationVersion\"],[50968,\"PreviewSettingsName\"],[50969,\"PreviewSettingsDigest\"],[50970,\"PreviewColorSpace\"],[50971,\"PreviewDateTime\"],[50972,\"RawImageDigest\"],[50973,\"OriginalRawFileDigest\"],[50981,\"ProfileLookTableDims\"],[50982,\"ProfileLookTableData\"],[51043,\"TimeCodes\"],[51044,\"FrameRate\"],[51058,\"TStop\"],[51081,\"ReelName\"],[51089,\"OriginalDefaultFinalSize\"],[51090,\"OriginalBestQualitySize\"],[51091,\"OriginalDefaultCropSize\"],[51105,\"CameraLabel\"],[51107,\"ProfileHueSatMapEncoding\"],[51108,\"ProfileLookTableEncoding\"],[51109,\"BaselineExposureOffset\"],[51110,\"DefaultBlackRender\"],[51111,\"NewRawImageDigest\"],[51112,\"RawToPreviewGain\"]]);let ct=[[273,\"StripOffsets\"],[279,\"StripByteCounts\"],[288,\"FreeOffsets\"],[289,\"FreeByteCounts\"],[291,\"GrayResponseCurve\"],[292,\"T4Options\"],[293,\"T6Options\"],[300,\"ColorResponseUnit\"],[320,\"ColorMap\"],[324,\"TileOffsets\"],[325,\"TileByteCounts\"],[326,\"BadFaxLines\"],[327,\"CleanFaxData\"],[328,\"ConsecutiveBadFaxLines\"],[330,\"SubIFD\"],[333,\"InkNames\"],[334,\"NumberofInks\"],[336,\"DotRange\"],[338,\"ExtraSamples\"],[339,\"SampleFormat\"],[340,\"SMinSampleValue\"],[341,\"SMaxSampleValue\"],[342,\"TransferRange\"],[343,\"ClipPath\"],[344,\"XClipPathUnits\"],[345,\"YClipPathUnits\"],[346,\"Indexed\"],[347,\"JPEGTables\"],[351,\"OPIProxy\"],[400,\"GlobalParametersIFD\"],[401,\"ProfileType\"],[402,\"FaxProfile\"],[403,\"CodingMethods\"],[404,\"VersionYear\"],[405,\"ModeNumber\"],[433,\"Decode\"],[434,\"DefaultImageColor\"],[435,\"T82Options\"],[437,\"JPEGTables\"],[512,\"JPEGProc\"],[515,\"JPEGRestartInterval\"],[517,\"JPEGLosslessPredictors\"],[518,\"JPEGPointTransforms\"],[519,\"JPEGQTables\"],[520,\"JPEGDCTables\"],[521,\"JPEGACTables\"],[559,\"StripRowCounts\"],[999,\"USPTOMiscellaneous\"],[18247,\"XP_DIP_XML\"],[18248,\"StitchInfo\"],[28672,\"SonyRawFileType\"],[28688,\"SonyToneCurve\"],[28721,\"VignettingCorrection\"],[28722,\"VignettingCorrParams\"],[28724,\"ChromaticAberrationCorrection\"],[28725,\"ChromaticAberrationCorrParams\"],[28726,\"DistortionCorrection\"],[28727,\"DistortionCorrParams\"],[29895,\"SonyCropTopLeft\"],[29896,\"SonyCropSize\"],[32781,\"ImageID\"],[32931,\"WangTag1\"],[32932,\"WangAnnotation\"],[32933,\"WangTag3\"],[32934,\"WangTag4\"],[32953,\"ImageReferencePoints\"],[32954,\"RegionXformTackPoint\"],[32955,\"WarpQuadrilateral\"],[32956,\"AffineTransformMat\"],[32995,\"Matteing\"],[32996,\"DataType\"],[32997,\"ImageDepth\"],[32998,\"TileDepth\"],[33300,\"ImageFullWidth\"],[33301,\"ImageFullHeight\"],[33302,\"TextureFormat\"],[33303,\"WrapModes\"],[33304,\"FovCot\"],[33305,\"MatrixWorldToScreen\"],[33306,\"MatrixWorldToCamera\"],[33405,\"Model2\"],[33421,\"CFARepeatPatternDim\"],[33422,\"CFAPattern2\"],[33423,\"BatteryLevel\"],[33424,\"KodakIFD\"],[33445,\"MDFileTag\"],[33446,\"MDScalePixel\"],[33447,\"MDColorTable\"],[33448,\"MDLabName\"],[33449,\"MDSampleInfo\"],[33450,\"MDPrepDate\"],[33451,\"MDPrepTime\"],[33452,\"MDFileUnits\"],[33589,\"AdventScale\"],[33590,\"AdventRevision\"],[33628,\"UIC1Tag\"],[33629,\"UIC2Tag\"],[33630,\"UIC3Tag\"],[33631,\"UIC4Tag\"],[33918,\"IntergraphPacketData\"],[33919,\"IntergraphFlagRegisters\"],[33921,\"INGRReserved\"],[34016,\"Site\"],[34017,\"ColorSequence\"],[34018,\"IT8Header\"],[34019,\"RasterPadding\"],[34020,\"BitsPerRunLength\"],[34021,\"BitsPerExtendedRunLength\"],[34022,\"ColorTable\"],[34023,\"ImageColorIndicator\"],[34024,\"BackgroundColorIndicator\"],[34025,\"ImageColorValue\"],[34026,\"BackgroundColorValue\"],[34027,\"PixelIntensityRange\"],[34028,\"TransparencyIndicator\"],[34029,\"ColorCharacterization\"],[34030,\"HCUsage\"],[34031,\"TrapIndicator\"],[34032,\"CMYKEquivalent\"],[34152,\"AFCP_IPTC\"],[34232,\"PixelMagicJBIGOptions\"],[34263,\"JPLCartoIFD\"],[34306,\"WB_GRGBLevels\"],[34310,\"LeafData\"],[34687,\"TIFF_FXExtensions\"],[34688,\"MultiProfiles\"],[34689,\"SharedData\"],[34690,\"T88Options\"],[34732,\"ImageLayer\"],[34750,\"JBIGOptions\"],[34856,\"Opto-ElectricConvFactor\"],[34857,\"Interlace\"],[34908,\"FaxRecvParams\"],[34909,\"FaxSubAddress\"],[34910,\"FaxRecvTime\"],[34929,\"FedexEDR\"],[34954,\"LeafSubIFD\"],[37387,\"FlashEnergy\"],[37388,\"SpatialFrequencyResponse\"],[37389,\"Noise\"],[37390,\"FocalPlaneXResolution\"],[37391,\"FocalPlaneYResolution\"],[37392,\"FocalPlaneResolutionUnit\"],[37397,\"ExposureIndex\"],[37398,\"TIFF-EPStandardID\"],[37399,\"SensingMethod\"],[37434,\"CIP3DataFile\"],[37435,\"CIP3Sheet\"],[37436,\"CIP3Side\"],[37439,\"StoNits\"],[37679,\"MSDocumentText\"],[37680,\"MSPropertySetStorage\"],[37681,\"MSDocumentTextPosition\"],[37724,\"ImageSourceData\"],[40965,\"InteropIFD\"],[40976,\"SamsungRawPointersOffset\"],[40977,\"SamsungRawPointersLength\"],[41217,\"SamsungRawByteOrder\"],[41218,\"SamsungRawUnknown\"],[41484,\"SpatialFrequencyResponse\"],[41485,\"Noise\"],[41489,\"ImageNumber\"],[41490,\"SecurityClassification\"],[41491,\"ImageHistory\"],[41494,\"TIFF-EPStandardID\"],[41995,\"DeviceSettingDescription\"],[42112,\"GDALMetadata\"],[42113,\"GDALNoData\"],[44992,\"ExpandSoftware\"],[44993,\"ExpandLens\"],[44994,\"ExpandFilm\"],[44995,\"ExpandFilterLens\"],[44996,\"ExpandScanner\"],[44997,\"ExpandFlashLamp\"],[46275,\"HasselbladRawImage\"],[48129,\"PixelFormat\"],[48130,\"Transformation\"],[48131,\"Uncompressed\"],[48132,\"ImageType\"],[48256,\"ImageWidth\"],[48257,\"ImageHeight\"],[48258,\"WidthResolution\"],[48259,\"HeightResolution\"],[48320,\"ImageOffset\"],[48321,\"ImageByteCount\"],[48322,\"AlphaOffset\"],[48323,\"AlphaByteCount\"],[48324,\"ImageDataDiscard\"],[48325,\"AlphaDataDiscard\"],[50215,\"OceScanjobDesc\"],[50216,\"OceApplicationSelector\"],[50217,\"OceIDNumber\"],[50218,\"OceImageLogic\"],[50255,\"Annotations\"],[50459,\"HasselbladExif\"],[50547,\"OriginalFileName\"],[50560,\"USPTOOriginalContentType\"],[50656,\"CR2CFAPattern\"],[50710,\"CFAPlaneColor\"],[50711,\"CFALayout\"],[50712,\"LinearizationTable\"],[50713,\"BlackLevelRepeatDim\"],[50714,\"BlackLevel\"],[50715,\"BlackLevelDeltaH\"],[50716,\"BlackLevelDeltaV\"],[50717,\"WhiteLevel\"],[50718,\"DefaultScale\"],[50719,\"DefaultCropOrigin\"],[50720,\"DefaultCropSize\"],[50733,\"BayerGreenSplit\"],[50737,\"ChromaBlurRadius\"],[50738,\"AntiAliasStrength\"],[50752,\"RawImageSegmentation\"],[50780,\"BestQualityScale\"],[50784,\"AliasLayerMetadata\"],[50829,\"ActiveArea\"],[50830,\"MaskedAreas\"],[50935,\"NoiseReductionApplied\"],[50974,\"SubTileBlockSize\"],[50975,\"RowInterleaveFactor\"],[51008,\"OpcodeList1\"],[51009,\"OpcodeList2\"],[51022,\"OpcodeList3\"],[51041,\"NoiseProfile\"],[51114,\"CacheVersion\"],[51125,\"DefaultUserCrop\"],[51157,\"NikonNEFInfo\"],[65024,\"KdcIFD\"]];F(E,\"ifd0\",ct),F(E,\"exif\",ct),U(B,\"gps\",[[23,{M:\"Magnetic North\",T:\"True North\"}],[25,{K:\"Kilometers\",M:\"Miles\",N:\"Nautical Miles\"}]]);class ft extends re{static canHandle(e,t){return 224===e.getUint8(t+1)&&1246120262===e.getUint32(t+4)&&0===e.getUint8(t+8)}parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint16(0)],[2,this.chunk.getUint8(2)],[3,this.chunk.getUint16(3)],[5,this.chunk.getUint16(5)],[7,this.chunk.getUint8(7)],[8,this.chunk.getUint8(8)]])}}c(ft,\"type\",\"jfif\"),c(ft,\"headerLength\",9),T.set(\"jfif\",ft),U(E,\"jfif\",[[0,\"JFIFVersion\"],[2,\"ResolutionUnit\"],[3,\"XResolution\"],[5,\"YResolution\"],[7,\"ThumbnailWidth\"],[8,\"ThumbnailHeight\"]]);class dt extends re{parse(){return this.parseTags(),this.translate(),this.output}parseTags(){this.raw=new Map([[0,this.chunk.getUint32(0)],[4,this.chunk.getUint32(4)],[8,this.chunk.getUint8(8)],[9,this.chunk.getUint8(9)],[10,this.chunk.getUint8(10)],[11,this.chunk.getUint8(11)],[12,this.chunk.getUint8(12)],...Array.from(this.raw)])}}c(dt,\"type\",\"ihdr\"),T.set(\"ihdr\",dt),U(E,\"ihdr\",[[0,\"ImageWidth\"],[4,\"ImageHeight\"],[8,\"BitDepth\"],[9,\"ColorType\"],[10,\"Compression\"],[11,\"Filter\"],[12,\"Interlace\"]]),U(B,\"ihdr\",[[9,{0:\"Grayscale\",2:\"RGB\",3:\"Palette\",4:\"Grayscale with Alpha\",6:\"RGB with Alpha\",DEFAULT:\"Unknown\"}],[10,{0:\"Deflate/Inflate\",DEFAULT:\"Unknown\"}],[11,{0:\"Adaptive\",DEFAULT:\"Unknown\"}],[12,{0:\"Noninterlaced\",1:\"Adam7 Interlace\",DEFAULT:\"Unknown\"}]]);class pt extends re{static canHandle(e,t){return 226===e.getUint8(t+1)&&1229144927===e.getUint32(t+4)}static findPosition(e,t){let i=super.findPosition(e,t);return i.chunkNumber=e.getUint8(t+16),i.chunkCount=e.getUint8(t+17),i.multiSegment=i.chunkCount>1,i}static handleMultiSegments(e){return function(e){let t=function(e){let t=e[0].constructor,i=0;for(let t of e)i+=t.length;let n=new t(i),s=0;for(let t of e)n.set(t,s),s+=t.length;return n}(e.map((e=>e.chunk.toUint8())));return new I(t)}(e)}parse(){return this.raw=new Map,this.parseHeader(),this.parseTags(),this.translate(),this.output}parseHeader(){let{raw:e}=this;this.chunk.byteLength<84&&g(\"ICC header is too short\");for(let[t,i]of Object.entries(gt)){t=parseInt(t,10);let n=i(this.chunk,t);\"\\0\\0\\0\\0\"!==n&&e.set(t,n)}}parseTags(){let e,t,i,n,s,{raw:r}=this,a=this.chunk.getUint32(128),o=132,l=this.chunk.byteLength;for(;a--;){if(e=this.chunk.getString(o,4),t=this.chunk.getUint32(o+4),i=this.chunk.getUint32(o+8),n=this.chunk.getString(t,4),t+i>l)return void console.warn(\"reached the end of the first ICC chunk. Enable options.tiff.multiSegment to read all ICC segments.\");s=this.parseTag(n,t,i),void 0!==s&&\"\\0\\0\\0\\0\"!==s&&r.set(e,s),o+=12}}parseTag(e,t,i){switch(e){case\"desc\":return this.parseDesc(t);case\"mluc\":return this.parseMluc(t);case\"text\":return this.parseText(t,i);case\"sig \":return this.parseSig(t)}if(!(t+i>this.chunk.byteLength))return this.chunk.getUint8Array(t,i)}parseDesc(e){let t=this.chunk.getUint32(e+8)-1;return m(this.chunk.getString(e+12,t))}parseText(e,t){return m(this.chunk.getString(e+8,t-8))}parseSig(e){return m(this.chunk.getString(e+8,4))}parseMluc(e){let{chunk:t}=this,i=t.getUint32(e+8),n=t.getUint32(e+12),s=e+16,r=[];for(let a=0;a<i;a++){let i=t.getString(s+0,2),a=t.getString(s+2,2),o=t.getUint32(s+4),l=t.getUint32(s+8)+e,h=m(t.getUnicodeString(l,o));r.push({lang:i,country:a,text:h}),s+=n}return 1===i?r[0].text:r}translateValue(e,t){return\"string\"==typeof e?t[e]||t[e.toLowerCase()]||e:t[e]||e}}c(pt,\"type\",\"icc\"),c(pt,\"multiSegment\",!0),c(pt,\"headerLength\",18);const gt={4:mt,8:function(e,t){return[e.getUint8(t),e.getUint8(t+1)>>4,e.getUint8(t+1)%16].map((e=>e.toString(10))).join(\".\")},12:mt,16:mt,20:mt,24:function(e,t){const i=e.getUint16(t),n=e.getUint16(t+2)-1,s=e.getUint16(t+4),r=e.getUint16(t+6),a=e.getUint16(t+8),o=e.getUint16(t+10);return new Date(Date.UTC(i,n,s,r,a,o))},36:mt,40:mt,48:mt,52:mt,64:(e,t)=>e.getUint32(t),80:mt};function mt(e,t){return m(e.getString(t,4))}T.set(\"icc\",pt),U(E,\"icc\",[[4,\"ProfileCMMType\"],[8,\"ProfileVersion\"],[12,\"ProfileClass\"],[16,\"ColorSpaceData\"],[20,\"ProfileConnectionSpace\"],[24,\"ProfileDateTime\"],[36,\"ProfileFileSignature\"],[40,\"PrimaryPlatform\"],[44,\"CMMFlags\"],[48,\"DeviceManufacturer\"],[52,\"DeviceModel\"],[56,\"DeviceAttributes\"],[64,\"RenderingIntent\"],[68,\"ConnectionSpaceIlluminant\"],[80,\"ProfileCreator\"],[84,\"ProfileID\"],[\"Header\",\"ProfileHeader\"],[\"MS00\",\"WCSProfiles\"],[\"bTRC\",\"BlueTRC\"],[\"bXYZ\",\"BlueMatrixColumn\"],[\"bfd\",\"UCRBG\"],[\"bkpt\",\"MediaBlackPoint\"],[\"calt\",\"CalibrationDateTime\"],[\"chad\",\"ChromaticAdaptation\"],[\"chrm\",\"Chromaticity\"],[\"ciis\",\"ColorimetricIntentImageState\"],[\"clot\",\"ColorantTableOut\"],[\"clro\",\"ColorantOrder\"],[\"clrt\",\"ColorantTable\"],[\"cprt\",\"ProfileCopyright\"],[\"crdi\",\"CRDInfo\"],[\"desc\",\"ProfileDescription\"],[\"devs\",\"DeviceSettings\"],[\"dmdd\",\"DeviceModelDesc\"],[\"dmnd\",\"DeviceMfgDesc\"],[\"dscm\",\"ProfileDescriptionML\"],[\"fpce\",\"FocalPlaneColorimetryEstimates\"],[\"gTRC\",\"GreenTRC\"],[\"gXYZ\",\"GreenMatrixColumn\"],[\"gamt\",\"Gamut\"],[\"kTRC\",\"GrayTRC\"],[\"lumi\",\"Luminance\"],[\"meas\",\"Measurement\"],[\"meta\",\"Metadata\"],[\"mmod\",\"MakeAndModel\"],[\"ncl2\",\"NamedColor2\"],[\"ncol\",\"NamedColor\"],[\"ndin\",\"NativeDisplayInfo\"],[\"pre0\",\"Preview0\"],[\"pre1\",\"Preview1\"],[\"pre2\",\"Preview2\"],[\"ps2i\",\"PS2RenderingIntent\"],[\"ps2s\",\"PostScript2CSA\"],[\"psd0\",\"PostScript2CRD0\"],[\"psd1\",\"PostScript2CRD1\"],[\"psd2\",\"PostScript2CRD2\"],[\"psd3\",\"PostScript2CRD3\"],[\"pseq\",\"ProfileSequenceDesc\"],[\"psid\",\"ProfileSequenceIdentifier\"],[\"psvm\",\"PS2CRDVMSize\"],[\"rTRC\",\"RedTRC\"],[\"rXYZ\",\"RedMatrixColumn\"],[\"resp\",\"OutputResponse\"],[\"rhoc\",\"ReflectionHardcopyOrigColorimetry\"],[\"rig0\",\"PerceptualRenderingIntentGamut\"],[\"rig2\",\"SaturationRenderingIntentGamut\"],[\"rpoc\",\"ReflectionPrintOutputColorimetry\"],[\"sape\",\"SceneAppearanceEstimates\"],[\"scoe\",\"SceneColorimetryEstimates\"],[\"scrd\",\"ScreeningDesc\"],[\"scrn\",\"Screening\"],[\"targ\",\"CharTarget\"],[\"tech\",\"Technology\"],[\"vcgt\",\"VideoCardGamma\"],[\"view\",\"ViewingConditions\"],[\"vued\",\"ViewingCondDesc\"],[\"wtpt\",\"MediaWhitePoint\"]]);const St={\"4d2p\":\"Erdt Systems\",AAMA:\"Aamazing Technologies\",ACER:\"Acer\",ACLT:\"Acolyte Color Research\",ACTI:\"Actix Sytems\",ADAR:\"Adara Technology\",ADBE:\"Adobe\",ADI:\"ADI Systems\",AGFA:\"Agfa Graphics\",ALMD:\"Alps Electric\",ALPS:\"Alps Electric\",ALWN:\"Alwan Color Expertise\",AMTI:\"Amiable Technologies\",AOC:\"AOC International\",APAG:\"Apago\",APPL:\"Apple Computer\",AST:\"AST\",\"AT&T\":\"AT&T\",BAEL:\"BARBIERI electronic\",BRCO:\"Barco NV\",BRKP:\"Breakpoint\",BROT:\"Brother\",BULL:\"Bull\",BUS:\"Bus Computer Systems\",\"C-IT\":\"C-Itoh\",CAMR:\"Intel\",CANO:\"Canon\",CARR:\"Carroll Touch\",CASI:\"Casio\",CBUS:\"Colorbus PL\",CEL:\"Crossfield\",CELx:\"Crossfield\",CGS:\"CGS Publishing Technologies International\",CHM:\"Rochester Robotics\",CIGL:\"Colour Imaging Group, London\",CITI:\"Citizen\",CL00:\"Candela\",CLIQ:\"Color IQ\",CMCO:\"Chromaco\",CMiX:\"CHROMiX\",COLO:\"Colorgraphic Communications\",COMP:\"Compaq\",COMp:\"Compeq/Focus Technology\",CONR:\"Conrac Display Products\",CORD:\"Cordata Technologies\",CPQ:\"Compaq\",CPRO:\"ColorPro\",CRN:\"Cornerstone\",CTX:\"CTX International\",CVIS:\"ColorVision\",CWC:\"Fujitsu Laboratories\",DARI:\"Darius Technology\",DATA:\"Dataproducts\",DCP:\"Dry Creek Photo\",DCRC:\"Digital Contents Resource Center, Chung-Ang University\",DELL:\"Dell Computer\",DIC:\"Dainippon Ink and Chemicals\",DICO:\"Diconix\",DIGI:\"Digital\",\"DL&C\":\"Digital Light & Color\",DPLG:\"Doppelganger\",DS:\"Dainippon Screen\",DSOL:\"DOOSOL\",DUPN:\"DuPont\",EPSO:\"Epson\",ESKO:\"Esko-Graphics\",ETRI:\"Electronics and Telecommunications Research Institute\",EVER:\"Everex Systems\",EXAC:\"ExactCODE\",Eizo:\"Eizo\",FALC:\"Falco Data Products\",FF:\"Fuji Photo Film\",FFEI:\"FujiFilm Electronic Imaging\",FNRD:\"Fnord Software\",FORA:\"Fora\",FORE:\"Forefront Technology\",FP:\"Fujitsu\",FPA:\"WayTech Development\",FUJI:\"Fujitsu\",FX:\"Fuji Xerox\",GCC:\"GCC Technologies\",GGSL:\"Global Graphics Software\",GMB:\"Gretagmacbeth\",GMG:\"GMG\",GOLD:\"GoldStar Technology\",GOOG:\"Google\",GPRT:\"Giantprint\",GTMB:\"Gretagmacbeth\",GVC:\"WayTech Development\",GW2K:\"Sony\",HCI:\"HCI\",HDM:\"Heidelberger Druckmaschinen\",HERM:\"Hermes\",HITA:\"Hitachi America\",HP:\"Hewlett-Packard\",HTC:\"Hitachi\",HiTi:\"HiTi Digital\",IBM:\"IBM\",IDNT:\"Scitex\",IEC:\"Hewlett-Packard\",IIYA:\"Iiyama North America\",IKEG:\"Ikegami Electronics\",IMAG:\"Image Systems\",IMI:\"Ingram Micro\",INTC:\"Intel\",INTL:\"N/A (INTL)\",INTR:\"Intra Electronics\",IOCO:\"Iocomm International Technology\",IPS:\"InfoPrint Solutions Company\",IRIS:\"Scitex\",ISL:\"Ichikawa Soft Laboratory\",ITNL:\"N/A (ITNL)\",IVM:\"IVM\",IWAT:\"Iwatsu Electric\",Idnt:\"Scitex\",Inca:\"Inca Digital Printers\",Iris:\"Scitex\",JPEG:\"Joint Photographic Experts Group\",JSFT:\"Jetsoft Development\",JVC:\"JVC Information Products\",KART:\"Scitex\",KFC:\"KFC Computek Components\",KLH:\"KLH Computers\",KMHD:\"Konica Minolta\",KNCA:\"Konica\",KODA:\"Kodak\",KYOC:\"Kyocera\",Kart:\"Scitex\",LCAG:\"Leica\",LCCD:\"Leeds Colour\",LDAK:\"Left Dakota\",LEAD:\"Leading Technology\",LEXM:\"Lexmark International\",LINK:\"Link Computer\",LINO:\"Linotronic\",LITE:\"Lite-On\",Leaf:\"Leaf\",Lino:\"Linotronic\",MAGC:\"Mag Computronic\",MAGI:\"MAG Innovision\",MANN:\"Mannesmann\",MICN:\"Micron Technology\",MICR:\"Microtek\",MICV:\"Microvitec\",MINO:\"Minolta\",MITS:\"Mitsubishi Electronics America\",MITs:\"Mitsuba\",MNLT:\"Minolta\",MODG:\"Modgraph\",MONI:\"Monitronix\",MONS:\"Monaco Systems\",MORS:\"Morse Technology\",MOTI:\"Motive Systems\",MSFT:\"Microsoft\",MUTO:\"MUTOH INDUSTRIES\",Mits:\"Mitsubishi Electric\",NANA:\"NANAO\",NEC:\"NEC\",NEXP:\"NexPress Solutions\",NISS:\"Nissei Sangyo America\",NKON:\"Nikon\",NONE:\"none\",OCE:\"Oce Technologies\",OCEC:\"OceColor\",OKI:\"Oki\",OKID:\"Okidata\",OKIP:\"Okidata\",OLIV:\"Olivetti\",OLYM:\"Olympus\",ONYX:\"Onyx Graphics\",OPTI:\"Optiquest\",PACK:\"Packard Bell\",PANA:\"Matsushita Electric Industrial\",PANT:\"Pantone\",PBN:\"Packard Bell\",PFU:\"PFU\",PHIL:\"Philips Consumer Electronics\",PNTX:\"HOYA\",POne:\"Phase One A/S\",PREM:\"Premier Computer Innovations\",PRIN:\"Princeton Graphic Systems\",PRIP:\"Princeton Publishing Labs\",QLUX:\"Hong Kong\",QMS:\"QMS\",QPCD:\"QPcard AB\",QUAD:\"QuadLaser\",QUME:\"Qume\",RADI:\"Radius\",RDDx:\"Integrated Color Solutions\",RDG:\"Roland DG\",REDM:\"REDMS Group\",RELI:\"Relisys\",RGMS:\"Rolf Gierling Multitools\",RICO:\"Ricoh\",RNLD:\"Edmund Ronald\",ROYA:\"Royal\",RPC:\"Ricoh Printing Systems\",RTL:\"Royal Information Electronics\",SAMP:\"Sampo\",SAMS:\"Samsung\",SANT:\"Jaime Santana Pomares\",SCIT:\"Scitex\",SCRN:\"Dainippon Screen\",SDP:\"Scitex\",SEC:\"Samsung\",SEIK:\"Seiko Instruments\",SEIk:\"Seikosha\",SGUY:\"ScanGuy.com\",SHAR:\"Sharp Laboratories\",SICC:\"International Color Consortium\",SONY:\"Sony\",SPCL:\"SpectraCal\",STAR:\"Star\",STC:\"Sampo Technology\",Scit:\"Scitex\",Sdp:\"Scitex\",Sony:\"Sony\",TALO:\"Talon Technology\",TAND:\"Tandy\",TATU:\"Tatung\",TAXA:\"TAXAN America\",TDS:\"Tokyo Denshi Sekei\",TECO:\"TECO Information Systems\",TEGR:\"Tegra\",TEKT:\"Tektronix\",TI:\"Texas Instruments\",TMKR:\"TypeMaker\",TOSB:\"Toshiba\",TOSH:\"Toshiba\",TOTK:\"TOTOKU ELECTRIC\",TRIU:\"Triumph\",TSBT:\"Toshiba\",TTX:\"TTX Computer Products\",TVM:\"TVM Professional Monitor\",TW:\"TW Casper\",ULSX:\"Ulead Systems\",UNIS:\"Unisys\",UTZF:\"Utz Fehlau & Sohn\",VARI:\"Varityper\",VIEW:\"Viewsonic\",VISL:\"Visual communication\",VIVO:\"Vivo Mobile Communication\",WANG:\"Wang\",WLBR:\"Wilbur Imaging\",WTG2:\"Ware To Go\",WYSE:\"WYSE Technology\",XERX:\"Xerox\",XRIT:\"X-Rite\",ZRAN:\"Zoran\",Zebr:\"Zebra Technologies\",appl:\"Apple Computer\",bICC:\"basICColor\",berg:\"bergdesign\",ceyd:\"Integrated Color Solutions\",clsp:\"MacDermid ColorSpan\",ds:\"Dainippon Screen\",dupn:\"DuPont\",ffei:\"FujiFilm Electronic Imaging\",flux:\"FluxData\",iris:\"Scitex\",kart:\"Scitex\",lcms:\"Little CMS\",lino:\"Linotronic\",none:\"none\",ob4d:\"Erdt Systems\",obic:\"Medigraph\",quby:\"Qubyx Sarl\",scit:\"Scitex\",scrn:\"Dainippon Screen\",sdp:\"Scitex\",siwi:\"SIWI GRAFIKA\",yxym:\"YxyMaster\"},Ct={scnr:\"Scanner\",mntr:\"Monitor\",prtr:\"Printer\",link:\"Device Link\",abst:\"Abstract\",spac:\"Color Space Conversion Profile\",nmcl:\"Named Color\",cenc:\"ColorEncodingSpace profile\",mid:\"MultiplexIdentification profile\",mlnk:\"MultiplexLink profile\",mvis:\"MultiplexVisualization profile\",nkpf:\"Nikon Input Device Profile (NON-STANDARD!)\"};U(B,\"icc\",[[4,St],[12,Ct],[40,Object.assign({},St,Ct)],[48,St],[80,St],[64,{0:\"Perceptual\",1:\"Relative Colorimetric\",2:\"Saturation\",3:\"Absolute Colorimetric\"}],[\"tech\",{amd:\"Active Matrix Display\",crt:\"Cathode Ray Tube Display\",kpcd:\"Photo CD\",pmd:\"Passive Matrix Display\",dcam:\"Digital Camera\",dcpj:\"Digital Cinema Projector\",dmpc:\"Digital Motion Picture Camera\",dsub:\"Dye Sublimation Printer\",epho:\"Electrophotographic Printer\",esta:\"Electrostatic Printer\",flex:\"Flexography\",fprn:\"Film Writer\",fscn:\"Film Scanner\",grav:\"Gravure\",ijet:\"Ink Jet Printer\",imgs:\"Photo Image Setter\",mpfr:\"Motion Picture Film Recorder\",mpfs:\"Motion Picture Film Scanner\",offs:\"Offset Lithography\",pjtv:\"Projection Television\",rpho:\"Photographic Paper Printer\",rscn:\"Reflective Scanner\",silk:\"Silkscreen\",twax:\"Thermal Wax Printer\",vidc:\"Video Camera\",vidm:\"Video Monitor\"}]]);class yt extends re{static canHandle(e,t,i){return 237===e.getUint8(t+1)&&\"Photoshop\"===e.getString(t+4,9)&&void 0!==this.containsIptc8bim(e,t,i)}static headerLength(e,t,i){let n,s=this.containsIptc8bim(e,t,i);if(void 0!==s)return n=e.getUint8(t+s+7),n%2!=0&&(n+=1),0===n&&(n=4),s+8+n}static containsIptc8bim(e,t,i){for(let n=0;n<i;n++)if(this.isIptcSegmentHead(e,t+n))return n}static isIptcSegmentHead(e,t){return 56===e.getUint8(t)&&943868237===e.getUint32(t)&&1028===e.getUint16(t+4)}parse(){let{raw:e}=this,t=this.chunk.byteLength-1,i=!1;for(let n=0;n<t;n++)if(28===this.chunk.getUint8(n)&&2===this.chunk.getUint8(n+1)){i=!0;let t=this.chunk.getUint16(n+3),s=this.chunk.getUint8(n+2),r=this.chunk.getLatin1String(n+5,t);e.set(s,this.pluralizeValue(e.get(s),r)),n+=4+t}else if(i)break;return this.translate(),this.output}pluralizeValue(e,t){return void 0!==e?e instanceof Array?(e.push(t),e):[e,t]:t}}c(yt,\"type\",\"iptc\"),c(yt,\"translateValues\",!1),c(yt,\"reviveValues\",!1),T.set(\"iptc\",yt),U(E,\"iptc\",[[0,\"ApplicationRecordVersion\"],[3,\"ObjectTypeReference\"],[4,\"ObjectAttributeReference\"],[5,\"ObjectName\"],[7,\"EditStatus\"],[8,\"EditorialUpdate\"],[10,\"Urgency\"],[12,\"SubjectReference\"],[15,\"Category\"],[20,\"SupplementalCategories\"],[22,\"FixtureIdentifier\"],[25,\"Keywords\"],[26,\"ContentLocationCode\"],[27,\"ContentLocationName\"],[30,\"ReleaseDate\"],[35,\"ReleaseTime\"],[37,\"ExpirationDate\"],[38,\"ExpirationTime\"],[40,\"SpecialInstructions\"],[42,\"ActionAdvised\"],[45,\"ReferenceService\"],[47,\"ReferenceDate\"],[50,\"ReferenceNumber\"],[55,\"DateCreated\"],[60,\"TimeCreated\"],[62,\"DigitalCreationDate\"],[63,\"DigitalCreationTime\"],[65,\"OriginatingProgram\"],[70,\"ProgramVersion\"],[75,\"ObjectCycle\"],[80,\"Byline\"],[85,\"BylineTitle\"],[90,\"City\"],[92,\"Sublocation\"],[95,\"State\"],[100,\"CountryCode\"],[101,\"Country\"],[103,\"OriginalTransmissionReference\"],[105,\"Headline\"],[110,\"Credit\"],[115,\"Source\"],[116,\"CopyrightNotice\"],[118,\"Contact\"],[120,\"Caption\"],[121,\"LocalCaption\"],[122,\"Writer\"],[125,\"RasterizedCaption\"],[130,\"ImageType\"],[131,\"ImageOrientation\"],[135,\"LanguageIdentifier\"],[150,\"AudioType\"],[151,\"AudioSamplingRate\"],[152,\"AudioSamplingResolution\"],[153,\"AudioDuration\"],[154,\"AudioOutcue\"],[184,\"JobID\"],[185,\"MasterDocumentID\"],[186,\"ShortDocumentID\"],[187,\"UniqueDocumentID\"],[188,\"OwnerID\"],[200,\"ObjectPreviewFileFormat\"],[201,\"ObjectPreviewFileVersion\"],[202,\"ObjectPreviewData\"],[221,\"Prefs\"],[225,\"ClassifyState\"],[228,\"SimilarityIndex\"],[230,\"DocumentNotes\"],[231,\"DocumentHistory\"],[232,\"ExifCameraInfo\"],[255,\"CatalogSets\"]]),U(B,\"iptc\",[[10,{0:\"0 (reserved)\",1:\"1 (most urgent)\",2:\"2\",3:\"3\",4:\"4\",5:\"5 (normal urgency)\",6:\"6\",7:\"7\",8:\"8 (least urgent)\",9:\"9 (user-defined priority)\"}],[75,{a:\"Morning\",b:\"Both Morning and Evening\",p:\"Evening\"}],[131,{L:\"Landscape\",P:\"Portrait\",S:\"Square\"}]]);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/exifr/dist/full.esm.mjs\n");

/***/ })

};
;