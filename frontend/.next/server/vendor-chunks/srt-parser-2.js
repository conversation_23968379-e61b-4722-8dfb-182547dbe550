"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/srt-parser-2";
exports.ids = ["vendor-chunks/srt-parser-2"];
exports.modules = {

/***/ "(ssr)/./node_modules/srt-parser-2/dist/index.js":
/*!*************************************************!*\
  !*** ./node_modules/srt-parser-2/dist/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nclass Parser {\n    seperator = \",\";\n    timestampToSeconds(srtTimestamp) {\n        const [rest, millisecondsString] = srtTimestamp.split(\",\");\n        const milliseconds = parseInt(millisecondsString);\n        const [hours, minutes, seconds] = rest.split(\":\").map((x) => parseInt(x));\n        const result = milliseconds * 0.001 + seconds + 60 * minutes + 3600 * hours;\n        // fix odd JS roundings, e.g. timestamp '00:01:20,460' result is 80.46000000000001\n        return Math.round(result * 1000) / 1000;\n    }\n    ;\n    correctFormat(time) {\n        // Fix the format if the format is wrong\n        // 00:00:28.9670 Become 00:00:28,967\n        // 00:00:28.967  Become 00:00:28,967\n        // 00:00:28.96   Become 00:00:28,960\n        // 00:00:28.9    Become 00:00:28,900\n        // 00:00:28,96   Become 00:00:28,960\n        // 00:00:28,9    Become 00:00:28,900\n        // 00:00:28,0    Become 00:00:28,000\n        // 00:00:28,01   Become 00:00:28,010\n        // 0:00:10,500   Become 00:00:10,500\n        let str = time.replace(\".\", \",\");\n        var hour = null;\n        var minute = null;\n        var second = null;\n        var millisecond = null;\n        // Handle millisecond\n        var [front, ms] = str.split(\",\");\n        millisecond = this.fixed_str_digit(3, ms);\n        // Handle hour\n        var [a_hour, a_minute, a_second] = front.split(\":\");\n        hour = this.fixed_str_digit(2, a_hour, false);\n        minute = this.fixed_str_digit(2, a_minute, false);\n        second = this.fixed_str_digit(2, a_second, false);\n        return `${hour}:${minute}:${second},${millisecond}`;\n    }\n    /*\n    // make sure string is 'how_many_digit' long\n    // if str is shorter than how_many_digit, pad with 0\n    // if str is longer than how_many_digit, slice from the beginning\n    // Example:\n  \n    Input: fixed_str_digit(3, '100')\n    Output: 100\n    Explain: unchanged, because \"100\" is 3 digit\n  \n    Input: fixed_str_digit(3, '50')\n    Output: 500\n    Explain: pad end with 0\n  \n    Input: fixed_str_digit(3, '50', false)\n    Output: 050\n    Explain: pad start with 0\n  \n    Input: fixed_str_digit(3, '7771')\n    Output: 777\n    Explain: slice from beginning\n    */\n    fixed_str_digit(how_many_digit, str, padEnd = true) {\n        if (str.length == how_many_digit) {\n            return str;\n        }\n        if (str.length > how_many_digit) {\n            return str.slice(0, how_many_digit);\n        }\n        if (str.length < how_many_digit) {\n            if (padEnd) {\n                return str.padEnd(how_many_digit, \"0\");\n            }\n            else {\n                return str.padStart(how_many_digit, \"0\");\n            }\n        }\n    }\n    tryComma(data) {\n        data = data.replace(/\\r/g, \"\");\n        var regex = /(\\d+)\\n(\\d{1,2}:\\d{2}:\\d{2},\\d{1,3}) --> (\\d{1,2}:\\d{2}:\\d{2},\\d{1,3})/g;\n        let data_array = data.split(regex);\n        data_array.shift(); // remove first '' in array\n        return data_array;\n    }\n    tryDot(data) {\n        data = data.replace(/\\r/g, \"\");\n        var regex = /(\\d+)\\n(\\d{1,2}:\\d{2}:\\d{2}\\.\\d{1,3}) --> (\\d{1,2}:\\d{2}:\\d{2}\\.\\d{1,3})/g;\n        let data_array = data.split(regex);\n        data_array.shift(); // remove first '' in array\n        this.seperator = \".\";\n        return data_array;\n    }\n    fromSrt(data) {\n        var originalData = data;\n        var data_array = this.tryComma(originalData);\n        if (data_array.length == 0) {\n            data_array = this.tryDot(originalData);\n        }\n        var items = [];\n        for (var i = 0; i < data_array.length; i += 4) {\n            const startTime = this.correctFormat(data_array[i + 1].trim());\n            const endTime = this.correctFormat(data_array[i + 2].trim());\n            var new_line = {\n                id: data_array[i].trim(),\n                startTime,\n                startSeconds: this.timestampToSeconds(startTime),\n                endTime,\n                endSeconds: this.timestampToSeconds(endTime),\n                text: data_array[i + 3].trim(),\n            };\n            items.push(new_line);\n        }\n        return items;\n    }\n    toSrt(data) {\n        var res = \"\";\n        const end_of_line = \"\\r\\n\";\n        for (var i = 0; i < data.length; i++) {\n            var s = data[i];\n            res += s.id + end_of_line;\n            res += s.startTime + \" --> \" + s.endTime + end_of_line;\n            res += s.text.replace(\"\\n\", end_of_line) + end_of_line + end_of_line;\n        }\n        return res;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Parser);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/srt-parser-2/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/srt-parser-2/dist/index.js":
/*!*************************************************!*\
  !*** ./node_modules/srt-parser-2/dist/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nclass Parser {\n    seperator = \",\";\n    timestampToSeconds(srtTimestamp) {\n        const [rest, millisecondsString] = srtTimestamp.split(\",\");\n        const milliseconds = parseInt(millisecondsString);\n        const [hours, minutes, seconds] = rest.split(\":\").map((x) => parseInt(x));\n        const result = milliseconds * 0.001 + seconds + 60 * minutes + 3600 * hours;\n        // fix odd JS roundings, e.g. timestamp '00:01:20,460' result is 80.46000000000001\n        return Math.round(result * 1000) / 1000;\n    }\n    ;\n    correctFormat(time) {\n        // Fix the format if the format is wrong\n        // 00:00:28.9670 Become 00:00:28,967\n        // 00:00:28.967  Become 00:00:28,967\n        // 00:00:28.96   Become 00:00:28,960\n        // 00:00:28.9    Become 00:00:28,900\n        // 00:00:28,96   Become 00:00:28,960\n        // 00:00:28,9    Become 00:00:28,900\n        // 00:00:28,0    Become 00:00:28,000\n        // 00:00:28,01   Become 00:00:28,010\n        // 0:00:10,500   Become 00:00:10,500\n        let str = time.replace(\".\", \",\");\n        var hour = null;\n        var minute = null;\n        var second = null;\n        var millisecond = null;\n        // Handle millisecond\n        var [front, ms] = str.split(\",\");\n        millisecond = this.fixed_str_digit(3, ms);\n        // Handle hour\n        var [a_hour, a_minute, a_second] = front.split(\":\");\n        hour = this.fixed_str_digit(2, a_hour, false);\n        minute = this.fixed_str_digit(2, a_minute, false);\n        second = this.fixed_str_digit(2, a_second, false);\n        return `${hour}:${minute}:${second},${millisecond}`;\n    }\n    /*\n    // make sure string is 'how_many_digit' long\n    // if str is shorter than how_many_digit, pad with 0\n    // if str is longer than how_many_digit, slice from the beginning\n    // Example:\n  \n    Input: fixed_str_digit(3, '100')\n    Output: 100\n    Explain: unchanged, because \"100\" is 3 digit\n  \n    Input: fixed_str_digit(3, '50')\n    Output: 500\n    Explain: pad end with 0\n  \n    Input: fixed_str_digit(3, '50', false)\n    Output: 050\n    Explain: pad start with 0\n  \n    Input: fixed_str_digit(3, '7771')\n    Output: 777\n    Explain: slice from beginning\n    */\n    fixed_str_digit(how_many_digit, str, padEnd = true) {\n        if (str.length == how_many_digit) {\n            return str;\n        }\n        if (str.length > how_many_digit) {\n            return str.slice(0, how_many_digit);\n        }\n        if (str.length < how_many_digit) {\n            if (padEnd) {\n                return str.padEnd(how_many_digit, \"0\");\n            }\n            else {\n                return str.padStart(how_many_digit, \"0\");\n            }\n        }\n    }\n    tryComma(data) {\n        data = data.replace(/\\r/g, \"\");\n        var regex = /(\\d+)\\n(\\d{1,2}:\\d{2}:\\d{2},\\d{1,3}) --> (\\d{1,2}:\\d{2}:\\d{2},\\d{1,3})/g;\n        let data_array = data.split(regex);\n        data_array.shift(); // remove first '' in array\n        return data_array;\n    }\n    tryDot(data) {\n        data = data.replace(/\\r/g, \"\");\n        var regex = /(\\d+)\\n(\\d{1,2}:\\d{2}:\\d{2}\\.\\d{1,3}) --> (\\d{1,2}:\\d{2}:\\d{2}\\.\\d{1,3})/g;\n        let data_array = data.split(regex);\n        data_array.shift(); // remove first '' in array\n        this.seperator = \".\";\n        return data_array;\n    }\n    fromSrt(data) {\n        var originalData = data;\n        var data_array = this.tryComma(originalData);\n        if (data_array.length == 0) {\n            data_array = this.tryDot(originalData);\n        }\n        var items = [];\n        for (var i = 0; i < data_array.length; i += 4) {\n            const startTime = this.correctFormat(data_array[i + 1].trim());\n            const endTime = this.correctFormat(data_array[i + 2].trim());\n            var new_line = {\n                id: data_array[i].trim(),\n                startTime,\n                startSeconds: this.timestampToSeconds(startTime),\n                endTime,\n                endSeconds: this.timestampToSeconds(endTime),\n                text: data_array[i + 3].trim(),\n            };\n            items.push(new_line);\n        }\n        return items;\n    }\n    toSrt(data) {\n        var res = \"\";\n        const end_of_line = \"\\r\\n\";\n        for (var i = 0; i < data.length; i++) {\n            var s = data[i];\n            res += s.id + end_of_line;\n            res += s.startTime + \" --> \" + s.endTime + end_of_line;\n            res += s.text.replace(\"\\n\", end_of_line) + end_of_line + end_of_line;\n        }\n        return res;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Parser);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/srt-parser-2/dist/index.js\n");

/***/ })

};
;