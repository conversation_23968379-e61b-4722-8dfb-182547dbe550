"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_srt-parser-2_dist_index_js"],{

/***/ "(app-pages-browser)/./node_modules/srt-parser-2/dist/index.js":
/*!*************************************************!*\
  !*** ./node_modules/srt-parser-2/dist/index.js ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nclass Parser {\n    seperator = \",\";\n    timestampToSeconds(srtTimestamp) {\n        const [rest, millisecondsString] = srtTimestamp.split(\",\");\n        const milliseconds = parseInt(millisecondsString);\n        const [hours, minutes, seconds] = rest.split(\":\").map((x) => parseInt(x));\n        const result = milliseconds * 0.001 + seconds + 60 * minutes + 3600 * hours;\n        // fix odd JS roundings, e.g. timestamp '00:01:20,460' result is 80.46000000000001\n        return Math.round(result * 1000) / 1000;\n    }\n    ;\n    correctFormat(time) {\n        // Fix the format if the format is wrong\n        // 00:00:28.9670 Become 00:00:28,967\n        // 00:00:28.967  Become 00:00:28,967\n        // 00:00:28.96   Become 00:00:28,960\n        // 00:00:28.9    Become 00:00:28,900\n        // 00:00:28,96   Become 00:00:28,960\n        // 00:00:28,9    Become 00:00:28,900\n        // 00:00:28,0    Become 00:00:28,000\n        // 00:00:28,01   Become 00:00:28,010\n        // 0:00:10,500   Become 00:00:10,500\n        let str = time.replace(\".\", \",\");\n        var hour = null;\n        var minute = null;\n        var second = null;\n        var millisecond = null;\n        // Handle millisecond\n        var [front, ms] = str.split(\",\");\n        millisecond = this.fixed_str_digit(3, ms);\n        // Handle hour\n        var [a_hour, a_minute, a_second] = front.split(\":\");\n        hour = this.fixed_str_digit(2, a_hour, false);\n        minute = this.fixed_str_digit(2, a_minute, false);\n        second = this.fixed_str_digit(2, a_second, false);\n        return `${hour}:${minute}:${second},${millisecond}`;\n    }\n    /*\n    // make sure string is 'how_many_digit' long\n    // if str is shorter than how_many_digit, pad with 0\n    // if str is longer than how_many_digit, slice from the beginning\n    // Example:\n  \n    Input: fixed_str_digit(3, '100')\n    Output: 100\n    Explain: unchanged, because \"100\" is 3 digit\n  \n    Input: fixed_str_digit(3, '50')\n    Output: 500\n    Explain: pad end with 0\n  \n    Input: fixed_str_digit(3, '50', false)\n    Output: 050\n    Explain: pad start with 0\n  \n    Input: fixed_str_digit(3, '7771')\n    Output: 777\n    Explain: slice from beginning\n    */\n    fixed_str_digit(how_many_digit, str, padEnd = true) {\n        if (str.length == how_many_digit) {\n            return str;\n        }\n        if (str.length > how_many_digit) {\n            return str.slice(0, how_many_digit);\n        }\n        if (str.length < how_many_digit) {\n            if (padEnd) {\n                return str.padEnd(how_many_digit, \"0\");\n            }\n            else {\n                return str.padStart(how_many_digit, \"0\");\n            }\n        }\n    }\n    tryComma(data) {\n        data = data.replace(/\\r/g, \"\");\n        var regex = /(\\d+)\\n(\\d{1,2}:\\d{2}:\\d{2},\\d{1,3}) --> (\\d{1,2}:\\d{2}:\\d{2},\\d{1,3})/g;\n        let data_array = data.split(regex);\n        data_array.shift(); // remove first '' in array\n        return data_array;\n    }\n    tryDot(data) {\n        data = data.replace(/\\r/g, \"\");\n        var regex = /(\\d+)\\n(\\d{1,2}:\\d{2}:\\d{2}\\.\\d{1,3}) --> (\\d{1,2}:\\d{2}:\\d{2}\\.\\d{1,3})/g;\n        let data_array = data.split(regex);\n        data_array.shift(); // remove first '' in array\n        this.seperator = \".\";\n        return data_array;\n    }\n    fromSrt(data) {\n        var originalData = data;\n        var data_array = this.tryComma(originalData);\n        if (data_array.length == 0) {\n            data_array = this.tryDot(originalData);\n        }\n        var items = [];\n        for (var i = 0; i < data_array.length; i += 4) {\n            const startTime = this.correctFormat(data_array[i + 1].trim());\n            const endTime = this.correctFormat(data_array[i + 2].trim());\n            var new_line = {\n                id: data_array[i].trim(),\n                startTime,\n                startSeconds: this.timestampToSeconds(startTime),\n                endTime,\n                endSeconds: this.timestampToSeconds(endTime),\n                text: data_array[i + 3].trim(),\n            };\n            items.push(new_line);\n        }\n        return items;\n    }\n    toSrt(data) {\n        var res = \"\";\n        const end_of_line = \"\\r\\n\";\n        for (var i = 0; i < data.length; i++) {\n            var s = data[i];\n            res += s.id + end_of_line;\n            res += s.startTime + \" --> \" + s.endTime + end_of_line;\n            res += s.text.replace(\"\\n\", end_of_line) + end_of_line + end_of_line;\n        }\n        return res;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (Parser);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/srt-parser-2/dist/index.js\n"));

/***/ })

}]);