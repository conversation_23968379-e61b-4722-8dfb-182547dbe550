"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_ui_dashboard-analytics_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chart-column.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ChartColumn; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n            key: \"c24i48\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 17V9\",\n            key: \"2bz60n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M13 17V5\",\n            key: \"1frdt8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 17v-3\",\n            key: \"17ska0\"\n        }\n    ]\n];\nconst ChartColumn = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chart-column\", __iconNode);\n //# sourceMappingURL=chart-column.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chart-pie.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ChartPie; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z\",\n            key: \"pzmjnu\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21.21 15.89A10 10 0 1 1 8 2.83\",\n            key: \"k2fpak\"\n        }\n    ]\n];\nconst ChartPie = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chart-pie\", __iconNode);\n //# sourceMappingURL=chart-pie.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/target.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Target; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"6\",\n            key: \"1vlfrh\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"2\",\n            key: \"1c9p78\"\n        }\n    ]\n];\nconst Target = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"target\", __iconNode);\n //# sourceMappingURL=target.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/dashboard-analytics.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/dashboard-analytics.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardAnalytics: function() { return /* binding */ DashboardAnalytics; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,CheckCircle,Clock,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardAnalytics auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DashboardAnalytics(param) {\n    let { data, timeRange, onTimeRangeChange } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const getGrowthColor = (growth)=>{\n        if (growth > 0) return \"text-green-600 dark:text-green-400\";\n        if (growth < 0) return \"text-red-600 dark:text-red-400\";\n        return \"text-muted-foreground\";\n    };\n    const getGrowthIcon = (growth)=>{\n        if (growth > 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n            lineNumber: 60,\n            columnNumber: 28\n        }, this);\n        if (growth < 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n            lineNumber: 61,\n            columnNumber: 28\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n            lineNumber: 62,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-card rounded-lg shadow border border-border\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-primary/10 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6 text-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-foreground\",\n                                            children: \"Business Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Comprehensive insights into your drone service business\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 bg-muted rounded-lg p-1\",\n                            children: [\n                                {\n                                    id: \"week\",\n                                    label: \"Week\"\n                                },\n                                {\n                                    id: \"month\",\n                                    label: \"Month\"\n                                },\n                                {\n                                    id: \"quarter\",\n                                    label: \"Quarter\"\n                                }\n                            ].map((range)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onTimeRangeChange(range.id),\n                                    className: \"px-3 py-1 rounded-md text-sm font-medium transition-colors \".concat(timeRange === range.id ? \"bg-background text-foreground shadow\" : \"text-muted-foreground hover:text-foreground\"),\n                                    children: range.label\n                                }, range.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-8 px-6\",\n                    children: [\n                        {\n                            id: \"overview\",\n                            label: \"Overview\",\n                            icon: _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                        },\n                        {\n                            id: \"revenue\",\n                            label: \"Revenue\",\n                            icon: _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                        },\n                        {\n                            id: \"projects\",\n                            label: \"Projects\",\n                            icon: _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                        },\n                        {\n                            id: \"performance\",\n                            label: \"Performance\",\n                            icon: _barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n                        }\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === tab.id ? \"border-primary text-primary\" : \"border-transparent text-muted-foreground hover:text-foreground\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary/10 border border-primary/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-8 h-8 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 \".concat(getGrowthColor(data.revenue.growth)),\n                                                children: [\n                                                    getGrowthIcon(data.revenue.growth),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: [\n                                                            Math.abs(data.revenue.growth),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: [\n                                            \"₹\",\n                                            data.revenue.total.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Total Revenue\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-600/10 border border-green-600/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-8 h-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: data.projects.completed\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Completed Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-600/10 border border-purple-600/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-8 h-8 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-purple-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        data.clients.retention,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: data.clients.active\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Active Clients\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-600/10 border border-orange-600/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-8 h-8 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-orange-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        data.performance.efficiency,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: data.performance.avgCompletionTime\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Avg. Days to Complete\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"revenue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-600/10 border border-green-600/20 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-foreground\",\n                                                        children: \"Monthly Revenue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-foreground\",\n                                                children: [\n                                                    \"₹\",\n                                                    data.revenue.monthly.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm flex items-center space-x-1 \".concat(getGrowthColor(data.revenue.growth)),\n                                                children: [\n                                                    getGrowthIcon(data.revenue.growth),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            Math.abs(data.revenue.growth),\n                                                            \"% vs last \",\n                                                            timeRange\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-primary/10 border border-primary/20 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-foreground\",\n                                                        children: \"Avg. Project Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-foreground\",\n                                                children: [\n                                                    \"₹\",\n                                                    data.performance.avgProjectValue.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Per project average\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-600/10 border border-purple-600/20 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-foreground\",\n                                                        children: \"Revenue Sources\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"Real Estate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: \"65%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"Events\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: \"25%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"Commercial\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: \"10%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted/50 border border-border rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-12 h-12 text-muted-foreground mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-foreground mb-2\",\n                                        children: \"Revenue Trends\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Interactive revenue chart would be displayed here\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"projects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border border-border rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-foreground mb-4\",\n                                                children: \"Project Status Distribution\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Completed\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: data.projects.completed\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-blue-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Active\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: data.projects.active\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-gray-400 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Total\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: data.projects.total\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border border-border rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-foreground mb-4\",\n                                                children: \"Performance Metrics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Completion Rate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: [\n                                                                    data.projects.completionRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Avg. Completion Time\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: [\n                                                                    data.performance.avgCompletionTime,\n                                                                    \" days\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Efficiency Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-primary\",\n                                                                children: [\n                                                                    data.performance.efficiency,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted/50 border border-border rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-12 h-12 text-muted-foreground mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-foreground mb-2\",\n                                        children: \"Project Timeline\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Project timeline and milestones would be displayed here\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"performance\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 border border-border rounded-lg bg-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    data.performance.efficiency,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Overall Efficiency\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 border border-border rounded-lg bg-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    data.clients.retention,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Client Retention\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 border border-border rounded-lg bg-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: data.performance.avgCompletionTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Avg. Days to Complete\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-4 border border-border rounded-lg bg-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    \"₹\",\n                                                    data.performance.avgProjectValue.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Avg. Project Value\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary/10 border border-primary/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-primary mb-3\",\n                                        children: \"Performance Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-sm text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Project completion rate is above industry average\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Revenue growth trending upward this \",\n                                                            timeRange\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_CheckCircle_Clock_DollarSign_PieChart_Target_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Client retention rate indicates strong satisfaction\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/dashboard-analytics.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardAnalytics, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = DashboardAnalytics;\nvar _c;\n$RefreshReg$(_c, \"DashboardAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/dashboard-analytics.tsx\n"));

/***/ })

}]);