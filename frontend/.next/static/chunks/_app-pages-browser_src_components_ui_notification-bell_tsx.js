"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_ui_notification-bell_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/bell.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Bell; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10.268 21a2 2 0 0 0 3.464 0\",\n            key: \"vwvbt9\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326\",\n            key: \"11g9vi\"\n        }\n    ]\n];\nconst Bell = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"bell\", __iconNode);\n //# sourceMappingURL=bell.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/notification-bell.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/notification-bell.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBell: function() { return /* binding */ NotificationBell; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bell!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _hooks_useNotifications__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useNotifications */ \"(app-pages-browser)/./src/hooks/useNotifications.ts\");\n/* __next_internal_client_entry_do_not_use__ NotificationBell auto */ \nvar _s = $RefreshSig$();\n\n\nfunction NotificationBell() {\n    _s();\n    const { stats, loading, error } = (0,_hooks_useNotifications__WEBPACK_IMPORTED_MODULE_1__.useNotifications)({\n        read: false,\n        limit: 5\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative p-2 rounded-full hover:bg-muted transition-colors\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative p-2 rounded-full hover:bg-muted transition-colors\",\n            title: error,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    const unreadCount = (stats === null || stats === void 0 ? void 0 : stats.unread) || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative p-2 rounded-full hover:bg-muted transition-colors cursor-pointer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                children: unreadCount > 99 ? \"99+\" : unreadCount\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/cymatics/frontend/src/components/ui/notification-bell.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"SXx0sKgu09RQWLxrxebzlyuwa3I=\", false, function() {\n    return [\n        _hooks_useNotifications__WEBPACK_IMPORTED_MODULE_1__.useNotifications\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL25vdGlmaWNhdGlvbi1iZWxsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFHdUU7QUFHWjtBQU1wRCxTQUFTRTs7SUFDZCxNQUFNLEVBQUVDLEtBQUssRUFBRUMsT0FBTyxFQUFFQyxLQUFLLEVBQUUsR0FBR0oseUVBQWdCQSxDQUFDO1FBQUVLLE1BQU07UUFBT0MsT0FBTztJQUFFO0lBRTNFLElBQUlILFNBQVM7UUFDWCxxQkFDRSw4REFBQ0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ1QsZ0ZBQUlBO2dCQUFDUyxXQUFVOzs7Ozs7Ozs7OztJQUd0QjtJQUVBLElBQUlKLE9BQU87UUFDVCxxQkFDRSw4REFBQ0c7WUFBSUMsV0FBVTtZQUE2REMsT0FBT0w7c0JBQ2pGLDRFQUFDTCxnRkFBSUE7Z0JBQUNTLFdBQVU7Ozs7Ozs7Ozs7O0lBR3RCO0lBRUEsTUFBTUUsY0FBY1IsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPUyxNQUFNLEtBQUk7SUFFckMscUJBQ0UsOERBQUNKO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDVCxnRkFBSUE7Z0JBQUNTLFdBQVU7Ozs7OztZQUNmRSxjQUFjLG1CQUNiLDhEQUFDRTtnQkFBS0osV0FBVTswQkFDYkUsY0FBYyxLQUFLLFFBQVFBOzs7Ozs7Ozs7Ozs7QUFLdEM7R0EvQmdCVDs7UUFDb0JELHFFQUFnQkE7OztLQURwQ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvbm90aWZpY2F0aW9uLWJlbGwudHN4P2Y5ZjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQmVsbCwgQ2hlY2ssIFgsIEZpbHRlciwgRG93bmxvYWQsIFRyYXNoMiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSdcbmltcG9ydCB7IHVzZU5vdGlmaWNhdGlvbnMgfSBmcm9tICdAL2hvb2tzL3VzZU5vdGlmaWNhdGlvbnMnXG5pbXBvcnQgdHlwZSB7IE5vdGlmaWNhdGlvbkZpbHRlcnMgfSBmcm9tICdAL3R5cGVzL25vdGlmaWNhdGlvbnMnXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcbmltcG9ydCB7IGZvcm1hdERpc3RhbmNlVG9Ob3cgfSBmcm9tICdkYXRlLWZucydcbmltcG9ydCB0eXBlIHsgTm90aWZpY2F0aW9uIH0gZnJvbSAnQC90eXBlcy9ub3RpZmljYXRpb25zJ1xuXG5leHBvcnQgZnVuY3Rpb24gTm90aWZpY2F0aW9uQmVsbCgpIHtcbiAgY29uc3QgeyBzdGF0cywgbG9hZGluZywgZXJyb3IgfSA9IHVzZU5vdGlmaWNhdGlvbnMoeyByZWFkOiBmYWxzZSwgbGltaXQ6IDUgfSlcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctbXV0ZWQgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgPEJlbGwgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgaWYgKGVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgcC0yIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy1tdXRlZCB0cmFuc2l0aW9uLWNvbG9yc1wiIHRpdGxlPXtlcnJvcn0+XG4gICAgICAgIDxCZWxsIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGNvbnN0IHVucmVhZENvdW50ID0gc3RhdHM/LnVucmVhZCB8fCAwXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctbXV0ZWQgdHJhbnNpdGlvbi1jb2xvcnMgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgIDxCZWxsIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgIHt1bnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcm91bmRlZC1mdWxsIGgtNSB3LTUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICB7dW5yZWFkQ291bnQgPiA5OSA/ICc5OSsnIDogdW5yZWFkQ291bnR9XG4gICAgICAgIDwvc3Bhbj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJCZWxsIiwidXNlTm90aWZpY2F0aW9ucyIsIk5vdGlmaWNhdGlvbkJlbGwiLCJzdGF0cyIsImxvYWRpbmciLCJlcnJvciIsInJlYWQiLCJsaW1pdCIsImRpdiIsImNsYXNzTmFtZSIsInRpdGxlIiwidW5yZWFkQ291bnQiLCJ1bnJlYWQiLCJzcGFuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/notification-bell.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useNotifications.ts":
/*!***************************************!*\
  !*** ./src/hooks/useNotifications.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNotifications: function() { return /* binding */ useNotifications; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useNotifications(filters) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log(\"\\uD83D\\uDD14 useNotifications hook called with filters:\", filters);\n    // Fetch notifications and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD14 useNotifications effect running\");\n        const fetchNotifications = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Provide fallback data instead of making API calls\n                console.warn(\"\\uD83D\\uDD14 Notifications API disabled, using empty data\");\n                setNotifications([]);\n                setStats({\n                    total: 0,\n                    unread: 0,\n                    by_type: {\n                        payment: 0,\n                        schedule: 0,\n                        task: 0,\n                        info: 0,\n                        success: 0,\n                        warning: 0,\n                        error: 0,\n                        project: 0,\n                        system: 0\n                    },\n                    by_category: {\n                        task_assigned: 0,\n                        task_due: 0,\n                        task_overdue: 0,\n                        task_completed: 0,\n                        project_created: 0,\n                        project_updated: 0,\n                        schedule_upcoming: 0,\n                        schedule_changed: 0,\n                        payment_received: 0,\n                        payment_overdue: 0,\n                        system_update: 0,\n                        user_mention: 0,\n                        deadline_reminder: 0,\n                        general: 0\n                    },\n                    by_priority: {\n                        low: 0,\n                        medium: 0,\n                        high: 0,\n                        urgent: 0\n                    }\n                });\n            } catch (err) {\n                console.error(\"Error in useNotifications:\", err);\n                setError(err instanceof Error ? err.message : \"Failed to fetch notifications\");\n                // Ensure we still set empty data even if there's an error\n                setNotifications([]);\n                setStats({\n                    total: 0,\n                    unread: 0,\n                    by_type: {\n                        payment: 0,\n                        schedule: 0,\n                        task: 0,\n                        info: 0,\n                        success: 0,\n                        warning: 0,\n                        error: 0,\n                        project: 0,\n                        system: 0\n                    },\n                    by_category: {\n                        task_assigned: 0,\n                        task_due: 0,\n                        task_overdue: 0,\n                        task_completed: 0,\n                        project_created: 0,\n                        project_updated: 0,\n                        schedule_upcoming: 0,\n                        schedule_changed: 0,\n                        payment_received: 0,\n                        payment_overdue: 0,\n                        system_update: 0,\n                        user_mention: 0,\n                        deadline_reminder: 0,\n                        general: 0\n                    },\n                    by_priority: {\n                        low: 0,\n                        medium: 0,\n                        high: 0,\n                        urgent: 0\n                    }\n                });\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchNotifications();\n    }, [\n        filters === null || filters === void 0 ? void 0 : filters.read,\n        filters === null || filters === void 0 ? void 0 : filters.type,\n        filters === null || filters === void 0 ? void 0 : filters.category,\n        filters === null || filters === void 0 ? void 0 : filters.priority,\n        filters === null || filters === void 0 ? void 0 : filters.limit,\n        filters === null || filters === void 0 ? void 0 : filters.offset\n    ]);\n    // Return minimal interface\n    return {\n        notifications,\n        stats,\n        loading,\n        error,\n        refetch: ()=>Promise.resolve(),\n        markAsRead: ()=>Promise.resolve(),\n        markAsUnread: ()=>Promise.resolve(),\n        markAllAsRead: ()=>Promise.resolve(),\n        deleteNotification: ()=>Promise.resolve(),\n        createNotification: ()=>Promise.resolve({})\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts\n"));

/***/ })

}]);