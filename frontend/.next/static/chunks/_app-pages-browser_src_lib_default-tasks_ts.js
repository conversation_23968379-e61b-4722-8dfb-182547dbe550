"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_default-tasks_ts"],{

/***/ "(app-pages-browser)/./src/lib/default-tasks.ts":
/*!**********************************!*\
  !*** ./src/lib/default-tasks.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TASK_TEMPLATES: function() { return /* binding */ DEFAULT_TASK_TEMPLATES; },\n/* harmony export */   createTasksFromTemplates: function() { return /* binding */ createTasksFromTemplates; },\n/* harmony export */   getClientTypesWithDefaultTasks: function() { return /* binding */ getClientTypesWithDefaultTasks; },\n/* harmony export */   getDefaultTasksForClientType: function() { return /* binding */ getDefaultTasksForClientType; },\n/* harmony export */   getDefaultTasksForScheduleType: function() { return /* binding */ getDefaultTasksForScheduleType; }\n/* harmony export */ });\n// Default task templates for each schedule type (based on requirements/tasks.md)\nconst DEFAULT_TASK_TEMPLATES = {\n    // Wedding, Movie, Surveillance, Events, News, Collaboration\n    Wedding: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the wedding shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Movie: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the movie shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Surveillance: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the surveillance shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Event: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the event shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    News: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the news shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    Collaboration: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the collaboration shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Corporate, Real Estate (Script Confirmation + Shoot + File Upload + File Backup + Edit)\n    Corporate: [\n        {\n            title: \"Script Confirmation\",\n            description: \"Confirm script and requirements with client\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Shoot\",\n            description: \"Conduct the corporate shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Edit\",\n            description: \"Edit and post-process the footage\",\n            assigned_role: \"editor\",\n            priority: \"high\",\n            order: 5,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    \"Real Estate\": [\n        {\n            title: \"Script Confirmation\",\n            description: \"Confirm property details and requirements\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Shoot\",\n            description: \"Conduct the real estate shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Edit\",\n            description: \"Edit and enhance the property footage\",\n            assigned_role: \"editor\",\n            priority: \"high\",\n            order: 5,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Government, NGO (Shoot + File Upload + File Backup + Edit)\n    Govt: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the government project shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Edit\",\n            description: \"Edit and process the footage\",\n            assigned_role: \"editor\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    NGO: [\n        {\n            title: \"Shoot\",\n            description: \"Conduct the NGO project shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured files to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 2,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all files\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 3,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Edit\",\n            description: \"Edit and process the footage\",\n            assigned_role: \"editor\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver final files to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ],\n    // Survey (Plan Flight + Mark GCPs + Shoot + File Upload + File Backup + Post-Processing)\n    Survey: [\n        {\n            title: \"Plan Flight\",\n            description: \"Plan the survey flight path and parameters\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Mark GCPs\",\n            description: \"Mark Ground Control Points for survey accuracy\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 2,\n            isProjectTask: false\n        },\n        {\n            title: \"Shoot\",\n            description: \"Conduct the survey shoot\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 3,\n            isProjectTask: false\n        },\n        {\n            title: \"File Upload\",\n            description: \"Upload captured survey data to storage\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 4,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"File Backup\",\n            description: \"Create backup of all survey data\",\n            assigned_role: \"pilot\",\n            priority: \"medium\",\n            order: 5,\n            dueDaysAfterShoot: 1,\n            isProjectTask: false\n        },\n        {\n            title: \"Post-Processing\",\n            description: \"Process survey data and generate maps/models\",\n            assigned_role: \"pilot\",\n            priority: \"high\",\n            order: 6,\n            dueDaysAfterShoot: 7,\n            isProjectTask: false\n        },\n        // Project-level tasks (created once per project)\n        {\n            title: \"Deliver Files\",\n            description: \"Deliver processed survey results to client\",\n            assigned_role: \"admin\",\n            priority: \"medium\",\n            order: 100,\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        },\n        {\n            title: \"Payment Collect\",\n            description: \"Collect payment from client\",\n            assigned_role: \"admin\",\n            priority: \"high\",\n            order: 101,\n            dueDaysAfterTask: \"Deliver Files\",\n            dueDaysAfterShoot: 7,\n            isProjectTask: true\n        }\n    ]\n};\n/**\n * Get default tasks for a specific client type (for project-level tasks)\n */ function getDefaultTasksForClientType(clientType) {\n    return DEFAULT_TASK_TEMPLATES[clientType] || [];\n}\n/**\n * Get default tasks for a specific schedule type (for schedule-based tasks)\n */ function getDefaultTasksForScheduleType(scheduleType) {\n    return DEFAULT_TASK_TEMPLATES[scheduleType] || [];\n}\n/**\n * Create task forms from templates for a specific project\n */ function createTasksFromTemplates(templates, projectId, usersByRole, shootDate, shootId // Optional shoot ID for shoot-based tasks\n) {\n    const sortedTemplates = templates.sort((a, b)=>a.order - b.order);\n    return sortedTemplates.map((template)=>{\n        let dueDate;\n        // Special case: Shoot task should have due date as the schedule start time\n        if (shootDate && template.title === \"Shoot\") {\n            dueDate = shootDate // Use the full schedule date/time\n            ;\n        } else if (shootDate && template.dueDaysAfterShoot) {\n            const shootDateTime = new Date(shootDate);\n            const dueDateObj = new Date(shootDateTime);\n            dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot);\n            dueDate = dueDateObj.toISOString().split(\"T\")[0] // Format as YYYY-MM-DD\n            ;\n        }\n        // Handle tasks that depend on other tasks (like Payment Collect after Deliver Files)\n        if (shootDate && template.dueDaysAfterTask && template.dueDaysAfterShoot) {\n            const shootDateTime = new Date(shootDate);\n            const dueDateObj = new Date(shootDateTime);\n            dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot);\n            dueDate = dueDateObj.toISOString().split(\"T\")[0];\n        }\n        return {\n            title: template.title,\n            description: template.description,\n            status: \"pending\",\n            priority: template.priority,\n            assigned_to: usersByRole[template.assigned_role] || \"\",\n            assigned_role: template.assigned_role,\n            project_id: projectId,\n            shoot_id: template.isProjectTask ? undefined : shootId,\n            due_date: dueDate,\n            order: template.order\n        };\n    });\n}\n/**\n * Get all available client types that have default tasks\n */ function getClientTypesWithDefaultTasks() {\n    return Object.keys(DEFAULT_TASK_TEMPLATES);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/default-tasks.ts\n"));

/***/ })

}]);