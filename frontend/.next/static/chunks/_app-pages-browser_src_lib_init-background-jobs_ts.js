"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_init-background-jobs_ts"],{

/***/ "(app-pages-browser)/./src/lib/background-jobs.ts":
/*!************************************!*\
  !*** ./src/lib/background-jobs.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAllJobs: function() { return /* binding */ getAllJobs; },\n/* harmony export */   getBackgroundJobQueue: function() { return /* binding */ getBackgroundJobQueue; },\n/* harmony export */   getJobStatus: function() { return /* binding */ getJobStatus; },\n/* harmony export */   queueBackgroundJob: function() { return /* binding */ queueBackgroundJob; },\n/* harmony export */   queueSharePointFolderCreation: function() { return /* binding */ queueSharePointFolderCreation; }\n/* harmony export */ });\n/**\n * Background job system for handling asynchronous tasks like SharePoint folder creation\n */ class BackgroundJobQueue {\n    /**\n   * Add a new job to the queue\n   */ addJob(type, entityType, entityId, payload) {\n        const jobId = \"\".concat(type, \"_\").concat(entityType, \"_\").concat(entityId, \"_\").concat(Date.now());\n        const job = {\n            id: jobId,\n            type,\n            entityType,\n            entityId,\n            payload,\n            status: \"pending\",\n            attempts: 0,\n            maxAttempts: 3,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        this.jobs.set(jobId, job);\n        console.log(\"\\uD83D\\uDCCB Background job added: \".concat(jobId, \" (\").concat(type, \" for \").concat(entityType, \":\").concat(entityId, \")\"));\n        console.log(\"\\uD83D\\uDCCA Total jobs in queue: \".concat(this.jobs.size));\n        return jobId;\n    }\n    /**\n   * Start processing jobs in the background\n   */ startProcessing() {\n        if (this.processingInterval) return;\n        console.log(\"\\uD83D\\uDE80 Starting background job processing...\");\n        // Re-enable background job processing with optimized interval\n        this.processingInterval = setInterval(async ()=>{\n            if (this.isProcessing) return;\n            await this.processNextJob();\n        }, 10000) // Process every 10 seconds to reduce CPU load\n        ;\n    }\n    /**\n   * Stop processing jobs\n   */ stopProcessing() {\n        if (this.processingInterval) {\n            clearInterval(this.processingInterval);\n            this.processingInterval = null;\n        }\n    }\n    /**\n   * Process the next pending job\n   */ async processNextJob() {\n        const pendingJob = Array.from(this.jobs.values()).find((job)=>job.status === \"pending\" && job.attempts < job.maxAttempts);\n        if (!pendingJob) return;\n        this.isProcessing = true;\n        pendingJob.status = \"processing\";\n        pendingJob.attempts++;\n        pendingJob.updatedAt = new Date();\n        console.log(\"\\uD83D\\uDD04 Processing background job: \".concat(pendingJob.id, \" (attempt \").concat(pendingJob.attempts, \"/\").concat(pendingJob.maxAttempts, \")\"));\n        try {\n            await this.executeJob(pendingJob);\n            pendingJob.status = \"completed\";\n            pendingJob.updatedAt = new Date();\n            console.log(\"✅ Background job completed: \".concat(pendingJob.id));\n        } catch (error) {\n            console.error(\"❌ Background job failed: \".concat(pendingJob.id), error);\n            pendingJob.error = error instanceof Error ? error.message : \"Unknown error\";\n            pendingJob.updatedAt = new Date();\n            if (pendingJob.attempts >= pendingJob.maxAttempts) {\n                pendingJob.status = \"failed\";\n                console.error(\"\\uD83D\\uDC80 Background job permanently failed after \".concat(pendingJob.maxAttempts, \" attempts: \").concat(pendingJob.id));\n            } else {\n                pendingJob.status = \"pending\" // Retry\n                ;\n                console.log(\"\\uD83D\\uDD04 Background job will retry: \".concat(pendingJob.id));\n            }\n        } finally{\n            this.isProcessing = false;\n        }\n    }\n    /**\n   * Execute a specific job based on its type\n   */ async executeJob(job) {\n        switch(job.type){\n            case \"sharepoint_folder_creation\":\n                await this.executeSharePointFolderCreation(job);\n                break;\n            case \"heatmap_automation\":\n                await this.executeHeatmapAutomation(job);\n                break;\n            case \"notification_triggers\":\n                await this.executeNotificationTriggers(job);\n                break;\n            default:\n                throw new Error(\"Unknown job type: \".concat(job.type));\n        }\n    }\n    /**\n   * Execute SharePoint folder creation job\n   */ async executeSharePointFolderCreation(job) {\n        const { entityType, entityId, payload } = job;\n        switch(entityType){\n            case \"client\":\n                await this.createClientFolder(entityId, payload);\n                break;\n            case \"project\":\n                await this.createProjectFolder(entityId, payload);\n                break;\n            case \"schedule\":\n                await this.createScheduleFolder(entityId, payload);\n                break;\n            default:\n                throw new Error(\"Unknown entity type: \".concat(entityType));\n        }\n    }\n    /**\n   * Create SharePoint folder for client\n   */ async createClientFolder(clientId, payload) {\n        const { createClientFolder } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(app-pages-browser)/./src/lib/microsoft-graph.ts\"));\n        await createClientFolder(clientId, payload.customId, payload.name);\n    }\n    /**\n   * Create SharePoint folder for project\n   */ async createProjectFolder(projectId, payload) {\n        const { createProjectFolder } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(app-pages-browser)/./src/lib/microsoft-graph.ts\"));\n        await createProjectFolder(projectId, payload.customId, payload.name, payload.clientId);\n    }\n    /**\n   * Create SharePoint folder for schedule\n   */ async createScheduleFolder(scheduleId, payload) {\n        console.log(\"⚡️ Dynamically importing SharePointService for schedule: \".concat(scheduleId));\n        const { SharePointService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_sharepoint-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/sharepoint-service */ \"(app-pages-browser)/./src/lib/sharepoint-service.ts\"));\n        console.log(\"\\uD83D\\uDCDE Calling SharePointService.ensureScheduleFolder for schedule: \".concat(scheduleId));\n        const result = await SharePointService.ensureScheduleFolder(scheduleId);\n        console.log(\"✔️ SharePointService.ensureScheduleFolder result for schedule \".concat(scheduleId, \":\"), result);\n    }\n    /**\n   * Execute heatmap automation job\n   */ async executeHeatmapAutomation(job) {\n        const { entityId } = job;\n        console.log(\"\\uD83D\\uDDFA️ Executing heatmap automation for schedule: \".concat(entityId));\n        const { processHeatmapAutomation } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_heatmap-automation_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/heatmap-automation */ \"(app-pages-browser)/./src/lib/heatmap-automation.ts\"));\n        const result = await processHeatmapAutomation(entityId);\n        if (!result.success) {\n            throw new Error(result.message);\n        }\n        console.log(\"✅ Heatmap automation completed for schedule: \".concat(entityId));\n    }\n    /**\n   * Execute notification triggers job\n   */ async executeNotificationTriggers(job) {\n        const { payload } = job;\n        console.log(\"\\uD83D\\uDD14 Executing notification triggers: \".concat(payload.triggerType));\n        const { processNotificationTriggers } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_notification-triggers_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/notification-triggers */ \"(app-pages-browser)/./src/lib/notification-triggers.ts\"));\n        const result = await processNotificationTriggers(payload.triggerType, payload.options);\n        if (!result.success) {\n            throw new Error(result.message);\n        }\n        console.log(\"✅ Notification triggers completed: \".concat(payload.triggerType, \", created \").concat(result.count, \" notifications\"));\n    }\n    /**\n   * Get job status\n   */ getJobStatus(jobId) {\n        return this.jobs.get(jobId);\n    }\n    /**\n   * Get all jobs for debugging\n   */ getAllJobs() {\n        return Array.from(this.jobs.values());\n    }\n    /**\n   * Clean up completed and failed jobs older than 1 hour\n   */ cleanup() {\n        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);\n        for (const [jobId, job] of this.jobs.entries()){\n            if ((job.status === \"completed\" || job.status === \"failed\") && job.updatedAt < oneHourAgo) {\n                this.jobs.delete(jobId);\n                console.log(\"\\uD83E\\uDDF9 Cleaned up old job: \".concat(jobId));\n            }\n        }\n    }\n    constructor(){\n        this.jobs = new Map();\n        this.isProcessing = false;\n        this.processingInterval = null;\n        console.log(\"\\uD83C\\uDFD7️ BackgroundJobQueue constructor called\");\n        // Start processing jobs every 2 seconds\n        this.startProcessing();\n        console.log(\"✅ BackgroundJobQueue initialized and processing started\");\n    }\n}\n// Global instance\nlet backgroundJobQueue = null;\n/**\n * Get the global background job queue instance\n */ function getBackgroundJobQueue() {\n    if (!backgroundJobQueue) {\n        console.log(\"\\uD83D\\uDE80 Creating new BackgroundJobQueue instance\");\n        backgroundJobQueue = new BackgroundJobQueue();\n        // Clean up old jobs every 30 minutes\n        setInterval(()=>{\n            backgroundJobQueue === null || backgroundJobQueue === void 0 ? void 0 : backgroundJobQueue.cleanup();\n        }, 30 * 60 * 1000);\n        console.log(\"\\uD83C\\uDFAF BackgroundJobQueue instance created and cleanup scheduled\");\n    }\n    return backgroundJobQueue;\n}\n/**\n * Add a SharePoint folder creation job to the background queue\n * @param entityType - Type of entity (client, project, schedule)\n * @param entityId - ID of the entity\n * @param payload - Job payload\n * @param options - Additional options\n * @returns Job ID or 'sync' if executed synchronously\n */ async function queueSharePointFolderCreation(entityType, entityId, payload) {\n    let options = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n    const { executeSync = false, fallbackToSync = true } = options;\n    // If sync execution is requested, execute immediately\n    if (executeSync) {\n        console.log(\"\\uD83D\\uDD04 Executing SharePoint folder creation synchronously for \".concat(entityType, \":\").concat(entityId));\n        try {\n            await executeSharePointFolderCreationDirect(entityType, entityId, payload);\n            console.log(\"✅ Synchronous SharePoint folder creation completed for \".concat(entityType, \":\").concat(entityId));\n            return \"sync\";\n        } catch (error) {\n            console.error(\"❌ Synchronous SharePoint folder creation failed for \".concat(entityType, \":\").concat(entityId, \":\"), error);\n            throw error;\n        }\n    }\n    // Try to queue the job\n    try {\n        const queue = getBackgroundJobQueue();\n        const jobId = queue.addJob(\"sharepoint_folder_creation\", entityType, entityId, payload);\n        console.log(\"\\uD83D\\uDCCB SharePoint folder creation queued for \".concat(entityType, \":\").concat(entityId, \" with job ID: \").concat(jobId));\n        return jobId;\n    } catch (queueError) {\n        console.error(\"❌ Failed to queue SharePoint folder creation for \".concat(entityType, \":\").concat(entityId, \":\"), queueError);\n        // Fall back to synchronous execution if queuing fails and fallback is enabled\n        if (fallbackToSync) {\n            console.log(\"\\uD83D\\uDD04 Falling back to synchronous execution for \".concat(entityType, \":\").concat(entityId));\n            try {\n                await executeSharePointFolderCreationDirect(entityType, entityId, payload);\n                console.log(\"✅ Fallback synchronous SharePoint folder creation completed for \".concat(entityType, \":\").concat(entityId));\n                return \"sync-fallback\";\n            } catch (syncError) {\n                console.error(\"❌ Fallback synchronous SharePoint folder creation failed for \".concat(entityType, \":\").concat(entityId, \":\"), syncError);\n                throw syncError;\n            }\n        }\n        throw queueError;\n    }\n}\n/**\n * Execute SharePoint folder creation directly (synchronously)\n */ async function executeSharePointFolderCreationDirect(entityType, entityId, payload) {\n    switch(entityType){\n        case \"client\":\n            const { createClientFolder } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(app-pages-browser)/./src/lib/microsoft-graph.ts\"));\n            await createClientFolder(entityId, payload.customId, payload.name);\n            break;\n        case \"project\":\n            const { createProjectFolder } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_microsoft-graph_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/microsoft-graph */ \"(app-pages-browser)/./src/lib/microsoft-graph.ts\"));\n            await createProjectFolder(entityId, payload.customId, payload.name, payload.clientId);\n            break;\n        case \"schedule\":\n            const { SharePointService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_sharepoint-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/sharepoint-service */ \"(app-pages-browser)/./src/lib/sharepoint-service.ts\"));\n            await SharePointService.ensureScheduleFolder(entityId);\n            break;\n        default:\n            throw new Error(\"Unknown entity type: \".concat(entityType));\n    }\n}\n/**\n * Get the status of a background job\n */ function getJobStatus(jobId) {\n    const queue = getBackgroundJobQueue();\n    return queue.getJobStatus(jobId);\n}\n/**\n * Get all background jobs (for debugging)\n */ function getAllJobs() {\n    const queue = getBackgroundJobQueue();\n    return queue.getAllJobs();\n}\n/**\n * Queue a heatmap automation job for a schedule\n * @param scheduleId - ID of the schedule to process\n * @returns Job ID\n */ function queueBackgroundJob(type, entityType, entityId, payload) {\n    const queue = getBackgroundJobQueue();\n    const jobId = queue.addJob(type, entityType, entityId, payload);\n    console.log(\"\\uD83D\\uDCCB Heatmap automation queued for schedule:\".concat(entityId, \" with job ID: \").concat(jobId));\n    return jobId;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/background-jobs.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/init-background-jobs.ts":
/*!*****************************************!*\
  !*** ./src/lib/init-background-jobs.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupBackgroundJobs: function() { return /* binding */ cleanupBackgroundJobs; },\n/* harmony export */   initializeBackgroundJobs: function() { return /* binding */ initializeBackgroundJobs; }\n/* harmony export */ });\n/* harmony import */ var _background_jobs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./background-jobs */ \"(app-pages-browser)/./src/lib/background-jobs.ts\");\n/**\n * Background Jobs Initialization\n * \n * This module initializes the background job queue when the application starts.\n * The queue is automatically started when the first instance is created.\n */ \nlet isInitialized = false;\n/**\n * Initialize the background job queue\n * This should be called once when the application starts\n */ function initializeBackgroundJobs() {\n    if (isInitialized) {\n        console.log(\"Background job queue already initialized\");\n        return;\n    }\n    try {\n        // Get the background job queue instance (this starts it automatically)\n        const queue = (0,_background_jobs__WEBPACK_IMPORTED_MODULE_0__.getBackgroundJobQueue)();\n        isInitialized = true;\n        console.log(\"✅ Background job queue initialized successfully\");\n        return queue;\n    } catch (error) {\n        console.error(\"❌ Failed to initialize background job queue:\", error);\n    }\n}\n/**\n * Cleanup function to stop the background job queue\n * This should be called when the application shuts down\n */ function cleanupBackgroundJobs() {\n    if (!isInitialized) {\n        return;\n    }\n    try {\n        const queue = (0,_background_jobs__WEBPACK_IMPORTED_MODULE_0__.getBackgroundJobQueue)();\n        queue.stopProcessing();\n        isInitialized = false;\n        console.log(\"✅ Background job queue stopped successfully\");\n    } catch (error) {\n        console.error(\"❌ Failed to stop background job queue:\", error);\n    }\n}\n// Auto-initialize in server environments\nif (false) {}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvaW5pdC1iYWNrZ3JvdW5kLWpvYnMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFd0Q7QUFFekQsSUFBSUMsZ0JBQWdCO0FBRXBCOzs7Q0FHQyxHQUNNLFNBQVNDO0lBQ2QsSUFBSUQsZUFBZTtRQUNqQkUsUUFBUUMsR0FBRyxDQUFDO1FBQ1o7SUFDRjtJQUVBLElBQUk7UUFDRix1RUFBdUU7UUFDdkUsTUFBTUMsUUFBUUwsdUVBQXFCQTtRQUNuQ0MsZ0JBQWdCO1FBQ2hCRSxRQUFRQyxHQUFHLENBQUM7UUFDWixPQUFPQztJQUNULEVBQUUsT0FBT0MsT0FBTztRQUNkSCxRQUFRRyxLQUFLLENBQUMsZ0RBQWdEQTtJQUNoRTtBQUNGO0FBRUE7OztDQUdDLEdBQ00sU0FBU0M7SUFDZCxJQUFJLENBQUNOLGVBQWU7UUFDbEI7SUFDRjtJQUVBLElBQUk7UUFDRixNQUFNSSxRQUFRTCx1RUFBcUJBO1FBQ25DSyxNQUFNRyxjQUFjO1FBQ3BCUCxnQkFBZ0I7UUFDaEJFLFFBQVFDLEdBQUcsQ0FBQztJQUNkLEVBQUUsT0FBT0UsT0FBTztRQUNkSCxRQUFRRyxLQUFLLENBQUMsMENBQTBDQTtJQUMxRDtBQUNGO0FBRUEseUNBQXlDO0FBQ3pDLElBQUksS0FBa0IsRUFBYSxFQUdsQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGliL2luaXQtYmFja2dyb3VuZC1qb2JzLnRzPzA4MjgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBCYWNrZ3JvdW5kIEpvYnMgSW5pdGlhbGl6YXRpb25cbiAqIFxuICogVGhpcyBtb2R1bGUgaW5pdGlhbGl6ZXMgdGhlIGJhY2tncm91bmQgam9iIHF1ZXVlIHdoZW4gdGhlIGFwcGxpY2F0aW9uIHN0YXJ0cy5cbiAqIFRoZSBxdWV1ZSBpcyBhdXRvbWF0aWNhbGx5IHN0YXJ0ZWQgd2hlbiB0aGUgZmlyc3QgaW5zdGFuY2UgaXMgY3JlYXRlZC5cbiAqL1xuXG5pbXBvcnQgeyBnZXRCYWNrZ3JvdW5kSm9iUXVldWUgfSBmcm9tICcuL2JhY2tncm91bmQtam9icydcblxubGV0IGlzSW5pdGlhbGl6ZWQgPSBmYWxzZVxuXG4vKipcbiAqIEluaXRpYWxpemUgdGhlIGJhY2tncm91bmQgam9iIHF1ZXVlXG4gKiBUaGlzIHNob3VsZCBiZSBjYWxsZWQgb25jZSB3aGVuIHRoZSBhcHBsaWNhdGlvbiBzdGFydHNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGluaXRpYWxpemVCYWNrZ3JvdW5kSm9icygpIHtcbiAgaWYgKGlzSW5pdGlhbGl6ZWQpIHtcbiAgICBjb25zb2xlLmxvZygnQmFja2dyb3VuZCBqb2IgcXVldWUgYWxyZWFkeSBpbml0aWFsaXplZCcpXG4gICAgcmV0dXJuXG4gIH1cblxuICB0cnkge1xuICAgIC8vIEdldCB0aGUgYmFja2dyb3VuZCBqb2IgcXVldWUgaW5zdGFuY2UgKHRoaXMgc3RhcnRzIGl0IGF1dG9tYXRpY2FsbHkpXG4gICAgY29uc3QgcXVldWUgPSBnZXRCYWNrZ3JvdW5kSm9iUXVldWUoKVxuICAgIGlzSW5pdGlhbGl6ZWQgPSB0cnVlXG4gICAgY29uc29sZS5sb2coJ+KchSBCYWNrZ3JvdW5kIGpvYiBxdWV1ZSBpbml0aWFsaXplZCBzdWNjZXNzZnVsbHknKVxuICAgIHJldHVybiBxdWV1ZVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gaW5pdGlhbGl6ZSBiYWNrZ3JvdW5kIGpvYiBxdWV1ZTonLCBlcnJvcilcbiAgfVxufVxuXG4vKipcbiAqIENsZWFudXAgZnVuY3Rpb24gdG8gc3RvcCB0aGUgYmFja2dyb3VuZCBqb2IgcXVldWVcbiAqIFRoaXMgc2hvdWxkIGJlIGNhbGxlZCB3aGVuIHRoZSBhcHBsaWNhdGlvbiBzaHV0cyBkb3duXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjbGVhbnVwQmFja2dyb3VuZEpvYnMoKSB7XG4gIGlmICghaXNJbml0aWFsaXplZCkge1xuICAgIHJldHVyblxuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBxdWV1ZSA9IGdldEJhY2tncm91bmRKb2JRdWV1ZSgpXG4gICAgcXVldWUuc3RvcFByb2Nlc3NpbmcoKVxuICAgIGlzSW5pdGlhbGl6ZWQgPSBmYWxzZVxuICAgIGNvbnNvbGUubG9nKCfinIUgQmFja2dyb3VuZCBqb2IgcXVldWUgc3RvcHBlZCBzdWNjZXNzZnVsbHknKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gc3RvcCBiYWNrZ3JvdW5kIGpvYiBxdWV1ZTonLCBlcnJvcilcbiAgfVxufVxuXG4vLyBBdXRvLWluaXRpYWxpemUgaW4gc2VydmVyIGVudmlyb25tZW50c1xuaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gIGNvbnNvbGUubG9nKCfwn5qAIFNlcnZlciBlbnZpcm9ubWVudCBkZXRlY3RlZCwgaW5pdGlhbGl6aW5nIGJhY2tncm91bmQgam9icy4uLicpXG4gIGluaXRpYWxpemVCYWNrZ3JvdW5kSm9icygpXG59Il0sIm5hbWVzIjpbImdldEJhY2tncm91bmRKb2JRdWV1ZSIsImlzSW5pdGlhbGl6ZWQiLCJpbml0aWFsaXplQmFja2dyb3VuZEpvYnMiLCJjb25zb2xlIiwibG9nIiwicXVldWUiLCJlcnJvciIsImNsZWFudXBCYWNrZ3JvdW5kSm9icyIsInN0b3BQcm9jZXNzaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/init-background-jobs.ts\n"));

/***/ })

}]);