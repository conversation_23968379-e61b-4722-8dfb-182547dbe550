"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_status-completion_ts"],{

/***/ "(app-pages-browser)/./src/lib/status-completion.ts":
/*!**************************************!*\
  !*** ./src/lib/status-completion.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAndCompleteProject: function() { return /* binding */ checkAndCompleteProject; },\n/* harmony export */   checkAndCompleteShoot: function() { return /* binding */ checkAndCompleteShoot; },\n/* harmony export */   handleTaskStatusChange: function() { return /* binding */ handleTaskStatusChange; }\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n\nconst supabase = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.createClientSupabaseClient)();\n/**\n * Check if all shoot-based tasks for a specific shoot are completed\n * and automatically complete the shoot if they are\n */ async function checkAndCompleteShoot(shootId) {\n    try {\n        console.log(\"Checking shoot completion for shoot:\", shootId);\n        // Get the shoot details\n        const { data: shoot, error: shootError } = await supabase.from(\"shoots\").select(\"id, status, project_id\").eq(\"id\", shootId).single();\n        if (shootError || !shoot) {\n            console.error(\"Error fetching shoot:\", shootError);\n            return;\n        }\n        // Skip if shoot is already completed\n        if (shoot.status === \"completed\") {\n            console.log(\"Shoot is already completed\");\n            return;\n        }\n        // Get all shoot-based tasks for this shoot\n        const { data: shootTasks, error: tasksError } = await supabase.from(\"tasks\").select(\"id, title, status\").eq(\"shoot_id\", shootId).neq(\"status\", \"cancelled\") // Exclude cancelled tasks\n        ;\n        if (tasksError) {\n            console.error(\"Error fetching shoot tasks:\", tasksError);\n            return;\n        }\n        if (!shootTasks || shootTasks.length === 0) {\n            console.log(\"No shoot-based tasks found for shoot:\", shootId);\n            return;\n        }\n        // Check if all shoot-based tasks are completed\n        const allCompleted = shootTasks.every((task)=>task.status === \"completed\");\n        console.log(\"Shoot tasks status:\", shootTasks.map((t)=>({\n                title: t.title,\n                status: t.status\n            })));\n        console.log(\"All shoot tasks completed:\", allCompleted);\n        if (allCompleted) {\n            // Automatically complete the shoot\n            const { error: updateError } = await supabase.from(\"shoots\").update({\n                status: \"completed\",\n                actual_date: new Date().toISOString()\n            }).eq(\"id\", shootId);\n            if (updateError) {\n                console.error(\"Error updating shoot status:\", updateError);\n                return;\n            }\n            console.log(\"Shoot automatically completed:\", shootId);\n            // Now check if the project should be completed\n            await checkAndCompleteProject(shoot.project_id);\n        }\n    } catch (error) {\n        console.error(\"Error in checkAndCompleteShoot:\", error);\n    }\n}\n/**\n * Check if all shoots and project-level tasks for a project are completed\n * and automatically complete the project if they are\n */ async function checkAndCompleteProject(projectId) {\n    try {\n        console.log(\"Checking project completion for project:\", projectId);\n        // Get the project details\n        const { data: project, error: projectError } = await supabase.from(\"projects\").select(\"id, status\").eq(\"id\", projectId).single();\n        if (projectError || !project) {\n            console.error(\"Error fetching project:\", projectError);\n            return;\n        }\n        // Skip if project is already completed\n        if (project.status === \"completed\") {\n            console.log(\"Project is already completed\");\n            return;\n        }\n        // Get all shoots for this project\n        const { data: shoots, error: shootsError } = await supabase.from(\"shoots\").select(\"id, status\").eq(\"project_id\", projectId).neq(\"status\", \"cancelled\") // Exclude cancelled shoots\n        ;\n        if (shootsError) {\n            console.error(\"Error fetching project shoots:\", shootsError);\n            return;\n        }\n        var _shoots_every;\n        // Check if all shoots are completed\n        const allShootsCompleted = (_shoots_every = shoots === null || shoots === void 0 ? void 0 : shoots.every((shoot)=>shoot.status === \"completed\")) !== null && _shoots_every !== void 0 ? _shoots_every : true;\n        console.log(\"Project shoots status:\", shoots === null || shoots === void 0 ? void 0 : shoots.map((s)=>({\n                id: s.id,\n                status: s.status\n            })));\n        console.log(\"All shoots completed:\", allShootsCompleted);\n        if (!allShootsCompleted) {\n            console.log(\"Not all shoots are completed yet\");\n            return;\n        }\n        // Get all project-level tasks (tasks without shoot_id)\n        const { data: projectTasks, error: projectTasksError } = await supabase.from(\"tasks\").select(\"id, title, status\").eq(\"project_id\", projectId).is(\"shoot_id\", null) // Project-level tasks have no shoot_id\n        .neq(\"status\", \"cancelled\") // Exclude cancelled tasks\n        ;\n        if (projectTasksError) {\n            console.error(\"Error fetching project tasks:\", projectTasksError);\n            return;\n        }\n        var _projectTasks_every;\n        // Check if all project-level tasks are completed\n        const allProjectTasksCompleted = (_projectTasks_every = projectTasks === null || projectTasks === void 0 ? void 0 : projectTasks.every((task)=>task.status === \"completed\")) !== null && _projectTasks_every !== void 0 ? _projectTasks_every : true;\n        console.log(\"Project tasks status:\", projectTasks === null || projectTasks === void 0 ? void 0 : projectTasks.map((t)=>({\n                title: t.title,\n                status: t.status\n            })));\n        console.log(\"All project tasks completed:\", allProjectTasksCompleted);\n        if (allShootsCompleted && allProjectTasksCompleted) {\n            // Automatically complete the project\n            const { error: updateError } = await supabase.from(\"projects\").update({\n                status: \"completed\"\n            }).eq(\"id\", projectId);\n            if (updateError) {\n                console.error(\"Error updating project status:\", updateError);\n                return;\n            }\n            console.log(\"Project automatically completed:\", projectId);\n        }\n    } catch (error) {\n        console.error(\"Error in checkAndCompleteProject:\", error);\n    }\n}\n/**\n * Handle task status change and trigger automatic completion checks\n */ async function handleTaskStatusChange(task, newStatus) {\n    try {\n        console.log(\"Handling task status change:\", {\n            taskId: task.id,\n            title: task.title,\n            newStatus\n        });\n        // If task is completed, check for automatic completion\n        if (newStatus === \"completed\") {\n            // If it's a shoot-based task, check shoot completion\n            if (task.shoot_id) {\n                await checkAndCompleteShoot(task.shoot_id);\n            } else if (task.project_id) {\n                await checkAndCompleteProject(task.project_id);\n            }\n        }\n    } catch (error) {\n        console.error(\"Error in handleTaskStatusChange:\", error);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/status-completion.ts\n"));

/***/ })

}]);