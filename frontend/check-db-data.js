const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.log('SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkData() {
  console.log('🔍 Checking database data...\n');

  // Check projects
  console.log('📊 PROJECTS DATA:');
  const { data: projects, error: projectsError } = await supabase
    .from('projects')
    .select('id, name, location, google_maps_link, status')
    .limit(10);

  if (projectsError) {
    console.error('❌ Projects error:', projectsError);
  } else {
    console.log(`Found ${projects.length} projects:`);
    projects.forEach((project, index) => {
      console.log(`  ${index + 1}. ${project.name}`);
      console.log(`     Location: ${project.location || 'NULL'}`);
      console.log(`     Maps Link: ${project.google_maps_link || 'NULL'}`);
      console.log(`     Status: ${project.status}`);
      console.log('');
    });
  }

  // Check schedules
  console.log('\n📅 SCHEDULES DATA:');
  const { data: schedules, error: schedulesError } = await supabase
    .from('schedules')
    .select(`
      id, 
      scheduled_date, 
      location, 
      google_maps_link, 
      status,
      project:projects(name)
    `)
    .limit(10);

  if (schedulesError) {
    console.error('❌ Schedules error:', schedulesError);
  } else {
    console.log(`Found ${schedules.length} schedules:`);
    schedules.forEach((schedule, index) => {
      console.log(`  ${index + 1}. ${schedule.project?.name || 'Unknown Project'} - ${schedule.scheduled_date}`);
      console.log(`     Location: ${schedule.location || 'NULL'}`);
      console.log(`     Maps Link: ${schedule.google_maps_link || 'NULL'}`);
      console.log(`     Status: ${schedule.status}`);
      console.log('');
    });
  }

  // Summary
  const projectsWithLocation = projects?.filter(p => p.location) || [];
  const projectsWithMapsLink = projects?.filter(p => p.google_maps_link) || [];
  const schedulesWithLocation = schedules?.filter(s => s.location) || [];
  const schedulesWithMapsLink = schedules?.filter(s => s.google_maps_link) || [];

  console.log('\n📈 SUMMARY:');
  console.log(`Projects with location: ${projectsWithLocation.length}/${projects?.length || 0}`);
  console.log(`Projects with maps link: ${projectsWithMapsLink.length}/${projects?.length || 0}`);
  console.log(`Schedules with location: ${schedulesWithLocation.length}/${schedules?.length || 0}`);
  console.log(`Schedules with maps link: ${schedulesWithMapsLink.length}/${schedules?.length || 0}`);
}

checkData().catch(console.error);