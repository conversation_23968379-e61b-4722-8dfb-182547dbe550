const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkUsers() {
  console.log('👥 Checking users data...\n');

  // Check users table
  const { data: users, error: usersError } = await supabase
    .from('users')
    .select('id, email, name, role, created_at')
    .limit(10);

  if (usersError) {
    console.error('❌ Users error:', usersError);
  } else {
    console.log(`Found ${users.length} users:`);
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.name || 'No Name'} (${user.email})`);
      console.log(`     Role: ${user.role}`);
      console.log(`     ID: ${user.id}`);
      console.log(`     Created: ${user.created_at}`);
      console.log('');
    });
  }

  // Check auth users (this requires admin access)
  try {
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('❌ Auth users error:', authError);
    } else {
      console.log(`\n🔐 Found ${authUsers.users.length} auth users:`);
      authUsers.users.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.email}`);
        console.log(`     ID: ${user.id}`);
        console.log(`     Created: ${user.created_at}`);
        console.log(`     Email Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`);
        console.log('');
      });
    }
  } catch (error) {
    console.error('❌ Could not fetch auth users:', error.message);
  }
}

checkUsers().catch(console.error);