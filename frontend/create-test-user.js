const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestUser() {
  console.log('👤 Creating test user...\n');

  const testEmail = '<EMAIL>';
  const testPassword = 'testpassword123';

  try {
    // Create auth user
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true
    });

    if (authError) {
      console.error('❌ Auth user creation error:', authError);
      return;
    }

    console.log('✅ Auth user created:', authUser.user.id);

    // Create user profile
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .insert({
        id: authUser.user.id,
        email: testEmail,
        name: 'Test User',
        role: 'admin'
      })
      .select()
      .single();

    if (profileError) {
      console.error('❌ User profile creation error:', profileError);
      return;
    }

    console.log('✅ User profile created:', userProfile);
    console.log('\n🎉 Test user created successfully!');
    console.log(`📧 Email: ${testEmail}`);
    console.log(`🔑 Password: ${testPassword}`);

  } catch (error) {
    console.error('❌ Error creating test user:', error);
  }
}

createTestUser().catch(console.error);