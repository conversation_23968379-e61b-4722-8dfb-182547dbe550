'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Modal } from '@/components/ui/modal'
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { ClientForm } from '@/components/forms/ClientForm'
import { useDeleteClient } from '@/hooks/useApi'
import { clientsApi, projectsApi } from '@/lib/api'
import { Search, Plus, Edit, Trash2, Mail, Phone, MapPin, FileText, ArrowUpDown, ArrowUp, ArrowDown, Check } from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'
import type { Client, Project } from '@/types'

type SortOption = 'new_to_old' | 'pending_payment' | 'project_status' | 'project_value'
type SortDirection = 'asc' | 'desc'

interface ClientWithProjects extends Client {
  projects?: Project[]
  totalProjectValue?: number
  pendingAmount?: number
  hasActiveProjects?: boolean
}

export default function ClientsPage() {
  const { deleteClient, loading: deleteLoading } = useDeleteClient()
  const [clients, setClients] = useState<ClientWithProjects[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<SortOption>('new_to_old')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | null>(null)
  const [deletingClientId, setDeletingClientId] = useState<string | null>(null)

  // Load sort preferences from localStorage
  useEffect(() => {
    const savedSort = localStorage.getItem('clients-sort-preference')
    if (savedSort) {
      try {
        const { sortBy: savedSortBy, sortDirection: savedDirection } = JSON.parse(savedSort)
        setSortBy(savedSortBy)
        setSortDirection(savedDirection)
      } catch (error) {
        console.error('Error loading sort preferences:', error)
      }
    }
  }, [])

  // Save sort preferences to localStorage
  useEffect(() => {
    localStorage.setItem('clients-sort-preference', JSON.stringify({ sortBy, sortDirection }))
  }, [sortBy, sortDirection])

  // Fetch clients and projects data
  const fetchData = async () => {
    try {
      setLoading(true)
      const [clientsData, projectsData] = await Promise.all([
        clientsApi.getAll(),
        projectsApi.getAll()
      ])

      // Enhance clients with project data
      const clientsWithProjects: ClientWithProjects[] = clientsData.map(client => {
        const clientProjects = projectsData.filter(project => project.client_id === client.id)
        const totalProjectValue = clientProjects.reduce((sum, project) => sum + project.total_amount, 0)
        const pendingAmount = clientProjects.reduce((sum, project) => sum + project.amount_pending, 0)
        const hasActiveProjects = clientProjects.some(project => project.status === 'active')

        return {
          ...client,
          projects: clientProjects,
          totalProjectValue,
          pendingAmount,
          hasActiveProjects
        }
      })

      setClients(clientsWithProjects)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load clients')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  // Keyboard shortcuts for sorting
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only trigger if no input is focused and Ctrl/Cmd is pressed
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case '1':
            event.preventDefault()
            handleSortChange('new_to_old')
            break
          case '2':
            event.preventDefault()
            handleSortChange('pending_payment')
            break
          case '3':
            event.preventDefault()
            handleSortChange('project_status')
            break
          case '4':
            event.preventDefault()
            handleSortChange('project_value')
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [sortBy, sortDirection])

  // Handle sort option change
  const handleSortChange = (newSortBy: SortOption) => {
    if (newSortBy === sortBy) {
      // Toggle direction if same sort option
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      // Set new sort option with default direction
      setSortBy(newSortBy)
      setSortDirection(newSortBy === 'new_to_old' ? 'desc' : 'desc')
    }
  }

  // Filter and sort clients
  const filteredAndSortedClients = clients
    .filter(client =>
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email?.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      let result = 0

      switch (sortBy) {
        case 'new_to_old':
          result = new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          break
        case 'pending_payment':
          result = (b.pendingAmount || 0) - (a.pendingAmount || 0)
          break
        case 'project_status':
          // Active projects first, then others
          if (a.hasActiveProjects && !b.hasActiveProjects) result = -1
          else if (!a.hasActiveProjects && b.hasActiveProjects) result = 1
          else result = 0
          break
        case 'project_value':
          result = (b.totalProjectValue || 0) - (a.totalProjectValue || 0)
          break
        default:
          result = 0
      }

      // Apply sort direction
      return sortDirection === 'asc' ? -result : result
    })

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false)
    fetchData()
  }

  const handleEditSuccess = () => {
    setEditingClient(null)
    fetchData()
  }

  const handleDelete = async (client: Client) => {
    if (!confirm(`Are you sure you want to delete "${client.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      setDeletingClientId(client.id)
      await deleteClient(client.id)
      toast.success('Client deleted successfully')
      fetchData()
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete client')
    } finally {
      setDeletingClientId(null)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading clients...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 sm:space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-foreground">Clients</h1>
          <p className="text-sm text-muted-foreground mt-1">Manage your client relationships</p>
        </div>
        <Button onClick={() => setIsCreateModalOpen(true)} className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto">
          <Plus className="w-4 h-4 mr-2" />
          Add Client
        </Button>
      </div>

      {/* Search and Sort */}
      <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
        <div className="relative flex-1 sm:max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search clients..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-9 border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400"
          />
        </div>

        <div className="flex items-center justify-between sm:justify-end gap-3">
          {/* Results count */}
          <div className="text-sm text-muted-foreground">
            {filteredAndSortedClients.length} of {clients.length}
          </div>

          {/* Sort Dropdown */}
          <DropdownMenu
            trigger={
              <Button variant="outline" size="sm" className="flex items-center gap-2 h-9 px-3 border-gray-200 dark:border-gray-700">
                {sortDirection === 'asc' ? (
                  <ArrowUp className="w-3 h-3" />
                ) : (
                  <ArrowDown className="w-3 h-3" />
                )}
                <span className="text-sm hidden sm:inline">
                  {sortBy === 'new_to_old' ? 'Date' :
                  sortBy === 'pending_payment' ? 'Payment' :
                  sortBy === 'project_status' ? 'Status' :
                  sortBy === 'project_value' ? 'Value' : 'Default'}
                </span>
                <span className="text-sm sm:hidden">Sort</span>
              </Button>
            }
            align="right"
          >
          <DropdownMenuItem
            onClick={() => handleSortChange('new_to_old')}
            className={`flex items-center justify-between ${sortBy === 'new_to_old' ? 'bg-muted' : ''}`}
          >
            <span>New to Old</span>
            {sortBy === 'new_to_old' && (
              <div className="flex items-center gap-1">
                {sortDirection === 'asc' ? <ArrowUp className="w-3 h-3" /> : <ArrowDown className="w-3 h-3" />}
                <Check className="w-3 h-3" />
              </div>
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleSortChange('pending_payment')}
            className={`flex items-center justify-between ${sortBy === 'pending_payment' ? 'bg-muted' : ''}`}
          >
            <span>Pending Payment</span>
            {sortBy === 'pending_payment' && (
              <div className="flex items-center gap-1">
                {sortDirection === 'asc' ? <ArrowUp className="w-3 h-3" /> : <ArrowDown className="w-3 h-3" />}
                <Check className="w-3 h-3" />
              </div>
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleSortChange('project_status')}
            className={`flex items-center justify-between ${sortBy === 'project_status' ? 'bg-muted' : ''}`}
          >
            <span>Project Status</span>
            {sortBy === 'project_status' && (
              <div className="flex items-center gap-1">
                {sortDirection === 'asc' ? <ArrowUp className="w-3 h-3" /> : <ArrowDown className="w-3 h-3" />}
                <Check className="w-3 h-3" />
              </div>
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleSortChange('project_value')}
            className={`flex items-center justify-between ${sortBy === 'project_value' ? 'bg-muted' : ''}`}
          >
            <span>Project Value</span>
            {sortBy === 'project_value' && (
              <div className="flex items-center gap-1">
                {sortDirection === 'asc' ? <ArrowUp className="w-3 h-3" /> : <ArrowDown className="w-3 h-3" />}
                <Check className="w-3 h-3" />
              </div>
            )}
          </DropdownMenuItem>
        </DropdownMenu>

          {/* Keyboard shortcut hint */}
          <div className="text-xs text-muted-foreground opacity-60 hover:opacity-100 transition-opacity hidden sm:block">
            Ctrl+1-4
          </div>
        </div>
      </div>

      {/* Clients List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
        {filteredAndSortedClients.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              {searchTerm ? 'No clients found' : 'No clients yet'}
            </h3>
            <p className="text-muted-foreground mb-6">
              {searchTerm
                ? 'Try adjusting your search terms'
                : 'Get started by adding your first client'
              }
            </p>
            {!searchTerm && (
              <Button onClick={() => setIsCreateModalOpen(true)} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Client
              </Button>
            )}
          </div>
        ) : (
          filteredAndSortedClients.map((client) => (
            <div key={client.id} className="relative bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 hover:shadow-lg transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600 overflow-hidden group">
              {/* Content */}
              <div className="relative z-10">
                {/* Header with Avatar and Name */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {/* Avatar */}
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="text-white font-semibold text-lg">
                        {client.name.charAt(0).toUpperCase()}
                      </span>
                    </div>

                    {/* Name and Status */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Link href={`/clients/${client.id}`}>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white hover:text-blue-600 cursor-pointer transition-colors truncate">
                            {client.name}
                          </h3>
                        </Link>
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-accent text-foreground flex-shrink-0">
                          Active
                        </span>
                        {client.hasActiveProjects && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 flex-shrink-0">
                            Active
                          </span>
                        )}
                      </div>
                      {client.custom_id && (
                        <div className="text-xs text-muted-foreground -mt-1 truncate">
                          {client.custom_id}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingClient(client)}
                      className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(client)}
                      disabled={deleteLoading && deletingClientId === client.id}
                      className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/20 dark:hover:text-red-400"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="space-y-2 mb-3">
                  {client.email && (
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <Mail className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">{client.email}</span>
                    </div>
                  )}
                  {client.phone && (
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <Phone className="w-3 h-3 flex-shrink-0" />
                      <span>{client.phone}</span>
                    </div>
                  )}
                  {client.address && (
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <MapPin className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">{client.address}</span>
                    </div>
                  )}
                </div>

                {/* Project Summary */}
                {client.projects && client.projects.length > 0 && (
                  <div className="pt-3 border-t border-gray-100 dark:border-gray-700">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-300">
                        {client.projects.length} project{client.projects.length !== 1 ? 's' : ''}
                      </span>
                      <div className="flex flex-col items-end gap-1">
                        {client.totalProjectValue && client.totalProjectValue > 0 && (
                          <span className="font-medium text-gray-900 dark:text-white">
                            ₹{client.totalProjectValue.toLocaleString()}
                          </span>
                        )}
                        {client.pendingAmount && client.pendingAmount > 0 && (
                          <span className="text-xs font-medium text-orange-600 dark:text-orange-400">
                            ₹{client.pendingAmount.toLocaleString()} pending
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Create Client Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Add New Client"
      >
        <ClientForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setIsCreateModalOpen(false)}
        />
      </Modal>

      {/* Edit Client Modal */}
      <Modal
        isOpen={!!editingClient}
        onClose={() => setEditingClient(null)}
        title="Edit Client"
      >
        {editingClient && (
          <ClientForm
            client={editingClient}
            onSuccess={handleEditSuccess}
            onCancel={() => setEditingClient(null)}
          />
        )}
      </Modal>
    </div>
  )
}
