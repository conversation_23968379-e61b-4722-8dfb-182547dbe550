'use client'

import { useState, useEffect, useRef, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useClients, useProjects, useSchedules } from '@/hooks/useApi'
import { extractLocationFromMapsUrl, geocodeLocation } from '@/lib/maps-utils'
import { format } from 'date-fns'
import { Loader as GoogleMapsLoader } from '@googlemaps/js-api-loader'
import {
  MapPin,
  Users,
  FolderOpen,
  Calendar as CalendarIcon,
  Search,
  Filter,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Share,
  ExternalLink
} from 'lucide-react'
import type { Client, Project, Schedule } from '@/types'

interface MapLocation {
  id: string
  type: 'client' | 'project' | 'schedule'
  name: string
  location: string
  coordinates?: { lat: number; lng: number }
  client?: Client
  project?: Project
  schedule?: Schedule
  value?: number
  status?: string
  date?: string
}

interface MapFilters {
  selectedItems: string[] // Can contain client IDs, project IDs, or schedule IDs
  dateRange: {
    from?: Date
    to?: Date
  }
}

interface MapMetrics {
  totalLocations: number
  totalValue: number
  clientsCount: number
  projectsCount: number
  schedulesCount: number
}

// Helper function to get status color
const getStatusColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'active':
      return '#10b981' // green
    case 'pending':
    case 'in-progress':
      return '#f59e0b' // amber
    case 'cancelled':
    case 'inactive':
      return '#ef4444' // red
    default:
      return '#6b7280' // gray
  }
}

// Helper function to get status badge background color
const getStatusBadgeColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'active':
      return 'linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%)' // green gradient
    case 'pending':
    case 'in-progress':
      return 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)' // amber gradient
    case 'cancelled':
    case 'inactive':
      return 'linear-gradient(135deg, #fee2e2 0%, #fecaca 100%)' // red gradient
    default:
      return 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)' // gray gradient
  }
}

// Helper function to get status text color
const getStatusTextColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'active':
      return '#065f46' // dark green
    case 'pending':
    case 'in-progress':
      return '#92400e' // dark amber
    case 'cancelled':
    case 'inactive':
      return '#991b1b' // dark red
    default:
      return '#475569' // dark gray
  }
}

export default function MapPage() {
  // Data hooks
  const { data: clients = [], loading: clientsLoading } = useClients()
  const { data: projects = [], loading: projectsLoading } = useProjects()
  const { data: schedules = [], loading: schedulesLoading } = useSchedules()

  // Calculate loading state early
  const loading = clientsLoading || projectsLoading || schedulesLoading



  // State
  const [filters, setFilters] = useState<MapFilters>({
    selectedItems: [],
    dateRange: {}
  })
  const [mapLocations, setMapLocations] = useState<MapLocation[]>([])
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null)
  const [mapType, setMapType] = useState<'heatmap' | 'markers'>('markers')
  const [mapLoading, setMapLoading] = useState(true)
  const [mapError, setMapError] = useState<string | null>(null)

  // Map refs
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<google.maps.Map | null>(null)
  const markersRef = useRef<google.maps.Marker[]>([])
  const heatmapRef = useRef<google.maps.visualization.HeatmapLayer | null>(null)

  // Calculate metrics based on filtered data
  const metrics: MapMetrics = {
    totalLocations: mapLocations.length,
    totalValue: mapLocations.reduce((sum, loc) => sum + (loc.value || 0), 0),
    clientsCount: new Set(mapLocations.map(loc => loc.client?.id).filter(Boolean)).size,
    projectsCount: new Set(mapLocations.map(loc => loc.project?.id).filter(Boolean)).size,
    schedulesCount: mapLocations.filter(loc => loc.type === 'schedule').length
  }

  // For now, show all data until tree structure is implemented
  const filteredProjects = projects || []
  const filteredSchedules = schedules || []

  // Stabilize filters to prevent infinite re-renders
  const stableFilters = useMemo(() => filters, [
    filters.selectedItems.join(','), 
    filters.dateRange.from?.getTime(), 
    filters.dateRange.to?.getTime()
  ])

  // Initialize Google Maps
  useEffect(() => {
    const initMap = async () => {
      if (!mapRef.current) return

      try {
        setMapLoading(true)
        setMapError(null)

        // Check if API key is configured
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
        if (!apiKey) {
          throw new Error('Google Maps API key is not configured. Please set NEXT_PUBLIC_GOOGLE_MAPS_API_KEY in your environment variables.')
        }

        // Add a small delay to ensure DOM is ready
        await new Promise(resolve => setTimeout(resolve, 100))

        console.log('Initializing Google Maps with API key:', apiKey.substring(0, 10) + '...')

        const loader = new GoogleMapsLoader({
          apiKey,
          version: 'weekly',
          libraries: ['visualization', 'places']
        })

        const google = await loader.load()
        console.log('Google Maps API loaded successfully')

        const map = new google.maps.Map(mapRef.current, {
          center: { lat: 11.1271, lng: 78.6569 }, // Tamil Nadu, India
          zoom: 7,
          mapTypeId: google.maps.MapTypeId.ROADMAP,
          styles: [
            {
              featureType: 'poi',
              elementType: 'labels',
              stylers: [{ visibility: 'off' }]
            }
          ]
        })

        mapInstanceRef.current = map
        console.log('Google Maps initialized successfully')
        setMapLoading(false)
      } catch (error) {
        console.error('Error loading Google Maps:', error)
        
        // Provide more specific error messages
        let errorMessage = 'Failed to load Google Maps.'
        
        if (error instanceof Error) {
          if (error.message.includes('API key')) {
            errorMessage = error.message
          } else if (error.message.includes('RefererNotAllowedMapError')) {
            errorMessage = 'Google Maps API key is restricted. Please check your API key restrictions in Google Cloud Console.'
          } else if (error.message.includes('InvalidKeyMapError')) {
            errorMessage = 'Invalid Google Maps API key. Please check your API key configuration.'
          } else if (error.message.includes('RequestDeniedMapError')) {
            errorMessage = 'Google Maps API request denied. Please check your API key permissions.'
          } else if (error.message.includes('QuotaExceededError')) {
            errorMessage = 'Google Maps API quota exceeded. Please check your usage limits.'
          } else {
            errorMessage = `Google Maps error: ${error.message}`
          }
        }
        
        setMapError(errorMessage)
        setMapLoading(false)
      }
    }

    // Only initialize map after data is loaded
    if (!loading && clients && projects && schedules) {
      initMap()
    }
  }, [loading, clients, projects, schedules])

  // Update map markers and heatmap
  useEffect(() => {
    if (!mapInstanceRef.current || mapLoading) return

    // Clear existing markers
    markersRef.current.forEach(marker => marker.setMap(null))
    markersRef.current = []

    // Clear existing heatmap
    if (heatmapRef.current) {
      heatmapRef.current.setMap(null)
      heatmapRef.current = null
    }

    if (mapType === 'markers') {
      // Add markers for each location
      mapLocations.forEach(location => {
        if (!location.coordinates) return

        const marker = new google.maps.Marker({
          position: location.coordinates,
          map: mapInstanceRef.current,
          title: location.name,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            scale: 8,
            fillColor: getMarkerColor(location),
            fillOpacity: 0.8,
            strokeColor: '#ffffff',
            strokeWeight: 2,
          }
        })

        // Add click listener
        marker.addListener('click', () => {
          setSelectedLocation(location)

          // Center map on clicked marker
          mapInstanceRef.current?.panTo(location.coordinates!)
        })

        // Add hover info window with modern design
        const infoWindow = new google.maps.InfoWindow({
          content: `
            <style>
              .gm-style-iw { 
                padding: 0 !important; 
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style-iw-d { 
                padding: 0 !important; 
                margin: 0 !important;
                overflow: visible !important;
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style-iw-c { 
                padding: 0 !important; 
                margin: 0 !important;
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style-iw-t { 
                padding: 0 !important; 
                margin: 0 !important;
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style-iw-tc { 
                padding: 0 !important; 
                margin: 0 !important;
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              /* Remove all borders, outlines and backgrounds from InfoWindow elements */
              .gm-style .gm-style-iw-chr {
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style .gm-style-iw-ch {
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              /* Remove any potential wrapper borders */
              .gm-style .gm-style-iw-a {
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
            </style>
            <div style="
              padding: 0;
              margin: 0;
              min-width: 300px;
              max-width: 340px;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
              border-radius: 16px;
              border: 1px solid rgba(148, 163, 184, 0.2);
              box-shadow: 
                0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04),
                0 0 0 1px rgba(255, 255, 255, 0.8);
              overflow: hidden;
              position: relative;
            ">
              <!-- Header with gradient -->
              <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 20px 20px 16px 20px;
                margin: 0;
                position: relative;
                overflow: hidden;
              ">
                <!-- Decorative elements -->
                <div style="
                  position: absolute;
                  top: -50%;
                  right: -20%;
                  width: 100px;
                  height: 100px;
                  background: rgba(255, 255, 255, 0.1);
                  border-radius: 50%;
                  filter: blur(20px);
                "></div>
                <div style="
                  position: absolute;
                  bottom: -30%;
                  left: -10%;
                  width: 60px;
                  height: 60px;
                  background: rgba(255, 255, 255, 0.08);
                  border-radius: 50%;
                  filter: blur(15px);
                "></div>
                
                <h3 style="
                  font-weight: 700;
                  margin: 0;
                  padding: 0;
                  color: #ffffff;
                  font-size: 20px;
                  line-height: 1.2;
                  letter-spacing: -0.025em;
                  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                  position: relative;
                  z-index: 1;
                ">${location.name}</h3>
              </div>
              
              <!-- Content Section -->
              <div style="padding: 12px 16px 16px 16px; margin: 0;">
              
              <!-- Location with enhanced styling -->
              <div style="
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                padding: 12px 16px;
                background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
                border-radius: 12px;
                border: 1px solid rgba(99, 102, 241, 0.1);
                position: relative;
                overflow: hidden;
              ">
                <!-- Subtle background pattern -->
                <div style="
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background: radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.03) 0%, transparent 50%);
                "></div>
                
                <div style="
                  width: 32px;
                  height: 32px;
                  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 12px;
                  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
                  position: relative;
                  z-index: 1;
                ">
                  <span style="font-size: 16px;">📍</span>
                </div>
                <p style="
                  font-size: 15px;
                  color: #374151;
                  margin: 0;
                  line-height: 1.4;
                  font-weight: 600;
                  position: relative;
                  z-index: 1;
                ">${location.location}</p>
              </div>
              
              <!-- Details Grid -->
              <div style="display: flex; flex-direction: column; gap: 12px;">
                ${location.client ? `
                  <div style="
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
                    border-radius: 10px;
                    border: 1px solid rgba(34, 197, 94, 0.1);
                    transition: all 0.2s ease;
                  ">
                    <div style="
                      width: 28px;
                      height: 28px;
                      background: linear-gradient(135deg, #22c55e 0%, #3b82f6 100%);
                      border-radius: 6px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin-right: 12px;
                      box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
                    ">
                      <span style="font-size: 12px;">👤</span>
                    </div>
                    <div style="flex: 1;">
                      <div style="
                        font-size: 11px;
                        font-weight: 600;
                        color: #6b7280;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                        margin-bottom: 2px;
                      ">Client</div>
                      <div style="
                        font-size: 15px;
                        color: #111827;
                        font-weight: 600;
                      ">${location.client.name}</div>
                    </div>
                  </div>
                ` : ''}
                
                ${location.value ? `
                  <div style="
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(34, 197, 94, 0.05) 100%);
                    border-radius: 10px;
                    border: 1px solid rgba(16, 185, 129, 0.1);
                    transition: all 0.2s ease;
                  ">
                    <div style="
                      width: 28px;
                      height: 28px;
                      background: linear-gradient(135deg, #10b981 0%, #22c55e 100%);
                      border-radius: 6px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin-right: 12px;
                      box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
                    ">
                      <span style="font-size: 12px;">💰</span>
                    </div>
                    <div style="flex: 1;">
                      <div style="
                        font-size: 11px;
                        font-weight: 600;
                        color: #6b7280;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                        margin-bottom: 2px;
                      ">Value</div>
                      <div style="
                        font-size: 16px;
                        color: #059669;
                        font-weight: 700;
                      ">₹${location.value.toLocaleString()}</div>
                    </div>
                  </div>
                ` : ''}
                
                ${location.status ? `
                  <div style="
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    background: linear-gradient(135deg, rgba(147, 51, 234, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
                    border-radius: 10px;
                    border: 1px solid rgba(147, 51, 234, 0.1);
                    transition: all 0.2s ease;
                  ">
                    <div style="
                      width: 28px;
                      height: 28px;
                      background: linear-gradient(135deg, #9333ea 0%, #a855f7 100%);
                      border-radius: 6px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin-right: 12px;
                      box-shadow: 0 2px 4px rgba(147, 51, 234, 0.2);
                    ">
                      <span style="font-size: 12px;">📊</span>
                    </div>
                    <div style="flex: 1;">
                      <div style="
                        font-size: 11px;
                        font-weight: 600;
                        color: #6b7280;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                        margin-bottom: 2px;
                      ">Status</div>
                      <div style="
                        display: inline-block;
                        font-size: 13px;
                        color: ${getStatusTextColor(location.status)};
                        background: ${getStatusBadgeColor(location.status)};
                        padding: 6px 12px;
                        border-radius: 20px;
                        font-weight: 600;
                        text-transform: capitalize;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                      ">${location.status}</div>
                    </div>
                  </div>
                ` : ''}
              </div>
              </div>
            </div>
          `
        })

        marker.addListener('mouseover', () => {
          infoWindow.open(mapInstanceRef.current, marker)
        })

        marker.addListener('mouseout', () => {
          infoWindow.close()
        })

        markersRef.current.push(marker)
      })
    } else if (mapType === 'heatmap') {
      // Create heatmap data
      const heatmapData = mapLocations
        .filter(location => location.coordinates)
        .map(location => ({
          location: new google.maps.LatLng(location.coordinates!.lat, location.coordinates!.lng),
          weight: location.value ? Math.log(location.value + 1) : 1
        }))

      if (heatmapData.length > 0) {
        heatmapRef.current = new google.maps.visualization.HeatmapLayer({
          data: heatmapData,
          map: mapInstanceRef.current,
          radius: 50,
          opacity: 0.8
        })

        // Add invisible markers for hover functionality in heatmap mode
        mapLocations.forEach(location => {
          if (!location.coordinates) return

          // Create invisible marker for hover interaction
          const marker = new google.maps.Marker({
            position: location.coordinates,
            map: mapInstanceRef.current,
            title: location.name,
            icon: {
              path: google.maps.SymbolPath.CIRCLE,
              scale: 15, // Larger invisible area for easier hovering
              fillColor: 'transparent',
              fillOpacity: 0,
              strokeColor: 'transparent',
              strokeWeight: 0,
            },
            zIndex: 1000 // Ensure markers are above heatmap
          })

          // Create hover info window with consistent design matching marker mode
          const infoWindow = new google.maps.InfoWindow({
            content: `
            <style>
              .gm-style-iw { 
                padding: 0 !important; 
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style-iw-d { 
                padding: 0 !important; 
                margin: 0 !important;
                overflow: visible !important;
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style-iw-c { 
                padding: 0 !important; 
                margin: 0 !important;
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style-iw-t { 
                padding: 0 !important; 
                margin: 0 !important;
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style-iw-tc { 
                padding: 0 !important; 
                margin: 0 !important;
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              /* Remove all borders, outlines and backgrounds from InfoWindow elements */
              .gm-style .gm-style-iw-chr {
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              .gm-style .gm-style-iw-ch {
                background: transparent !important;
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
              /* Remove any potential wrapper borders */
              .gm-style .gm-style-iw-a {
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
              }
            </style>
            <div style="
              min-width: 320px;
              max-width: 380px;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background: #ffffff;
              border-radius: 16px;
              overflow: hidden;
              position: relative;
              box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
            ">
              <!-- Header Section with Gradient -->
              <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 20px 24px;
                position: relative;
                overflow: hidden;
              ">
                <!-- Decorative Elements -->
                <div style="
                  position: absolute;
                  top: -20%;
                  right: -15%;
                  width: 80px;
                  height: 80px;
                  background: rgba(255, 255, 255, 0.1);
                  border-radius: 50%;
                  filter: blur(20px);
                "></div>
                <div style="
                  position: absolute;
                  bottom: -30%;
                  left: -10%;
                  width: 60px;
                  height: 60px;
                  background: rgba(255, 255, 255, 0.08);
                  border-radius: 50%;
                  filter: blur(15px);
                "></div>
                
                <h3 style="
                  font-weight: 700;
                  margin: 0;
                  padding: 0;
                  color: #ffffff;
                  font-size: 20px;
                  line-height: 1.2;
                  letter-spacing: -0.025em;
                  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                  position: relative;
                  z-index: 1;
                ">${location.name}</h3>
              </div>
              
              <!-- Content Section -->
              <div style="padding: 12px 16px 16px 16px; margin: 0;">
              
              <!-- Location with enhanced styling -->
              <div style="
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                padding: 12px 16px;
                background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
                border-radius: 12px;
                border: 1px solid rgba(99, 102, 241, 0.1);
                position: relative;
                overflow: hidden;
              ">
                <!-- Subtle background pattern -->
                <div style="
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background: radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.03) 0%, transparent 50%);
                "></div>
                
                <div style="
                  width: 32px;
                  height: 32px;
                  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 12px;
                  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
                  position: relative;
                  z-index: 1;
                ">
                  <span style="font-size: 16px;">📍</span>
                </div>
                <p style="
                  font-size: 15px;
                  color: #374151;
                  margin: 0;
                  line-height: 1.4;
                  font-weight: 600;
                  position: relative;
                  z-index: 1;
                ">${location.location}</p>
              </div>
              
              <!-- Details Grid -->
              <div style="display: flex; flex-direction: column; gap: 12px;">
                ${location.client ? `
                  <div style="
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
                    border-radius: 10px;
                    border: 1px solid rgba(34, 197, 94, 0.1);
                    transition: all 0.2s ease;
                  ">
                    <div style="
                      width: 28px;
                      height: 28px;
                      background: linear-gradient(135deg, #22c55e 0%, #3b82f6 100%);
                      border-radius: 6px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin-right: 12px;
                      box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
                    ">
                      <span style="font-size: 12px;">👤</span>
                    </div>
                    <div style="flex: 1;">
                      <div style="
                        font-size: 11px;
                        font-weight: 600;
                        color: #6b7280;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                        margin-bottom: 2px;
                      ">Client</div>
                      <div style="
                        font-size: 15px;
                        color: #111827;
                        font-weight: 600;
                      ">${location.client.name}</div>
                    </div>
                  </div>
                ` : ''}
                
                ${location.value ? `
                  <div style="
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(34, 197, 94, 0.05) 100%);
                    border-radius: 10px;
                    border: 1px solid rgba(16, 185, 129, 0.1);
                    transition: all 0.2s ease;
                  ">
                    <div style="
                      width: 28px;
                      height: 28px;
                      background: linear-gradient(135deg, #10b981 0%, #22c55e 100%);
                      border-radius: 6px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin-right: 12px;
                      box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
                    ">
                      <span style="font-size: 12px;">💰</span>
                    </div>
                    <div style="flex: 1;">
                      <div style="
                        font-size: 11px;
                        font-weight: 600;
                        color: #6b7280;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                        margin-bottom: 2px;
                      ">Value</div>
                      <div style="
                        font-size: 16px;
                        color: #059669;
                        font-weight: 700;
                      ">₹${location.value.toLocaleString()}</div>
                    </div>
                  </div>
                ` : ''}
                
                ${location.status ? `
                  <div style="
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    background: linear-gradient(135deg, rgba(147, 51, 234, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
                    border-radius: 10px;
                    border: 1px solid rgba(147, 51, 234, 0.1);
                    transition: all 0.2s ease;
                  ">
                    <div style="
                      width: 28px;
                      height: 28px;
                      background: linear-gradient(135deg, #9333ea 0%, #a855f7 100%);
                      border-radius: 6px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin-right: 12px;
                      box-shadow: 0 2px 4px rgba(147, 51, 234, 0.2);
                    ">
                      <span style="font-size: 12px;">📊</span>
                    </div>
                    <div style="flex: 1;">
                      <div style="
                        font-size: 11px;
                        font-weight: 600;
                        color: #6b7280;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                        margin-bottom: 2px;
                      ">Status</div>
                      <div style="
                        display: inline-block;
                        font-size: 13px;
                        color: ${getStatusTextColor(location.status)};
                        background: ${getStatusBadgeColor(location.status)};
                        padding: 6px 12px;
                        border-radius: 20px;
                        font-weight: 600;
                        text-transform: capitalize;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                      ">${location.status}</div>
                    </div>
                  </div>
                ` : ''}
              </div>
              </div>
            </div>
            `,
            disableAutoPan: true,
            pixelOffset: new google.maps.Size(0, -10)
          })

          // Add hover listeners
          marker.addListener('mouseover', () => {
            infoWindow.open(mapInstanceRef.current, marker)
          })

          marker.addListener('mouseout', () => {
            infoWindow.close()
          })

          // Add click listener for selection
          marker.addListener('click', () => {
            setSelectedLocation(location)
            mapInstanceRef.current?.panTo(location.coordinates!)
          })

          markersRef.current.push(marker)
        })
      }
    }

    // Adjust map bounds to fit all markers
    if (mapLocations.length > 0 && mapLocations.some(l => l.coordinates)) {
      const bounds = new google.maps.LatLngBounds()
      mapLocations.forEach(location => {
        if (location.coordinates) {
          bounds.extend(location.coordinates)
        }
      })
      mapInstanceRef.current.fitBounds(bounds)

      // Ensure minimum zoom level
      const listener = google.maps.event.addListener(mapInstanceRef.current, 'bounds_changed', () => {
        if (mapInstanceRef.current!.getZoom()! > 15) {
          mapInstanceRef.current!.setZoom(15)
        }
        google.maps.event.removeListener(listener)
      })
    }
  }, [mapLocations, mapType, mapLoading])

  // Get marker color based on location type and status
  const getMarkerColor = (location: MapLocation): string => {
    if (location.type === 'schedule') {
      switch (location.status) {
        case 'completed': return '#10b981' // green
        case 'scheduled': return '#f59e0b' // amber
        case 'in_progress': return '#3b82f6' // blue
        case 'cancelled': return '#ef4444' // red
        default: return '#6b7280' // gray
      }
    } else if (location.type === 'project') {
      switch (location.status) {
        case 'active': return '#3b82f6' // blue
        case 'completed': return '#10b981' // green
        case 'on_hold': return '#f59e0b' // amber
        case 'cancelled': return '#ef4444' // red
        default: return '#6b7280' // gray
      }
    }
    return '#8b5cf6' // purple for clients
  }

  // Process location data
  useEffect(() => {
    const processLocations = async () => {
      console.log('🗺️ Processing location data...')
      console.log('📊 Projects data:', projects?.length, 'projects')
      console.log('📅 Schedules data:', schedules?.length, 'schedules')
      console.log('🔍 Current filters:', stableFilters)
      
      // Log projects with locations
      const projectsWithLocations = (projects || []).filter(p => p.location)
      console.log(`📍 Projects with locations: ${projectsWithLocations.length}`, projectsWithLocations.map(p => ({name: p.name, location: p.location})))
      
      const locations: MapLocation[] = []

      // Add project locations
      console.log(`🏗️ Processing ${(projects || []).length} projects...`)
      for (const project of projects || []) {
        console.log(`📍 Project: ${project.name}`, {
          location: project.location,
          google_maps_link: project.google_maps_link,
          shouldInclude: shouldIncludeProject(project)
        })
        
        if (project.location && shouldIncludeProject(project)) {
          const coordinates = await extractCoordinatesFromLocation(project.location, project.google_maps_link)
          console.log(`📍 Extracted coordinates for ${project.name}:`, coordinates)

          // Only add to locations if we successfully extracted coordinates
          if (coordinates) {
            locations.push({
              id: `project-${project.id}`,
              type: 'project',
              name: project.name,
              location: project.location,
              coordinates,
              client: project.client,
              project,
              value: project.total_amount,
              status: project.status
            })
          } else {
            console.log(`⚠️ No coordinates found for project ${project.name}`)
          }
        }
      }

      // Add schedule locations
      console.log(`📅 Processing ${(schedules || []).length} schedules...`)
      for (const schedule of schedules || []) {
        console.log(`📍 Schedule: ${schedule.project?.name} - ${schedule.custom_id}`, {
          location: schedule.location,
          google_maps_link: schedule.google_maps_link,
          shouldInclude: shouldIncludeSchedule(schedule)
        })
        
        if (schedule.location && shouldIncludeSchedule(schedule)) {
          const coordinates = await extractCoordinatesFromLocation(schedule.location, schedule.google_maps_link)
          console.log(`📍 Extracted coordinates for ${schedule.project?.name} - ${schedule.custom_id}:`, coordinates)

          // Only add to locations if we successfully extracted coordinates
          if (coordinates) {
            locations.push({
              id: `schedule-${schedule.id}`,
              type: 'schedule',
              name: `${schedule.project?.name} - ${schedule.custom_id}`,
              location: schedule.location,
              coordinates,
              client: schedule.project?.client,
              project: schedule.project,
              schedule,
              value: schedule.amount,
              status: schedule.status,
              date: schedule.scheduled_date
            })
          } else {
            console.log(`⚠️ No coordinates found for schedule ${schedule.project?.name} - ${schedule.custom_id}`)
          }
        }
      }

      console.log(`🎯 Final map locations (${locations.length} total):`, locations)
      setMapLocations(locations)
    }

    if (!clientsLoading && !projectsLoading && !schedulesLoading) {
      processLocations()
    }
  }, [clients, projects, schedules, stableFilters, clientsLoading, projectsLoading, schedulesLoading])

  // Helper functions for filtering
  const shouldIncludeProject = (project: Project): boolean => {
    // If no items selected, show all
    if (stableFilters.selectedItems.length === 0) return true

    // Check if this project or its client is selected
    if (stableFilters.selectedItems.includes(project.id) || stableFilters.selectedItems.includes(project.client_id)) return true

    // Check date range if specified
    if (stableFilters.dateRange.from || stableFilters.dateRange.to) {
      // For projects, we'll check if any of their schedules fall within the date range
      const projectSchedules = (schedules || []).filter(s => s.project_id === project.id)
      const hasScheduleInRange = projectSchedules.some(schedule => {
        const scheduleDate = new Date(schedule.scheduled_date)
        if (stableFilters.dateRange.from && scheduleDate < stableFilters.dateRange.from) return false
        if (stableFilters.dateRange.to && scheduleDate > stableFilters.dateRange.to) return false
        return true
      })
      if (!hasScheduleInRange) return false
    }

    return false
  }

  const shouldIncludeSchedule = (schedule: Schedule): boolean => {
    // If no items selected, show all
    if (stableFilters.selectedItems.length === 0) return true

    // Check if this schedule, its project, or its client is selected
    if (stableFilters.selectedItems.includes(schedule.id) ||
        stableFilters.selectedItems.includes(schedule.project_id) ||
        (schedule.project && stableFilters.selectedItems.includes(schedule.project.client_id))) return true

    // Check date range if specified
    if (stableFilters.dateRange.from || stableFilters.dateRange.to) {
      const scheduleDate = new Date(schedule.scheduled_date)
      if (stableFilters.dateRange.from && scheduleDate < stableFilters.dateRange.from) return false
      if (stableFilters.dateRange.to && scheduleDate > stableFilters.dateRange.to) return false
    }

    return false
  }

  // Geocode location using Google Maps Geocoding API
  const geocodeLocation = async (location: string): Promise<{ lat: number; lng: number } | undefined> => {
    try {
      if (!mapInstanceRef.current) {
        console.log('❌ Map not initialized for geocoding')
        return undefined
      }

      const geocoder = new google.maps.Geocoder()
      const result = await new Promise<google.maps.GeocoderResult[]>((resolve, reject) => {
        geocoder.geocode(
          { 
            address: location,
            region: 'IN', // Bias towards India
            componentRestrictions: { country: 'IN' } // Restrict to India
          },
          (results, status) => {
            if (status === google.maps.GeocoderStatus.OK && results) {
              resolve(results)
            } else {
              reject(new Error(`Geocoding failed: ${status}`))
            }
          }
        )
      })

      if (result && result.length > 0) {
        const { lat, lng } = result[0].geometry.location
        return { lat: lat(), lng: lng() }
      }

      return undefined
    } catch (error) {
      console.error('Geocoding error:', error)
      return undefined
    }
  }

  // Extract coordinates from location string or Google Maps link
  const extractCoordinatesFromLocation = async (location: string, mapsLink?: string): Promise<{ lat: number; lng: number } | undefined> => {
    try {
      // First try to extract from Google Maps link
      if (mapsLink) {
        console.log(`🔗 Trying to extract from Maps link: ${mapsLink}`);
        const locationInfo = await extractLocationFromMapsUrl(mapsLink)
        if (locationInfo?.coordinates) {
          console.log(`✅ Coordinates from Maps link: ${locationInfo.coordinates.lat}, ${locationInfo.coordinates.lng}`);
          return locationInfo.coordinates
        }
      }

      // Fallback: try to geocode the location string
      if (location) {
        console.log(`📍 Trying to geocode location string: ${location}`);
        const coordinates = await geocodeLocation(location);
        if (coordinates) {
          console.log(`✅ Coordinates from geocoding: ${coordinates.lat}, ${coordinates.lng}`);
          return coordinates;
        }
      }

      console.log(`❌ No coordinates found for location: ${location}, maps link: ${mapsLink}`);
      return undefined
    } catch (error) {
      console.error('Error extracting coordinates:', error)
      return undefined
    }
  }

  // Show loading state while data is being fetched
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[600px]">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading map data...</p>
        </div>
      </div>
    )
  }

  // Ensure data is available before rendering
  if (!clients || !projects || !schedules) {
    return (
      <div className="flex items-center justify-center min-h-[600px]">
        <div className="text-center">
          <AlertCircle className="w-8 h-8 mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Failed to load data. Please refresh the page.</p>
          <Button className="mt-4" onClick={() => window.location.reload()}>
            Refresh
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Map & Locations</h1>
          <p className="text-muted-foreground">Visualize clients, projects, and schedules geographically</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Top Metrics Section */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MapPin className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Locations</p>
                <p className="text-2xl font-bold">{metrics.totalLocations}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FolderOpen className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">₹{(metrics.totalValue / 100000).toFixed(1)}L</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Clients</p>
                <p className="text-2xl font-bold">{metrics.clientsCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FolderOpen className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Projects</p>
                <p className="text-2xl font-bold">{metrics.projectsCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="w-5 h-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Schedules</p>
                <p className="text-2xl font-bold">{metrics.schedulesCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="w-5 h-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Tree-structured Client/Project/Schedule Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Select Items</label>
              <Select
                value={filters.selectedItems.length === 1 ? filters.selectedItems[0] : ''}
                onValueChange={(value) => {
                  if (value === 'all') {
                    setFilters(prev => ({ ...prev, selectedItems: [] }))
                  } else {
                    setFilters(prev => ({ ...prev, selectedItems: [value] }))
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select clients, projects, or schedules" />
                </SelectTrigger>
                <SelectContent className="max-h-80 overflow-y-auto">
                  <SelectItem value="all">All Items</SelectItem>
                  {(clients || []).map(client => (
                    <div key={client.id}>
                      <SelectItem value={client.id} className="font-medium">
                        👤 {client.name}
                      </SelectItem>
                      {/* Projects under this client */}
                      {(projects || [])
                        .filter(project => project.client_id === client.id)
                        .map(project => (
                          <div key={project.id}>
                            <SelectItem value={project.id} className="pl-6 text-sm">
                              📁 {project.name}
                            </SelectItem>
                            {/* Schedules under this project */}
                            {(schedules || [])
                              .filter(schedule => schedule.project_id === project.id)
                              .slice(0, 3) // Limit to first 3 schedules to avoid overwhelming UI
                              .map(schedule => (
                                <SelectItem
                                  key={schedule.id}
                                  value={schedule.id}
                                  className="pl-12 text-xs text-muted-foreground"
                                >
                                  📅 {schedule.custom_id} - {format(new Date(schedule.scheduled_date), 'MMM dd')}
                                </SelectItem>
                              ))}
                            {/* Show count if more schedules exist */}
                            {(schedules || []).filter(schedule => schedule.project_id === project.id).length > 3 && (
                              <div className="pl-12 text-xs text-muted-foreground py-1">
                                ... and {(schedules || []).filter(schedule => schedule.project_id === project.id).length - 3} more schedules
                              </div>
                            )}
                          </div>
                        ))}
                    </div>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date Range Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Date Range</label>
              <div className="flex space-x-2">
                <Input
                  type="date"
                  placeholder="From date"
                  value={filters.dateRange.from ? filters.dateRange.from.toISOString().split('T')[0] : ''}
                  onChange={(e) => {
                    const date = e.target.value ? new Date(e.target.value) : undefined
                    setFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, from: date }
                    }))
                  }}
                />
                <Input
                  type="date"
                  placeholder="To date"
                  value={filters.dateRange.to ? filters.dateRange.to.toISOString().split('T')[0] : ''}
                  onChange={(e) => {
                    const date = e.target.value ? new Date(e.target.value) : undefined
                    setFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, to: date }
                    }))
                  }}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Map Visualization Section */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="w-5 h-5" />
                  <span>Map Visualization</span>
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Button
                    variant={mapType === 'markers' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setMapType('markers')}
                  >
                    Markers
                  </Button>
                  <Button
                    variant={mapType === 'heatmap' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setMapType('heatmap')}
                  >
                    Heatmap
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="relative">
                {mapLoading && (
                  <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center">
                    <div className="text-center">
                      <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">Loading Google Maps...</p>
                    </div>
                  </div>
                )}

                {mapError && (
                  <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center">
                    <div className="text-center max-w-md mx-auto p-6">
                      <AlertCircle className="w-12 h-12 mx-auto mb-4 text-destructive" />
                      <h3 className="text-lg font-semibold mb-2">Google Maps Error</h3>
                      <p className="text-sm text-destructive mb-4">{mapError}</p>
                      
                      {mapError.includes('API key') && (
                        <div className="text-xs text-muted-foreground mb-4 p-3 bg-muted rounded-lg">
                          <p className="font-medium mb-1">Troubleshooting steps:</p>
                          <ul className="text-left space-y-1">
                            <li>• Check if NEXT_PUBLIC_GOOGLE_MAPS_API_KEY is set in .env.local</li>
                            <li>• Verify the API key is valid in Google Cloud Console</li>
                            <li>• Ensure Maps JavaScript API is enabled</li>
                            <li>• Check API key restrictions and referrer settings</li>
                          </ul>
                        </div>
                      )}
                      
                      <div className="flex gap-2 justify-center">
                        <Button size="sm" onClick={() => window.location.reload()}>
                          <RefreshCw className="w-4 h-4 mr-1" />
                          Retry
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => window.open('https://console.cloud.google.com/apis/credentials', '_blank')}
                        >
                          <ExternalLink className="w-4 h-4 mr-1" />
                          Google Console
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                <div
                  ref={mapRef}
                  className="w-full h-[600px] rounded-lg border bg-muted"
                  style={{ minHeight: '600px' }}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Location Details Sidebar */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Location Details</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedLocation ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg">{selectedLocation.name}</h3>
                    <p className="text-sm text-muted-foreground flex items-center mt-1">
                      <MapPin className="w-4 h-4 mr-1" />
                      {selectedLocation.location}
                    </p>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    {selectedLocation.client && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Client</label>
                        <p className="text-sm">{selectedLocation.client.name}</p>
                      </div>
                    )}

                    {selectedLocation.project && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Project</label>
                        <p className="text-sm">{selectedLocation.project.name}</p>
                      </div>
                    )}

                    {selectedLocation.schedule && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Schedule</label>
                        <p className="text-sm">{selectedLocation.schedule.custom_id}</p>
                        {selectedLocation.date && (
                          <p className="text-xs text-muted-foreground">
                            {format(new Date(selectedLocation.date), 'PPP')}
                          </p>
                        )}
                      </div>
                    )}

                    {selectedLocation.status && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Status</label>
                        <div className="flex items-center space-x-2 mt-1">
                          {selectedLocation.status === 'completed' && <CheckCircle className="w-4 h-4 text-green-600" />}
                          {selectedLocation.status === 'active' && <Clock className="w-4 h-4 text-blue-600" />}
                          {selectedLocation.status === 'scheduled' && <Clock className="w-4 h-4 text-orange-600" />}
                          {selectedLocation.status === 'cancelled' && <XCircle className="w-4 h-4 text-red-600" />}
                          <Badge variant={
                            selectedLocation.status === 'completed' ? 'default' :
                            selectedLocation.status === 'active' ? 'secondary' :
                            selectedLocation.status === 'scheduled' ? 'outline' : 'destructive'
                          }>
                            {selectedLocation.status}
                          </Badge>
                        </div>
                      </div>
                    )}

                    {selectedLocation.value && selectedLocation.value > 0 && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Value</label>
                        <p className="text-sm font-semibold">₹{selectedLocation.value.toLocaleString()}</p>
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Quick Actions */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Quick Actions</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <Button size="sm" variant="outline" className="text-xs">
                        <Eye className="w-3 h-3 mr-1" />
                        View Details
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="text-xs"
                        onClick={() => {
                          // Open SharePoint schedule folder if available
                          if (selectedLocation.schedule?.sharepoint_share_link) {
                            window.open(selectedLocation.schedule.sharepoint_share_link, '_blank');
                          } else {
                            console.log('No SharePoint folder link available for this schedule');
                            // Could show a toast notification here
                          }
                        }}
                      >
                        <FolderOpen className="w-3 h-3 mr-1" />
                        Open Files
                      </Button>
                      <Button size="sm" variant="outline" className="text-xs">
                        <Share className="w-3 h-3 mr-1" />
                        Share
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-sm text-muted-foreground">
                    Click on a location marker to view details
                  </p>
                </div>
              )}
            </CardContent>
          </Card>


        </div>
      </div>
    </div>
  )
}
