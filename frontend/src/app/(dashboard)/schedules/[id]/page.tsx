'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Modal } from '@/components/ui/modal'
import { ScheduleForm } from '@/components/forms/ScheduleForm'
import { TaskForm } from '@/components/forms/TaskForm'
import { RedesignedTaskCard } from '@/components/ui/redesigned-task-card'
import { ScheduleCompletionForm, type ScheduleCompletionData } from '@/components/ui/schedule-completion-form'

import { schedulesApi, tasksApi, projectsApi, usersApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import {
  ArrowLeft,
  Calendar,
  Clock,
  User,
  MapPin,
  Building,
  Edit,
  CheckCircle,
  AlertCircle,
  XCircle,
  Plus,
  ExternalLink,
  FileText,
  Battery,
  Zap
} from 'lucide-react'
import toast from 'react-hot-toast'
import type { Schedule, Task, User as UserType } from '@/types'
import Link from 'next/link'

export default function ScheduleDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const scheduleId = params.id as string

  const [schedule, setSchedule] = useState<Schedule | null>(null)
  const [tasks, setTasks] = useState<Task[]>([])
  const [users, setUsers] = useState<UserType[]>([])
  const [loading, setLoading] = useState(true)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false)
  const [completingSchedule, setCompletingSchedule] = useState<Schedule | null>(null)


  const fetchScheduleData = async () => {
    try {
      setLoading(true)
      const [scheduleData, tasksData, usersData] = await Promise.all([
        schedulesApi.getById(scheduleId),
        tasksApi.getAll(),
        usersApi.getAll()
      ])

      setSchedule(scheduleData)
      setUsers(usersData || [])

      if (!scheduleData) {
        toast.error('Schedule not found')
        return
      }

      // Filter tasks related to this schedule:
      // 1. Tasks specifically assigned to this schedule (schedule_id matches)
      // 2. Project-level tasks that don't have a schedule_id (default tasks)
      const scheduleTasks = tasksData
        .filter((task: Task) =>
          task.shoot_id === scheduleId ||
          (task.project_id === scheduleData.project_id && !task.shoot_id)
        )
        .sort((a: Task, b: Task) => {
          // Enhanced sorting: schedule tasks first, then project tasks
          const aIsProjectTask = !a.shoot_id
          const bIsProjectTask = !b.shoot_id

          // If one is project task and other is schedule task, schedule task comes first
          if (aIsProjectTask && !bIsProjectTask) return -1
          if (!aIsProjectTask && bIsProjectTask) return 1

          // Both are same type, sort by order field
          const aOrder = a.order ?? (aIsProjectTask ? 1000 : 0)
          const bOrder = b.order ?? (bIsProjectTask ? 1000 : 0)

          if (aOrder !== bOrder) {
            return aOrder - bOrder
          }

          // Fallback to creation time
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        });

      // If no tasks exist for this project and we have client type, create default tasks
      if (scheduleTasks.length === 0 && scheduleData.project?.client?.client_type) {
        console.log('Creating default tasks for client type:', scheduleData.project.client.client_type)
        try {
          await projectsApi.createDefaultTasks(
            scheduleData.project_id,
            scheduleData.project.client.client_type,
            scheduleData.scheduled_date,
            scheduleId // Pass the schedule ID for schedule-based tasks
          )

          console.log('Default tasks created, refetching...')
          // Refetch tasks after creating default ones
          const [updatedTasksData, updatedUsersData] = await Promise.all([
            tasksApi.getAll(),
            usersApi.getAll()
          ])
          const updatedScheduleTasks = updatedTasksData.filter((task: Task) =>
            task.shoot_id === scheduleId ||
            (task.project_id === scheduleData.project_id && !task.shoot_id)
          )
          console.log('Updated tasks:', updatedScheduleTasks)
          setTasks(updatedScheduleTasks)
          setUsers(updatedUsersData || [])
          toast.success('Default tasks created for this project')
        } catch (taskError: any) {
          console.error('Failed to create default tasks:', taskError)
          toast.error('Failed to create default tasks: ' + (taskError?.message || 'Unknown error'))
          setTasks(scheduleTasks) // Set empty tasks if creation fails
        }
      } else {
        console.log('Not creating tasks. scheduleTasks.length:', scheduleTasks.length, 'client_type:', scheduleData.project?.client?.client_type)
        console.log('Full schedule data:', JSON.stringify(scheduleData, null, 2))
        setTasks(scheduleTasks)
      }
    } catch (error: any) {
      toast.error('Failed to load schedule details')
      console.error('Error fetching schedule data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (scheduleId) {
      fetchScheduleData()
    }
  }, [scheduleId])

  const handleEditSuccess = () => {
    setIsEditModalOpen(false)
    fetchScheduleData()
  }

  const handleAddTaskSuccess = () => {
    setIsAddTaskModalOpen(false)
    fetchScheduleData()
  }

  const handleTaskStatusChange = async (task: Task, newStatus: Task['status']) => {
    try {
      // Optimistic update - update local state immediately
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === task.id
            ? { ...t, status: newStatus, updated_at: new Date().toISOString() }
            : t
        )
      )

      await tasksApi.update(task.id, { status: newStatus })
      toast.success('Task status updated successfully')
    } catch (error: any) {
      // Revert optimistic update on error by refetching
      fetchScheduleData()
      toast.error(error.message || 'Failed to update task status')
    }
  }

  const handleTaskInlineUpdate = async (taskId: string, updates: Partial<Task>) => {
    try {
      // Optimistic update - update local state immediately
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === taskId
            ? { ...t, ...updates, updated_at: new Date().toISOString() }
            : t
        )
      )

      await tasksApi.update(taskId, updates)
      toast.success('Task updated successfully')
    } catch (error: any) {
      // Revert optimistic update on error by refetching
      fetchScheduleData()
      toast.error(error.message || 'Failed to update task')
    }
  }

  const handleDeleteTask = async (task: Task) => {
    try {
      // Optimistic update - remove from local state immediately
      setTasks(prevTasks => prevTasks.filter(t => t.id !== task.id))

      await tasksApi.delete(task.id)
      toast.success('Task deleted successfully')
    } catch (error: any) {
      // Revert optimistic update on error by refetching
      fetchScheduleData()
      toast.error(error.message || 'Failed to delete task')
    }
  }

  const handleCompleteSchedule = async (completionData: ScheduleCompletionData) => {
    if (!completingSchedule) return

    try {
      const updates = {
        status: 'completed',
        actual_date: new Date().toISOString(),
        device_used: completionData.device_used,
        battery_count: completionData.battery_count,
        shoot_start_time: completionData.shoot_start_time ? `${new Date().toISOString().split('T')[0]}T${completionData.shoot_start_time}:00` : undefined,
        shoot_end_time: completionData.shoot_end_time ? `${new Date().toISOString().split('T')[0]}T${completionData.shoot_end_time}:00` : undefined,
        completion_notes: completionData.completion_notes
      }

      // Complete the schedule first
      await schedulesApi.update(completingSchedule.id, updates as any)

      // Then complete all schedule-related tasks
      await projectsApi.completeShootTasks(completingSchedule.id)

      toast.success('Schedule and all related tasks completed successfully')
      setCompletingSchedule(null)
      fetchScheduleData()
    } catch (error: any) {
      toast.error(error.message || 'Failed to complete schedule')
    }
  }



  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-5 h-5 text-blue-500" />
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'rescheduled':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />
      default:
        return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'text-blue-600 bg-blue-100 border-blue-200'
      case 'completed':
        return 'text-green-600 bg-green-100 border-green-200'
      case 'cancelled':
        return 'text-red-600 bg-red-100 border-red-200'
      case 'rescheduled':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading schedule details...</p>
        </div>
      </div>
    )
  }

  if (!schedule) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Schedule Not Found</h2>
        <p className="text-gray-600 mb-4">The schedule you're looking for doesn't exist or has been deleted.</p>
        <Button onClick={() => router.push('/schedules')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Schedules
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      {/* Centered Header */}
      <div className="flex items-center justify-center relative">
        {/* Back button left-aligned, positioned absolutely to avoid affecting centering */}
        <div className="absolute left-0">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              if (typeof window !== 'undefined') {
                // Robust back behavior with multiple fallbacks:
                // 1) If referrer is a projects page for this project -> go there
                // 2) If URL has ?from=projects and we have project_id -> go to project details
                // 3) If history length suggests a prior page -> router.back()
                // 4) Else default to /schedules
                const url = new URL(window.location.href)
                const from = url.searchParams.get('from')
                const ref = document.referrer || ''
                const projectId = schedule?.project_id

                const isProjectsRef = ref.includes('/projects/')
                const hasProjectHint = from === 'projects' || isProjectsRef

                if (hasProjectHint && projectId) {
                  router.push(`/projects/${projectId}`)
                  return
                }

                // Use history back only if referrer exists and is same-origin (prevents noop on direct opens)
                const sameOriginRef = ref && ref.startsWith(window.location.origin)
                if (sameOriginRef && window.history.length > 1) {
                  router.back()
                  return
                }

                // Final fallback
                router.push('/schedules')
              } else {
                router.push('/schedules')
              }
            }}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>

        {/* Center content */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-2">
            <h1 className="text-2xl font-bold text-gray-900">{schedule.project?.name}</h1>
            {schedule.custom_id && (
              <span
                className="px-2 py-0.5 rounded border border-border text-[10px] text-muted-foreground bg-muted/50"
                title="Schedule ID"
              >
                {schedule.custom_id}
              </span>
            )}
          </div>
          <p className="text-gray-600">{schedule.project?.client?.name}</p>
        </div>

        {/* Right actions, positioned absolutely to balance the header visually */}
        <div className="absolute right-0 flex items-center space-x-2">
          <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(schedule.status)}`}>
            {getStatusIcon(schedule.status)}
            <span className="ml-2 capitalize">{schedule.status}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditModalOpen(true)}
          >
            <Edit className="w-4 h-4 mr-2" />
            Edit Schedule
          </Button>
        </div>
      </div>

      {/* Schedule Details Card */}
      <div className="bg-card border border-border rounded-lg p-6">
        <h2 className="text-lg font-semibold text-foreground mb-4">Schedule Information</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Date & Time */}
          <div className="space-y-3">
            <div className="flex items-center text-sm text-muted-foreground">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Scheduled Date</span>
            </div>
            <p className="font-medium">
              {new Date(schedule.scheduled_date).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
            <p className="text-sm text-muted-foreground">
              {new Date(schedule.scheduled_date).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>

          {/* Pilot */}
          {schedule.pilot && (
            <div className="space-y-3">
              <div className="flex items-center text-sm text-muted-foreground">
                <User className="w-4 h-4 mr-2" />
                <span>Assigned Pilot</span>
              </div>
              <p className="font-medium">{schedule.pilot.name}</p>
              <p className="text-sm text-muted-foreground">{schedule.pilot.email}</p>
            </div>
          )}

          {/* Location */}
          {schedule.location && (
            <div className="space-y-3">
              <div className="flex items-center text-sm text-muted-foreground">
                <MapPin className="w-4 h-4 mr-2" />
                <span>Location</span>
              </div>
              <p className="font-medium">{schedule.location}</p>
              {schedule.google_maps_link && (
                <a
                  href={schedule.google_maps_link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  View on Maps
                </a>
              )}
            </div>
          )}

          {/* Amount */}
          <div className="space-y-3">
            <div className="flex items-center text-sm text-muted-foreground">
              <span>Amount</span>
            </div>
            <p className="font-medium text-lg">₹{schedule.amount.toLocaleString()}</p>
          </div>

          {/* Completion Details */}
          {schedule.status === 'completed' && (
            <>
              {schedule.device_used && (
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Zap className="w-4 h-4 mr-2" />
                    <span>Device Used</span>
                  </div>
                  <p className="font-medium">{schedule.device_used}</p>
                </div>
              )}

              {schedule.battery_count && (
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Battery className="w-4 h-4 mr-2" />
                    <span>Batteries Used</span>
                  </div>
                  <p className="font-medium">{schedule.battery_count}</p>
                </div>
              )}
            </>
          )}
        </div>

        {/* Notes */}
        {schedule.notes && (
          <div className="mt-6 pt-6 border-t border-border">
            <div className="flex items-center text-sm text-muted-foreground mb-2">
              <FileText className="w-4 h-4 mr-2" />
              <span>Notes</span>
            </div>
            <p className="text-foreground">{schedule.notes}</p>
          </div>
        )}

        {/* Completion Notes */}
        {schedule.completion_notes && (
          <div className="mt-4">
            <div className="flex items-center text-sm text-muted-foreground mb-2">
              <CheckCircle className="w-4 h-4 mr-2" />
              <span>Completion Notes</span>
            </div>
            <p className="text-foreground">{schedule.completion_notes}</p>
          </div>
        )}
      </div>

      {/* Outsourcing Details */}
      {Array.isArray(schedule.vendors) && schedule.vendors.length > 0 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-foreground">Outsourcing Details</h2>
            <p className="text-sm text-muted-foreground">
              Vendors and costs associated with this schedule
            </p>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between text-sm font-medium">
              <span>Total Outsourcing Cost</span>
              <span>
                ₹
                {schedule.vendors
                  .reduce((sum, v) => sum + (Number(v.cost) || 0), 0)
                  .toLocaleString()}
              </span>
            </div>
            <div className="border-t border-border pt-3 space-y-2">
              {schedule.vendors.map((sv) => (
                <div key={sv.id} className="flex items-center justify-between">
                  <div className="min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium truncate">{sv.vendor?.name || 'Vendor'}</span>
                      {sv.vendor?.specialization && (
                        <span className="px-2 py-0.5 rounded border border-border text-[10px] text-muted-foreground bg-muted/50">
                          {sv.vendor.specialization}
                        </span>
                      )}
                    </div>
                    {sv.notes && (
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2">{sv.notes}</p>
                    )}
                  </div>
                  <div className="text-sm font-semibold whitespace-nowrap">
                    ₹{(Number(sv.cost) || 0).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Tasks Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-lg font-semibold text-foreground">Related Tasks</h2>
            <p className="text-sm text-muted-foreground">
              Tasks for this schedule and project-level tasks
            </p>
          </div>
          {(user?.role === 'admin' || user?.role === 'manager') && (
            <Button
              onClick={() => setIsAddTaskModalOpen(true)}
              size="sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </Button>
          )}
        </div>

        {/* Only show tasks section for admin and accounts roles */}
        {(user?.role === 'admin' || user?.role === 'manager') ? (
          tasks.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No tasks found for this schedule or project.</p>
              <p className="text-sm text-muted-foreground mt-2">
                Add tasks to track specific activities for this schedule.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {tasks.map((task: Task) => (
                <RedesignedTaskCard
                  key={task.id}
                  task={task}
                  users={users}
                  onDelete={handleDeleteTask}
                  onStatusChange={handleTaskStatusChange}
                  onInlineUpdate={handleTaskInlineUpdate}
                  onCompleteSchedule={setCompletingSchedule}
                  schedule={schedule}
                  compact={true}
                  showRoleFilter={false} // No role filtering needed since only admin/accounts can see this
                  currentUserRole={user?.role}
                />
              ))}
            </div>
          )
        ) : (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Task management is available for admin and accounts roles only.</p>
            <p className="text-sm text-muted-foreground mt-2">
              Contact your administrator for task-related queries.
            </p>
          </div>
        )}
      </div>

      {/* Project Expenses (recent) */}
      <ProjectExpenses scheduleProjectId={schedule.project_id} />

      {/* Edit Schedule Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Schedule"
        size="xl"
      >
        <ScheduleForm
          schedule={schedule}
          onSuccess={handleEditSuccess}
          onCancel={() => setIsEditModalOpen(false)}
        />
      </Modal>

      {/* Add Task Modal */}
      <Modal
        isOpen={isAddTaskModalOpen}
        onClose={() => setIsAddTaskModalOpen(false)}
        title="Add Schedule Task"
        size="lg"
      >
        <TaskForm
          onSuccess={handleAddTaskSuccess}
          onCancel={() => setIsAddTaskModalOpen(false)}
          preselectedProjectId={schedule.project_id}
          preselectedShootId={schedule.id}
        />
      </Modal>

      {/* Schedule Completion Form */}
      <ScheduleCompletionForm
        schedule={completingSchedule}
        shootTask={tasks.find(task => task.title.toLowerCase() === 'shoot')}
        isOpen={!!completingSchedule}
        onClose={() => setCompletingSchedule(null)}
        onComplete={handleCompleteSchedule}
      />

    </div>
  )
}

/**
 * Lightweight expenses widget for schedule page.
 * Shows recent project-level expenses (top 5) in compact cards.
 * Note: Expense model is project-scoped currently.
 */
function ProjectExpenses({ scheduleProjectId }: { scheduleProjectId: string }) {
  const [expenses, setExpenses] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    let mounted = true
    async function load() {
      try {
        setLoading(true)
        const { expensesApi } = await import('@/lib/api')
        const res = await expensesApi.getByProjectId(scheduleProjectId)
        if (!mounted) return
        setExpenses((res || []).slice(0, 5))
      } catch (e) {
        console.error('Failed to load project expenses:', e)
      } finally {
        if (mounted) setLoading(false)
      }
    }
    if (scheduleProjectId) load()
    return () => {
      mounted = false
    }
  }, [scheduleProjectId])

  if (loading) {
    return (
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-foreground">Recent Expenses</h2>
        </div>
        <p className="text-sm text-muted-foreground">Loading expenses...</p>
      </div>
    )
  }

  if (!expenses || expenses.length === 0) {
    return (
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-foreground">Recent Expenses</h2>
        </div>
        <p className="text-sm text-muted-foreground">No recent expenses for this project.</p>
      </div>
    )
  }

  const total = expenses.reduce((sum, e) => sum + (Number(e.amount) || 0), 0)

  const ExpenseCard = require('@/components/ui/expense-card').ExpenseCard

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-lg font-semibold text-foreground">Recent Expenses</h2>
          <p className="text-sm text-muted-foreground">Top 5 latest expenses for this project</p>
        </div>
        <div className="text-sm font-medium">
          Total: ₹{total.toLocaleString()}
        </div>
      </div>
      <div className="space-y-3">
        {expenses.map((exp) => (
          <ExpenseCard key={exp.id} expense={exp} compact />
        ))}
      </div>
    </div>
  )
}