import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Server-side client creation started:', body)

    // Create Supabase client with service role key for server-side operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Insert the client into the database
    const { data, error } = await supabase
      .from('clients')
      .insert(body)
      .select('id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link, created_at, updated_at')
      .single()

    if (error) {
      console.error('Database error during client creation:', error)
      throw error
    }

    console.log('Client created in database:', data)

    // Queue SharePoint folder creation as background job
    try {
      const { queueSharePointFolderCreation } = await import('../../../lib/background-jobs')
      
      const jobId = queueSharePointFolderCreation('client', data.id, {
        customId: data.custom_id,
        name: data.name
      })
      
      console.log('SharePoint folder creation queued for client:', {
        clientId: data.id,
        customId: data.custom_id,
        name: data.name,
        jobId
      })
      
      // Add job ID to response for tracking
      ;(data as any).sharepoint_job_id = jobId
    } catch (jobError) {
      console.error('Failed to queue SharePoint folder creation for client:', {
        clientId: data.id,
        customId: data.custom_id,
        name: data.name,
        error: jobError instanceof Error ? jobError.message : jobError
      })
      // Don't fail the client creation if job queuing fails
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in client creation API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create client' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    const { data, error } = await supabase
      .from('clients')
      .select(`
        id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link, created_at, updated_at,
        contact_persons (*)
      `)
      .order('name')

    if (error) throw error
    
    return NextResponse.json(data || [])
  } catch (error) {
    console.error('Error fetching clients:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch clients' },
      { status: 500 }
    )
  }
}