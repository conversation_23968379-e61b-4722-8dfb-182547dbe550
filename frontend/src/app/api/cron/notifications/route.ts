import { NextRequest, NextResponse } from 'next/server'
import { queueNotificationTriggers } from '@/lib/notification-triggers'

/**
 * Cron endpoint for automated notification triggers
 * This should be called by external cron services (like Vercel Cron or GitHub Actions)
 */
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (optional)
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const triggerType = searchParams.get('type') as 'payment_overdue' | 'schedule_reminders' | 'all' || 'all'
    const dryRun = searchParams.get('dryRun') === 'true'

    console.log(`🔔 Cron job triggered for notification type: ${triggerType}`)

    // Queue the notification triggers as background jobs for better performance
    const jobId = queueNotificationTriggers(triggerType, { dryRun })

    return NextResponse.json({
      success: true,
      message: `Notification triggers queued successfully`,
      jobId,
      triggerType,
      dryRun
    })

  } catch (error) {
    console.error('❌ Error in notification cron job:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to queue notification triggers',
        success: false
      },
      { status: 500 }
    )
  }
}

/**
 * GET endpoint for manual testing
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const triggerType = searchParams.get('type') as 'payment_overdue' | 'schedule_reminders' | 'all' || 'all'
    const dryRun = searchParams.get('dryRun') === 'true'

    console.log(`🔔 Manual trigger for notification type: ${triggerType}`)

    // Queue the notification triggers
    const jobId = queueNotificationTriggers(triggerType, { dryRun })

    return NextResponse.json({
      success: true,
      message: `Notification triggers queued successfully (manual trigger)`,
      jobId,
      triggerType,
      dryRun,
      note: 'This is a manual trigger for testing. In production, use POST with proper authentication.'
    })

  } catch (error) {
    console.error('❌ Error in manual notification trigger:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to queue notification triggers',
        success: false
      },
      { status: 500 }
    )
  }
}
