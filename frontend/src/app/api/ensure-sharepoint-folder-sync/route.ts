import { NextRequest, NextResponse } from 'next/server'
import { queueSharePointFolderCreation } from '@/lib/background-jobs'

/**
 * API endpoint for synchronous SharePoint folder creation
 * This endpoint executes SharePoint folder creation immediately (synchronously)
 * instead of queuing it as a background job.
 * 
 * Useful for:
 * - Testing and debugging
 * - Critical operations that need immediate completion
 * - Fallback when background job system is unreliable
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { scheduleId, entityType = 'schedule', entityId, payload } = body

    // Validate required parameters
    if (!scheduleId && !entityId) {
      return NextResponse.json(
        { error: 'Either scheduleId or entityId is required' },
        { status: 400 }
      )
    }

    const finalEntityId = entityId || scheduleId
    const finalPayload = payload || { scheduleId: finalEntityId }

    console.log(`🔄 Synchronous SharePoint folder creation requested for ${entityType}:${finalEntityId}`)

    // Execute SharePoint folder creation synchronously
    const result = await queueSharePointFolderCreation(
      entityType as 'client' | 'project' | 'schedule',
      finalEntityId,
      finalPayload,
      {
        executeSync: true, // Force synchronous execution
        fallbackToSync: false // No fallback needed since we're already sync
      }
    )

    console.log(`✅ Synchronous SharePoint folder creation completed for ${entityType}:${finalEntityId}`)

    return NextResponse.json({
      success: true,
      message: 'SharePoint folder creation completed synchronously',
      entityType,
      entityId: finalEntityId,
      executionType: 'synchronous',
      result
    })

  } catch (error) {
    console.error('❌ Synchronous SharePoint folder creation failed:', error)
    
    return NextResponse.json(
      {
        error: 'SharePoint folder creation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        details: error
      },
      { status: 500 }
    )
  }
}

/**
 * GET endpoint to check if the API is available
 */
export async function GET() {
  return NextResponse.json({
    message: 'Synchronous SharePoint folder creation API is available',
    usage: 'POST with { scheduleId: "uuid" } or { entityType: "schedule", entityId: "uuid", payload: {...} }'
  })
}