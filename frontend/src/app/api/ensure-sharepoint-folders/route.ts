import { NextResponse } from 'next/server'
import { SharePointService } from '@/lib/sharepoint-service'

export async function POST() {
  try {
    console.log('🚀 Starting SharePoint folder creation for all schedules...')
    
    const result = await SharePointService.ensureAllScheduleFolders()
    
    return NextResponse.json({
      success: true,
      message: `SharePoint folder creation completed: ${result.success} success, ${result.failed} failed`,
      data: result
    })
  } catch (error) {
    console.error('❌ Failed to ensure SharePoint folders:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to ensure SharePoint folders',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST to trigger SharePoint folder creation for all schedules'
  })
}