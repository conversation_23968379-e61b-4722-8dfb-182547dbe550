import { NextResponse } from 'next/server'
import { initializeBackgroundJobs } from '@/lib/init-background-jobs'

export async function GET() {
  try {
    console.log('🔧 API init route called')
    initializeBackgroundJobs()
    return NextResponse.json({ status: 'Background jobs initialized' })
  } catch (error) {
    console.error('Failed to initialize background jobs via API:', error)
    return NextResponse.json({ error: 'Failed to initialize' }, { status: 500 })
  }
}