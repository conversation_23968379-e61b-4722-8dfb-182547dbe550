import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'
import type { NotificationExportOptions } from '@/types/notifications'
import type { UserRole } from '@/types'

/**
 * Export notifications to CSV (Admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user profile to check role
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    const userRole = userProfile.role as UserRole

    // Only admins can export notifications
    if (userRole !== 'admin') {
      return NextResponse.json({ error: 'Access denied. Admin role required.' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const exportOptions: NotificationExportOptions = {
      start_date: searchParams.get('start_date') || undefined,
      end_date: searchParams.get('end_date') || undefined,
      types: searchParams.get('types')?.split(',') as any[] || undefined,
      categories: searchParams.get('categories')?.split(',') as any[] || undefined,
      user_ids: searchParams.get('user_ids')?.split(',') || undefined,
      include_read: searchParams.get('include_read') !== 'false' // Default to true
    }

    // Build query with filters
    let query = supabase
      .from('notifications')
      .select(`
        id,
        user_id,
        title,
        message,
        type,
        category,
        priority,
        read,
        action_url,
        action_label,
        created_at,
        read_at,
        expires_at,
        metadata,
        users!inner(name, email, role)
      `)

    // Apply date filters
    if (exportOptions.start_date) {
      query = query.gte('created_at', exportOptions.start_date)
    }
    if (exportOptions.end_date) {
      query = query.lte('created_at', exportOptions.end_date)
    }

    // Apply type filters
    if (exportOptions.types && exportOptions.types.length > 0) {
      query = query.in('type', exportOptions.types)
    }

    // Apply category filters
    if (exportOptions.categories && exportOptions.categories.length > 0) {
      query = query.in('category', exportOptions.categories)
    }

    // Apply user filters
    if (exportOptions.user_ids && exportOptions.user_ids.length > 0) {
      query = query.in('user_id', exportOptions.user_ids)
    }

    // Apply read status filter
    if (!exportOptions.include_read) {
      query = query.eq('read', false)
    }

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false })

    const { data: notifications, error } = await query

    if (error) {
      console.error('Error fetching notifications for export:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Generate CSV content
    const csvHeaders = [
      'ID',
      'User Name',
      'User Email',
      'User Role',
      'Title',
      'Message',
      'Type',
      'Category',
      'Priority',
      'Read',
      'Action URL',
      'Action Label',
      'Created At',
      'Read At',
      'Expires At',
      'Metadata'
    ]

    const csvRows = notifications?.map(notification => [
      notification.id,
      (notification.users as any)?.name || '',
      (notification.users as any)?.email || '',
      (notification.users as any)?.role || '',
      `"${notification.title.replace(/"/g, '""')}"`, // Escape quotes
      `"${notification.message.replace(/"/g, '""')}"`, // Escape quotes
      notification.type,
      notification.category,
      notification.priority,
      notification.read ? 'Yes' : 'No',
      notification.action_url || '',
      notification.action_label || '',
      notification.created_at,
      notification.read_at || '',
      notification.expires_at || '',
      notification.metadata ? `"${JSON.stringify(notification.metadata).replace(/"/g, '""')}"` : ''
    ]) || []

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.join(','))
    ].join('\n')

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `notifications_export_${timestamp}.csv`

    // Return CSV file
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache'
      }
    })

  } catch (error) {
    console.error('Error in notifications export:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Get export statistics (for UI)
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user profile to check role
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    const userRole = userProfile.role as UserRole

    // Only admins can access export statistics
    if (userRole !== 'admin') {
      return NextResponse.json({ error: 'Access denied. Admin role required.' }, { status: 403 })
    }

    const exportOptions: NotificationExportOptions = await request.json()

    // Build query with filters (same as GET but just count)
    let query = supabase
      .from('notifications')
      .select('id, type, category, read, created_at', { count: 'exact' })

    // Apply filters (same logic as GET)
    if (exportOptions.start_date) {
      query = query.gte('created_at', exportOptions.start_date)
    }
    if (exportOptions.end_date) {
      query = query.lte('created_at', exportOptions.end_date)
    }
    if (exportOptions.types && exportOptions.types.length > 0) {
      query = query.in('type', exportOptions.types)
    }
    if (exportOptions.categories && exportOptions.categories.length > 0) {
      query = query.in('category', exportOptions.categories)
    }
    if (exportOptions.user_ids && exportOptions.user_ids.length > 0) {
      query = query.in('user_id', exportOptions.user_ids)
    }
    if (!exportOptions.include_read) {
      query = query.eq('read', false)
    }

    const { data: notifications, error, count } = await query

    if (error) {
      console.error('Error fetching export statistics:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Calculate statistics
    const stats = {
      total_count: count || 0,
      unread_count: notifications?.filter(n => !n.read).length || 0,
      by_type: notifications?.reduce((acc, n) => {
        acc[n.type] = (acc[n.type] || 0) + 1
        return acc
      }, {} as Record<string, number>) || {},
      by_category: notifications?.reduce((acc, n) => {
        acc[n.category] = (acc[n.category] || 0) + 1
        return acc
      }, {} as Record<string, number>) || {},
      date_range: {
        earliest: notifications?.length ? notifications[notifications.length - 1]?.created_at : null,
        latest: notifications?.length ? notifications[0]?.created_at : null
      }
    }

    return NextResponse.json({
      success: true,
      statistics: stats,
      estimated_file_size: `${Math.round((count || 0) * 0.5)}KB` // Rough estimate
    })

  } catch (error) {
    console.error('Error in export statistics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
