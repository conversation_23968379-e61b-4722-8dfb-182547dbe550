import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  // COMPLETELY DISABLED TO STOP INFINITE LOOP
  console.log('🚫 Notifications API called - PERMANENTLY DISABLED')
  console.log('🚫 Request URL:', request.url)
  
  return NextResponse.json({ 
    error: 'Notifications API disabled',
    message: 'This endpoint is disabled to prevent infinite loops. Use event-driven approach instead.',
    notifications: [],
    stats: {
      total: 0,
      unread: 0,
      by_type: {},
      by_category: {},
      by_priority: {}
    }
  }, { status: 200 }) // Return 200 to prevent error loops
}

export async function POST(request: NextRequest) {
  // COMPLETELY DISABLED TO STOP INFINITE LOOP
  console.log('🚫 Notifications POST API called - PERMANENTLY DISABLED')
  
  return NextResponse.json({ 
    error: 'Notifications POST API disabled',
    message: 'This endpoint is disabled to prevent infinite loops.'
  }, { status: 200 })
}
