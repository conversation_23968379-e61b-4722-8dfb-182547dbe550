import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'
import type { NotificationStats } from '@/types/notifications'
import type { UserRole } from '@/types'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get user profile to determine role
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .single()

    if (profileError) {
      console.error('Error fetching user profile:', profileError)
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const userRole = userProfile.role as UserRole
    const isAdmin = userRole === 'admin'
    const isManager = userRole === 'manager'

    // Build query based on user role
    let notificationsQuery = supabase
      .from('notifications')
      .select('*', { count: 'exact' })

    // Role-based filtering
    if (!isAdmin && !isManager) {
      // Regular users only see their own notifications
      notificationsQuery = notificationsQuery.eq('user_id', (await supabase.auth.getUser()).data.user?.id)
    }

    const { data: notifications, error, count } = await notificationsQuery

    if (error) {
      console.error('Error fetching notifications:', error)
      return NextResponse.json(
        { error: 'Failed to fetch notifications' },
        { status: 500 }
      )
    }

    // Calculate stats
    const stats: NotificationStats = {
      total: count || 0,
      unread: notifications?.filter(n => !n.read).length || 0,
      by_type: {
        error: notifications?.filter(n => n.type === 'error').length || 0,
        payment: notifications?.filter(n => n.type === 'payment').length || 0,
        schedule: notifications?.filter(n => n.type === 'schedule').length || 0,
        task: notifications?.filter(n => n.type === 'task').length || 0,
        system: notifications?.filter(n => n.type === 'system').length || 0,
        info: notifications?.filter(n => n.type === 'info').length || 0,
        success: notifications?.filter(n => n.type === 'success').length || 0,
        project: notifications?.filter(n => n.type === 'project').length || 0,
        warning: notifications?.filter(n => n.type === 'warning').length || 0
      },
      by_category: {
        task_assigned: notifications?.filter(n => n.category === 'task_assigned').length || 0,
        task_due: notifications?.filter(n => n.category === 'task_due').length || 0,
        task_overdue: notifications?.filter(n => n.category === 'task_overdue').length || 0,
        task_completed: notifications?.filter(n => n.category === 'task_completed').length || 0,
        project_created: notifications?.filter(n => n.category === 'project_created').length || 0,
        project_updated: notifications?.filter(n => n.category === 'project_updated').length || 0,
        schedule_upcoming: notifications?.filter(n => n.category === 'schedule_upcoming').length || 0,
        schedule_changed: notifications?.filter(n => n.category === 'schedule_changed').length || 0,
        payment_received: notifications?.filter(n => n.category === 'payment_received').length || 0,
        payment_overdue: notifications?.filter(n => n.category === 'payment_overdue').length || 0,
        system_update: notifications?.filter(n => n.category === 'system_update').length || 0,
        user_mention: notifications?.filter(n => n.category === 'user_mention').length || 0,
        deadline_reminder: notifications?.filter(n => n.category === 'deadline_reminder').length || 0,
        general: notifications?.filter(n => n.category === 'general').length || 0
      },
      by_priority: {
        low: notifications?.filter(n => n.priority === 'low').length || 0,
        medium: notifications?.filter(n => n.priority === 'medium').length || 0,
        high: notifications?.filter(n => n.priority === 'high').length || 0,
        urgent: notifications?.filter(n => n.priority === 'urgent').length || 0
      }
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching notification stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}