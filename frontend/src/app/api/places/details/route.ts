import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const placeId = searchParams.get('place_id')

    console.log('Places details request:', { placeId })

    if (!placeId) {
      return NextResponse.json(
        { error: 'place_id is required' },
        { status: 400 }
      )
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
    if (!apiKey) {
      console.error('Google Maps API key not found in environment variables')
      return NextResponse.json(
        { error: 'Google Maps API key not configured' },
        { status: 500 }
      )
    }

    // Try the new Places API format first
    const newApiUrl = `https://places.googleapis.com/v1/${placeId}`

    console.log('Making request to Google Places Details API (New)')

    const newApiResponse = await fetch(newApiUrl, {
      method: 'GET',
      headers: {
        'X-Goog-Api-Key': apiKey,
        'X-Goog-FieldMask': 'id,displayName,formattedAddress,addressComponents,location'
      }
    })

    if (newApiResponse.ok) {
      const newData = await newApiResponse.json()
      console.log('New Places Details API response:', { has_data: !!newData.id })

      if (newData.id) {
        let placeName = newData.displayName?.text || ''
        let city = ''

        // Extract city from address components
        if (newData.addressComponents) {
          const locality = newData.addressComponents.find((comp: any) =>
            comp.types.includes('locality') ||
            comp.types.includes('administrative_area_level_2')
          )

          if (locality) {
            city = locality.longText
          }
        }

        const formattedLocation = newData.formattedAddress || (placeName && city ? `${placeName}, ${city}` : placeName || city)

        return NextResponse.json({
          placeName,
          city,
          formattedLocation,
          coordinates: newData.location ? {
            lat: newData.location.latitude,
            lng: newData.location.longitude
          } : undefined
        })
      }
    }

    // Fallback to legacy API
    const legacyApiUrl = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=name,formatted_address,address_components,geometry&key=${apiKey}`

    console.log('Falling back to legacy Places Details API')

    const response = await fetch(legacyApiUrl)

    console.log('Google Places Details API response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Places Details API HTTP error:', response.status, errorText)
      return NextResponse.json(
        { error: `Places API HTTP error: ${response.status}` },
        { status: response.status }
      )
    }

    const data = await response.json()
    console.log('Google Places Details API response:', { status: data.status, has_result: !!data.result })

    if (data.status === 'OK' && data.result) {
      const result = data.result
      const addressComponents = result.address_components || []

      let placeName = result.name || ''
      let city = ''

      // Extract city from address components
      const locality = addressComponents.find((comp: any) =>
        comp.types.includes('locality') ||
        comp.types.includes('administrative_area_level_2')
      )

      if (locality) {
        city = locality.long_name
      }

      const formattedLocation = result.formatted_address || (placeName && city ? `${placeName}, ${city}` : placeName || city)

      return NextResponse.json({
        placeName,
        city,
        formattedLocation,
        coordinates: result.geometry?.location ? {
          lat: result.geometry.location.lat,
          lng: result.geometry.location.lng
        } : undefined
      })
    } else {
      console.error('Places Details API error:', data.status, data.error_message)
      return NextResponse.json(
        { error: `Places API error: ${data.status} - ${data.error_message || 'Unknown error'}` },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Error in places details API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
