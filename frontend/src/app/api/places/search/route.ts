import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('query')

    console.log('Places search request:', { query })

    if (!query || query.trim().length < 2) {
      return NextResponse.json({ predictions: [] })
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
    if (!apiKey) {
      console.error('Google Maps API key not found in environment variables')
      return NextResponse.json(
        { error: 'Google Maps API key not configured' },
        { status: 500 }
      )
    }

    // Try the new Places API (Text Search) format first
    const newApiUrl = `https://places.googleapis.com/v1/places:searchText`

    console.log('Making request to Google Places API (New):', newApiUrl)

    const newApiResponse = await fetch(newApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Goog-Api-Key': apiKey,
        'X-Goog-FieldMask': 'places.id,places.displayName,places.formattedAddress,places.location'
      },
      body: JSON.stringify({
        textQuery: query,
        maxResultCount: 10
      })
    })

    if (newApiResponse.ok) {
      const newData = await newApiResponse.json()
      console.log('New Places API response:', { places_count: newData.places?.length || 0 })

      if (newData.places && newData.places.length > 0) {
        return NextResponse.json({
          predictions: newData.places.map((place: any) => ({
            place_id: place.id,
            description: place.formattedAddress || place.displayName?.text || 'Unknown location',
            structured_formatting: {
              main_text: place.displayName?.text || 'Unknown',
              secondary_text: place.formattedAddress || ''
            },
            types: ['establishment']
          }))
        })
      }
    }

    // Fallback to legacy API if new API doesn't work
    const legacyApiUrl = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(query)}&key=${apiKey}`

    console.log('Falling back to legacy Places API:', legacyApiUrl.replace(apiKey, 'API_KEY_HIDDEN'))

    const response = await fetch(legacyApiUrl)

    console.log('Google Places API response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Places API HTTP error:', response.status, errorText)
      return NextResponse.json(
        { error: `Places API HTTP error: ${response.status}` },
        { status: response.status }
      )
    }

    const data = await response.json()
    console.log('Google Places API response:', { status: data.status, predictions_count: data.predictions?.length || 0 })

    if (data.status === 'OK') {
      return NextResponse.json({
        predictions: data.predictions.map((prediction: any) => ({
          place_id: prediction.place_id,
          description: prediction.description,
          structured_formatting: prediction.structured_formatting,
          types: prediction.types
        }))
      })
    } else if (data.status === 'ZERO_RESULTS') {
      return NextResponse.json({ predictions: [] })
    } else {
      console.error('Places API error:', data.status, data.error_message)
      return NextResponse.json(
        { error: `Places API error: ${data.status} - ${data.error_message || 'Unknown error'}` },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Error in places search API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
