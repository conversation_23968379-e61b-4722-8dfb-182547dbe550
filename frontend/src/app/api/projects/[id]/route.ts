import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error('Unauthorized delete attempt:', authError)
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: projectId } = await params
    console.log('🗑️ Deleting project:', projectId, 'by user:', user.email)

    // Check if user has permission to delete (admin or manager)
    if (user.user_metadata?.role !== 'admin' && user.user_metadata?.role !== 'manager') {
      console.error('Insufficient permissions for delete:', user.user_metadata?.role)
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Check if project exists and get its details
    const { data: project, error: fetchError } = await supabase
      .from('projects')
      .select('id, name, custom_id')
      .eq('id', projectId)
      .single()

    if (fetchError) {
      console.error('Error fetching project for deletion:', fetchError)
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      )
    }

    console.log('Project to delete:', project)

    // Delete the project (this should cascade to related records)
    const { error: deleteError } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId)

    if (deleteError) {
      console.error('❌ Error deleting project:', deleteError)
      return NextResponse.json(
        { error: deleteError.message },
        { status: 500 }
      )
    }

    console.log('✅ Project deleted successfully:', project.name)

    return NextResponse.json({ 
      success: true,
      message: `Project "${project.name}" deleted successfully`
    })

  } catch (error) {
    console.error('❌ Error in project delete API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete project' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: projectId } = await params

    const { data: project, error } = await supabase
      .from('projects')
      .select(`
        id, custom_id, name, description, client_id, location, google_maps_link, status, total_amount, gst_inclusive, amount_received, amount_pending, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date, vendor_payment_date, vendor_payment_notes, created_at, updated_at,
        client:clients(id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, created_at, updated_at),
        contact_person:contact_persons(id, name, phone, email),
        schedules:schedules(
          id, custom_id, scheduled_date, scheduled_end_date, actual_date, status, pilot_id, amount, location, google_maps_link, notes, weather_conditions, onedrive_folder_path, is_recurring, recurring_pattern, is_outsourced, device_used, battery_count, shoot_start_time, shoot_end_time, completion_notes, created_at, updated_at,
          pilot:users(id, name, email, role),
          vendors:schedule_vendors(
            id, cost, notes,
            vendor:outsourcing_vendors(id, name, specialization, contact_person, phone, email)
          )
        )
      `)
      .eq('id', projectId)
      .single()

    if (error) {
      console.error('Error fetching project:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    return NextResponse.json(project)

  } catch (error) {
    console.error('Error in project GET API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch project' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: projectId } = await params
    const updates = await request.json()

    console.log('🔄 Updating project:', projectId, 'with:', Object.keys(updates))

    const { data: project, error } = await supabase
      .from('projects')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId)
      .select(`
        id, custom_id, name, description, client_id, location, google_maps_link, status, total_amount, gst_inclusive, amount_received, amount_pending, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date, vendor_payment_date, vendor_payment_notes, created_at, updated_at,
        client:clients(id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, created_at, updated_at),
        contact_person:contact_persons(id, name, phone, email)
      `)
      .single()

    if (error) {
      console.error('❌ Error updating project:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log('✅ Project updated successfully:', project.name)

    return NextResponse.json(project)

  } catch (error) {
    console.error('❌ Error in project update API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update project' },
      { status: 500 }
    )
  }
}
