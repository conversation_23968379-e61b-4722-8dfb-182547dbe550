import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'
import { syncTaskDueDatesWithSchedule, shouldSyncTaskDueDates } from '@/lib/schedule-task-sync'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: scheduleId } = await params
    const updates = await request.json()

    console.log('🔄 Updating schedule:', {
      scheduleId,
      updates: Object.keys(updates)
    })

    // Get the current schedule to compare dates
    const { data: currentSchedule, error: fetchError } = await supabase
      .from('schedules')
      .select('scheduled_date, project_id')
      .eq('id', scheduleId)
      .single()

    if (fetchError) {
      console.error('❌ Error fetching current schedule:', fetchError)
      return NextResponse.json(
        { error: fetchError.message },
        { status: 500 }
      )
    }

    const oldScheduledDate = currentSchedule.scheduled_date
    const newScheduledDate = updates.scheduled_date

    // Update the schedule
    const { data, error } = await supabase
      .from('schedules')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', scheduleId)
      .select(`
        *,
        project:projects(id, custom_id, name, status),
        pilot:users(id, name, email, role),
        vendors:schedule_vendors(
          id,
          cost,
          notes,
          vendor:outsourcing_vendors(id, name, specialization, contact_person, phone, email)
        )
      `)
      .single()

    if (error) {
      console.error('❌ Error updating schedule:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Schedule updated successfully:', data.id)

    // Sync task due dates if the scheduled date changed
    if (newScheduledDate && oldScheduledDate && shouldSyncTaskDueDates(oldScheduledDate, newScheduledDate)) {
      try {
        console.log('🔄 Schedule date changed, syncing task due dates...')
        await syncTaskDueDatesWithSchedule(scheduleId, newScheduledDate, oldScheduledDate)
        console.log('✅ Task due dates synced successfully')
      } catch (syncError) {
        console.error('⚠️ Failed to sync task due dates:', syncError)
        // Don't fail the schedule update if task sync fails
      }
    }

    return NextResponse.json(data)

  } catch (error) {
    console.error('❌ Error in schedule update API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update schedule' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: scheduleId } = await params

    console.log('🗑️ Deleting schedule:', scheduleId)

    // Delete the schedule (this will cascade to related tasks and vendors)
    const { error } = await supabase
      .from('schedules')
      .delete()
      .eq('id', scheduleId)

    if (error) {
      console.error('❌ Error deleting schedule:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Schedule deleted successfully:', scheduleId)

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('❌ Error in schedule delete API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete schedule' },
      { status: 500 }
    )
  }
}
