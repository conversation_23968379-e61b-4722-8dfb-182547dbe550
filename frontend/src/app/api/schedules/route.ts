import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createServerSupabaseClient } from '@/lib/auth-server'
import { projectsApi } from '@/lib/api'
import { createDefaultTasksServerSide } from '@/lib/server-tasks'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    // Use service role client for server-side operations (matching projects API pattern)
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Fetch schedules with project and client information
    const { data: schedules, error } = await supabase
      .from('schedules')
      .select(`
        *,
        project:projects(
          *,
          client:clients(name)
        ),
        pilot:users(id, name, email, role),
        vendors:schedule_vendors(
          id,
          cost,
          notes,
          vendor:outsourcing_vendors(id, name, specialization, contact_person, phone, email)
        )
      `)
      .order('scheduled_date', { ascending: false })

    if (error) {
      console.error('Error fetching schedules:', error)
      return NextResponse.json({
        error: 'Failed to fetch schedules',
        details: error.message
      }, { status: 500 })
    }

    return NextResponse.json(schedules || [])

  } catch (error: any) {
    console.error('API error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabaseAuth = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { schedule, vendors } = await request.json()

    if (!schedule) {
      return NextResponse.json(
        { error: 'Schedule data is required' },
        { status: 400 }
      )
    }

    console.log('🚀 Creating schedule via API endpoint:', {
      project_id: schedule.project_id,
      scheduled_date: schedule.scheduled_date,
      location: schedule.location,
      schedule_type: schedule.schedule_type,
      user_id: user.id
    })

    // Use service role client for server-side operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Normalize vendors payload
    const normalizedVendors = Array.isArray(vendors)
      ? vendors.map((v: any) => ({
          vendor_id: String(v.vendor_id),
          cost: Number(v.cost) || 0,
          notes: v.notes || ''
        }))
      : []

    // Call the PostgreSQL function to create schedule with vendors
    const { data, error } = await supabase.rpc('create_schedule_with_vendors', {
      p_project_id: schedule.project_id,
      p_scheduled_date: schedule.scheduled_date,
      p_scheduled_end_date: schedule.scheduled_end_date,
      p_pilot_id: schedule.pilot_id,
      p_amount: schedule.amount,
      p_location: schedule.location,
      p_google_maps_link: schedule.google_maps_link,
      p_notes: schedule.notes,
      p_is_recurring: schedule.is_recurring,
      p_recurring_pattern: schedule.recurring_pattern,
      p_is_outsourced: !!schedule.is_outsourced,
      p_schedule_type: schedule.schedule_type,
      p_vendors: normalizedVendors,
      p_has_gst: schedule.has_gst || false,
      p_gst_amount: schedule.gst_amount || 0,
      p_total_amount: schedule.total_amount || schedule.amount || 0
    })

    if (error) {
      console.error('❌ Error creating schedule:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    console.log(`✅ Schedule created: ${data.custom_id} (ID: ${data.id})`)

    // Recalculate project total
    try {
      await projectsApi.recalculateProjectTotal(schedule.project_id)
    } catch (projectError) {
      console.error('⚠️ Failed to recalculate project total:', projectError)
      // Don't fail the schedule creation if project total calculation fails
    }

    // Create default tasks with due dates based on schedule date
    try {
      const { data: project } = await supabase
        .from('projects')
        .select(`
          id,
          client_id,
          clients!inner (
            client_type
          )
        `)
        .eq('id', schedule.project_id)
        .single()

      if ((project?.clients as any)?.client_type) {
        console.log('Creating default tasks for new schedule:', {
          projectId: schedule.project_id,
          clientType: (project.clients as any).client_type,
          scheduleDate: schedule.scheduled_date,
          scheduleId: data.id
        })

        // Create tasks directly using service role client instead of projectsApi
        await createDefaultTasksServerSide(
          schedule.project_id,
          (project.clients as any).client_type,
          schedule.scheduled_date,
          data.id, // Pass the schedule ID for shoot-based tasks
          data.schedule_type // Pass the schedule type for schedule-based task templates
        )

        console.log('Default tasks created successfully for schedule:', data.id)
      } else {
        console.log('No client type found, skipping task creation for schedule:', data.id)
      }
    } catch (taskError) {
      console.error('⚠️ Failed to create default tasks for schedule:', taskError)
      console.error('Task creation error details:', {
        message: taskError instanceof Error ? taskError.message : 'Unknown error',
        stack: taskError instanceof Error ? taskError.stack : undefined
      })
      // Don't fail the schedule creation if task creation fails
    }

    // Queue SharePoint folder creation as background job for fast response
    try {
      console.log('Queuing SharePoint folder creation for schedule:', data.id)

      // Ensure background job system is initialized
      const { initializeBackgroundJobs } = await import('@/lib/init-background-jobs')
      initializeBackgroundJobs()

      const { queueSharePointFolderCreation } = await import('@/lib/background-jobs')

      // Always use background processing for fast form submission
      const jobId = await queueSharePointFolderCreation('schedule', data.id, {
        scheduleId: data.id,
        customId: data.custom_id,
        scheduledDate: schedule.scheduled_date
      }, {
        executeSync: false,        // Never execute synchronously for fast response
        fallbackToSync: false      // Don't fallback to sync to keep it fast
      })

      console.log('SharePoint folder creation queued for schedule:', {
        scheduleId: data.id,
        customId: data.custom_id,
        jobId,
        executionType: 'background'
      })
    } catch (jobError) {
      console.error('⚠️ Failed to queue SharePoint folder creation for schedule:', jobError)
      // Don't fail the schedule creation if SharePoint folder creation fails
      // This ensures the user can still create schedules even if SharePoint is down
    }

    // Return the created schedule
    return NextResponse.json(data)

  } catch (error) {
    console.error('❌ Error in schedule creation API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create schedule' },
      { status: 500 }
    )
  }
}
