import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Test: Creating schedule with vendors')
    
    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // First, get an existing project to use for the test
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select('id, name, client_id')
      .limit(1)
    
    if (projectError || !projects || projects.length === 0) {
      console.error('❌ No projects found for test:', projectError)
      return NextResponse.json({
        success: false,
        message: 'No projects available for testing',
        schedule: null
      })
    }
    
    const testProject = projects[0]
    
    // Get existing vendors for the test
    const { data: vendors, error: vendorError } = await supabase
      .from('outsourcing_vendors')
      .select('id, name')
      .limit(2)
    
    if (vendorError) {
      console.error('❌ Error fetching vendors:', vendorError)
      return NextResponse.json({
        success: false,
        message: 'Failed to fetch vendors for testing',
        schedule: null
      })
    }
    
    // Create test schedule data
    const testScheduleData = {
      project_id: testProject.id,
      scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      scheduled_end_date: new Date(Date.now() + 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000).toISOString(), // Tomorrow + 4 hours
      pilot_id: null,
      amount: 5000,
      location: 'Test Location for Schedule Creation',
      google_maps_link: 'https://maps.google.com/test',
      notes: 'Test schedule created via test-createwithvendors API',
      is_recurring: false,
      recurring_pattern: null,
      is_outsourced: vendors && vendors.length > 0
    }
    
    // Create test vendor data
    const testVendors = vendors && vendors.length > 0 ? vendors.slice(0, 2).map((vendor, index) => ({
      vendor_id: vendor.id,
      cost: 1000 + (index * 500),
      notes: `Test vendor ${index + 1} for schedule creation`
    })) : []
    
    console.log('🔄 Creating test schedule with data:', {
      project: testProject.name,
      vendorCount: testVendors.length,
      isOutsourced: testScheduleData.is_outsourced
    })
    
    // Call the PostgreSQL function to create schedule with vendors
    const { data: schedule, error } = await supabase.rpc('create_schedule_with_vendors', {
      p_project_id: testScheduleData.project_id,
      p_scheduled_date: testScheduleData.scheduled_date,
      p_scheduled_end_date: testScheduleData.scheduled_end_date,
      p_pilot_id: testScheduleData.pilot_id,
      p_amount: testScheduleData.amount,
      p_location: testScheduleData.location,
      p_google_maps_link: testScheduleData.google_maps_link,
      p_notes: testScheduleData.notes,
      p_is_recurring: testScheduleData.is_recurring,
      p_recurring_pattern: testScheduleData.recurring_pattern,
      p_is_outsourced: testScheduleData.is_outsourced,
      p_vendors: testVendors
    })
    
    if (error) {
      console.error('❌ Error creating test schedule:', error)
      return NextResponse.json({
        success: false,
        message: `Failed to create test schedule: ${error.message}`,
        schedule: null
      }, { status: 500 })
    }
    
    console.log('✅ Test schedule created successfully:', schedule?.custom_id)
    
    // Queue SharePoint folder creation for the test schedule
    try {
      const { queueSharePointFolderCreation } = await import('@/lib/background-jobs')
      
      const jobId = await queueSharePointFolderCreation('schedule', schedule.id, {
        scheduleId: schedule.id,
        customId: schedule.custom_id,
        scheduledDate: schedule.scheduled_date
      }, {
        executeSync: true, // Execute synchronously for testing
        fallbackToSync: false
      })
      
      console.log('✅ SharePoint folder creation completed for test schedule:', {
        scheduleId: schedule.id,
        customId: schedule.custom_id,
        jobId
      })
    } catch (jobError) {
      console.error('⚠️ SharePoint folder creation failed for test schedule:', jobError)
      // Don't fail the test if SharePoint folder creation fails
    }
    
    return NextResponse.json({
      success: true,
      message: `Test schedule created successfully with ${testVendors.length} vendors`,
      schedule: schedule
    })
    
  } catch (error) {
    console.error('❌ Test schedule creation failed:', error)
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : 'Test failed',
      schedule: null
    }, { status: 500 })
  }
}
