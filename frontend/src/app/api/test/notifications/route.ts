import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'
import { runNotificationSystemTests } from '@/lib/test-notification-system'

/**
 * Test endpoint for notification system validation
 * Only accessible by admins in development/staging environments
 */
export async function POST(request: NextRequest) {
  try {
    // Check if we're in a development environment
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Test endpoints are not available in production' },
        { status: 403 }
      )
    }

    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user profile to check role
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Only admins can run tests
    if (userProfile.role !== 'admin') {
      return NextResponse.json({ error: 'Access denied. Admin role required.' }, { status: 403 })
    }

    console.log('🧪 Starting notification system tests...')
    
    // Run the tests
    await runNotificationSystemTests()

    return NextResponse.json({
      success: true,
      message: 'Notification system tests completed. Check server logs for detailed results.',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error running notification tests:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to run tests',
        success: false
      },
      { status: 500 }
    )
  }
}

/**
 * GET endpoint for test status/info
 */
export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Test endpoints are not available in production' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      available: true,
      environment: process.env.NODE_ENV,
      message: 'Notification system test endpoint is available. Use POST to run tests.',
      tests: [
        'Database Schema Tests',
        'Role-Based Filtering Tests', 
        'Automated Trigger Tests',
        'Real-Time Updates Tests',
        'Offline Functionality Tests',
        'CSV Export Tests'
      ]
    })

  } catch (error) {
    console.error('❌ Error in test info endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
