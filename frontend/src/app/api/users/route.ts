import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createServerSupabaseClient } from '@/lib/auth-server'
import type { UserRole } from '@/types'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabaseAuth = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user profile to check role
    const { data: userProfile, error: profileError } = await supabaseAuth
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Only admins can create users
    if (userProfile.role !== 'admin') {
      return NextResponse.json({ error: 'Access denied. Admin role required.' }, { status: 403 })
    }

    const userData = await request.json()
    const { name, email, role } = userData

    // Validate required fields
    if (!name || !email || !role) {
      return NextResponse.json(
        { error: 'Name, email, and role are required' },
        { status: 400 }
      )
    }

    // Validate role
    const validRoles: UserRole[] = ['pilot', 'editor', 'manager', 'admin']
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      )
    }

    console.log('🔄 Creating user via API endpoint:', { name, email, role })

    // Use service role client for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // First create the user in Supabase Auth
    const { data: authData, error: createAuthError } = await supabase.auth.admin.createUser({
      email,
      password: 'temp123!', // Temporary password - user should reset
      email_confirm: true,
      user_metadata: {
        name,
        role
      }
    })

    if (createAuthError) {
      console.error('Error creating auth user:', createAuthError)
      return NextResponse.json(
        { error: createAuthError.message },
        { status: 400 }
      )
    }

    // Then create the user record in our users table
    const { data: userRecord, error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        name,
        role
      })
      .select()
      .single()

    if (userError) {
      console.error('Error creating user record:', userError)
      // If user record creation fails, clean up the auth user
      await supabase.auth.admin.deleteUser(authData.user.id)
      return NextResponse.json(
        { error: userError.message },
        { status: 400 }
      )
    }

    console.log('✅ User created successfully:', { id: userRecord.id, email: userRecord.email })

    return NextResponse.json({
      success: true,
      data: userRecord
    })

  } catch (error) {
    console.error('❌ Error creating user:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to create user',
        success: false
      },
      { status: 500 }
    )
  }
}