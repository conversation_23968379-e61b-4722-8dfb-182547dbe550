import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const vendorId = searchParams.get('vendorId')
    
    if (!vendorId) {
      return NextResponse.json({ error: 'vendorId is required' }, { status: 400 })
    }
    
    // Use service role key for this API endpoint
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )
    
    // First, get all schedule IDs that are linked to this vendor
    const { data: scheduleVendors, error: scheduleVendorsError } = await supabase
      .from('schedule_vendors')
      .select('schedule_id')
      .eq('vendor_id', vendorId)

    if (scheduleVendorsError) {
      console.error('Error fetching schedule vendors:', scheduleVendorsError)
      return NextResponse.json({
        error: 'Failed to fetch schedule vendors',
        details: scheduleVendorsError.message
      }, { status: 500 })
    }

    // Extract schedule IDs
    const scheduleIds = scheduleVendors?.map(sv => sv.schedule_id) || []
    
    // If no schedules are linked to this vendor, return empty result
    if (scheduleIds.length === 0) {
      return NextResponse.json({
        shoots: [],
        count: 0
      })
    }
    
    // Now fetch the schedules with project and client information
    const { data: schedules, error } = await supabase
      .from('schedules')
      .select(`
        *,
        project:projects(
          *,
          client:clients(name),
          payments:payments(*),
          expenses:expenses!expenses_project_id_fkey(
            id,
            amount,
            category,
            description,
            date,
            vendor_id
          )
        ),
        vendors:schedule_vendors(
          *,
          vendor:outsourcing_vendors(*)
        )
      `)
      .in('id', scheduleIds)
      .eq('is_outsourced', true)
      .order('scheduled_date', { ascending: false })

    if (error) {
      console.error('Error fetching vendor schedules:', error)
      return NextResponse.json({
        error: 'Failed to fetch vendor schedules',
        details: error.message
      }, { status: 500 })
    }

    const uniqueSchedules = schedules || []

    // Sort by scheduled date
    uniqueSchedules.sort((a, b) => new Date(b.scheduled_date).getTime() - new Date(a.scheduled_date).getTime())

    return NextResponse.json({
      shoots: uniqueSchedules || [],
      count: uniqueSchedules?.length || 0
    })
    
  } catch (error: any) {
    console.error('API error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 })
  }
}
