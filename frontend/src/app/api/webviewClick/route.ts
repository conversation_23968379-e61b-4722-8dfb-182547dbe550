import { NextRequest, NextResponse } from 'next/server'

/**
 * Stub API endpoint for webviewClick
 * This endpoint handles external requests (likely from browser extensions or testing tools)
 * that are trying to call /api/webviewClick
 */
export async function POST(request: NextRequest) {
  try {
    // Log the request for debugging purposes
    console.log('webviewClick API called - likely from external source')
    
    // Return a simple success response to prevent timeout errors
    return NextResponse.json({ 
      success: true, 
      message: 'webviewClick handled',
      timestamp: new Date().toISOString()
    }, { status: 200 })
  } catch (error) {
    console.error('Error in webviewClick API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// Handle GET requests as well in case they're being made
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    success: true, 
    message: 'webviewClick GET endpoint - not implemented',
    timestamp: new Date().toISOString()
  }, { status: 200 })
}