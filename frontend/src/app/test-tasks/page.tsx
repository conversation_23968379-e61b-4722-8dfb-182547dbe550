'use client'

import { useState, useEffect } from 'react'
import { projectsApi, tasksApi } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { createClientSupabaseClient } from '@/lib/auth'
import { useAuth } from '@/contexts/AuthContext'

export default function TestTasksPage() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<string>('')
  const [logs, setLogs] = useState<string[]>([])

  useEffect(() => {
    if (user) {
      addLog(`Authenticated user: ${user.name} (${user.id}) - Role: ${user.role}`)
    } else {
      addLog('No authenticated user found')
    }
  }, [user])

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testDirectTaskCreation = async () => {
    setLoading(true)
    setResult('')
    setLogs([])

    try {
      addLog('Testing direct task creation...')

      const taskData = {
        title: 'Direct Test Task',
        description: 'Testing direct task creation',
        status: 'pending' as const,
        priority: 'medium' as const,
        assigned_to: '545522ca-f408-4006-bdf7-71bc909c67c6', // admin user ID
        project_id: 'b322e0cb-82f6-4a3f-98b5-c6aebba41cb5',
      }

      addLog('Creating task with data: ' + JSON.stringify(taskData))
      const createdTask = await tasksApi.create(taskData)
      addLog('Task created: ' + createdTask.id)
      setResult('Direct task creation successful!')
    } catch (error: any) {
      addLog(`Error creating direct task: ${error.message}`)
      setResult(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testCreateTasks = async () => {
    setLoading(true)
    setResult('')
    setLogs([])

    try {
      addLog('Starting task creation test...')

      // Test the template loading first
      const { getDefaultTasksForClientType } = await import('@/lib/default-tasks')
      const templates = getDefaultTasksForClientType('Real estate')
      addLog(`Templates found: ${templates.length} templates`)

      if (templates.length === 0) {
        setResult('No templates found for Real estate client type')
        return
      }

      addLog('Calling createDefaultTasks...')
      await projectsApi.createDefaultTasks(
        'b322e0cb-82f6-4a3f-98b5-c6aebba41cb5', // test project ID
        'Real estate', // client type
        '2025-07-29T19:59:00.000Z' // shoot date
      )
      setResult('Tasks created successfully!')
      addLog('Tasks created successfully!')
    } catch (error: any) {
      addLog(`Error creating tasks: ${error.message}`)
      setResult(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Test Task Creation</h1>
        <p className="text-red-600">Please log in to test task creation.</p>
      </div>
    )
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Task Creation</h1>
      <p className="mb-4">Logged in as: {user.name} ({user.role})</p>
      
      <div className="space-x-4 mb-4">
        <Button
          onClick={testCreateTasks}
          disabled={loading}
        >
          {loading ? 'Creating Tasks...' : 'Create Default Tasks'}
        </Button>

        <Button
          onClick={testDirectTaskCreation}
          disabled={loading}
          variant="outline"
        >
          {loading ? 'Creating...' : 'Test Direct Task Creation'}
        </Button>
      </div>
      
      {result && (
        <div className={`p-4 rounded mb-4 ${result.includes('Error') ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
          {result}
        </div>
      )}

      {logs.length > 0 && (
        <div className="bg-gray-100 p-4 rounded">
          <h3 className="font-semibold mb-2">Logs:</h3>
          {logs.map((log, index) => (
            <div key={index} className="text-sm font-mono">
              {log}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
