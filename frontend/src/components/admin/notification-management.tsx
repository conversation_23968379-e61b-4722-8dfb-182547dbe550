'use client'

import { useState, useEffect } from 'react'
import { Download, Filter, Calendar, Users, BarChart3, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { formatDistanceToNow } from 'date-fns'
import type { NotificationExportOptions } from '@/types/notifications'

interface ExportStats {
  total_count: number
  unread_count: number
  by_type: Record<string, number>
  by_category: Record<string, number>
  date_range: {
    earliest: string | null
    latest: string | null
  }
}

export function NotificationManagement() {
  const { user } = useAuth()
  const [exportStats, setExportStats] = useState<ExportStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [exportOptions, setExportOptions] = useState<NotificationExportOptions>({
    include_read: true
  })
  const [showFilters, setShowFilters] = useState(false)

  // Only render for admins
  if (user?.role !== 'admin') {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">Access denied. Admin role required.</p>
      </div>
    )
  }

  const fetchExportStats = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/notifications/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(exportOptions)
      })

      if (!response.ok) {
        throw new Error('Failed to fetch export statistics')
      }

      const data = await response.json()
      setExportStats(data.statistics)
    } catch (error) {
      console.error('Error fetching export stats:', error)
      alert('Failed to fetch export statistics')
    } finally {
      setLoading(false)
    }
  }

  const handleExport = async () => {
    try {
      const params = new URLSearchParams()
      if (exportOptions.start_date) params.append('start_date', exportOptions.start_date)
      if (exportOptions.end_date) params.append('end_date', exportOptions.end_date)
      if (exportOptions.types?.length) params.append('types', exportOptions.types.join(','))
      if (exportOptions.categories?.length) params.append('categories', exportOptions.categories.join(','))
      if (exportOptions.user_ids?.length) params.append('user_ids', exportOptions.user_ids.join(','))
      if (!exportOptions.include_read) params.append('include_read', 'false')

      const response = await fetch(`/api/notifications/export?${params}`)
      if (!response.ok) {
        throw new Error('Failed to export notifications')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `notifications_export_${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Error exporting notifications:', error)
      alert('Failed to export notifications')
    }
  }

  const triggerNotifications = async (type: 'payment_overdue' | 'schedule_reminders' | 'all') => {
    try {
      const response = await fetch(`/api/cron/notifications?type=${type}&dryRun=false`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to trigger notifications')
      }

      const data = await response.json()
      alert(`Notification triggers queued successfully. Job ID: ${data.jobId}`)
    } catch (error) {
      console.error('Error triggering notifications:', error)
      alert('Failed to trigger notifications')
    }
  }

  useEffect(() => {
    fetchExportStats()
  }, [exportOptions])

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Notification Management</h2>
        <Button
          onClick={fetchExportStats}
          disabled={loading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh Stats
        </Button>
      </div>

      {/* Statistics Cards */}
      {exportStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium">Total Notifications</span>
            </div>
            <p className="text-2xl font-bold mt-2">{exportStats.total_count}</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
            <div className="flex items-center space-x-2">
              <Badge variant="destructive" className="w-5 h-5 rounded-full p-0" />
              <span className="text-sm font-medium">Unread</span>
            </div>
            <p className="text-2xl font-bold mt-2">{exportStats.unread_count}</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium">Date Range</span>
            </div>
            <p className="text-sm mt-2">
              {exportStats.date_range.earliest && exportStats.date_range.latest ? (
                <>
                  {formatDistanceToNow(new Date(exportStats.date_range.earliest))} ago
                  <br />
                  to {formatDistanceToNow(new Date(exportStats.date_range.latest))} ago
                </>
              ) : (
                'No data'
              )}
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium">Top Type</span>
            </div>
            <p className="text-sm mt-2">
              {Object.entries(exportStats.by_type)
                .sort(([,a], [,b]) => b - a)[0]?.[0] || 'None'}
            </p>
          </div>
        </div>
      )}

      {/* Type Distribution */}
      {exportStats && (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Notification Types</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(exportStats.by_type).map(([type, count]) => (
              <div key={type} className="text-center">
                <p className="text-2xl font-bold">{count}</p>
                <p className="text-sm text-gray-500 capitalize">{type}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Manual Triggers */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Manual Triggers</h3>
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={() => triggerNotifications('payment_overdue')}
            variant="outline"
            size="sm"
          >
            💰 Trigger Payment Overdue
          </Button>
          <Button
            onClick={() => triggerNotifications('schedule_reminders')}
            variant="outline"
            size="sm"
          >
            📅 Trigger Schedule Reminders
          </Button>
          <Button
            onClick={() => triggerNotifications('all')}
            variant="outline"
            size="sm"
          >
            🔔 Trigger All
          </Button>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          These triggers will queue background jobs to generate notifications based on current data.
        </p>
      </div>

      {/* Export Section */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Export Notifications</h3>
          <Button
            onClick={() => setShowFilters(!showFilters)}
            variant="outline"
            size="sm"
          >
            <Filter className="w-4 h-4 mr-2" />
            {showFilters ? 'Hide' : 'Show'} Filters
          </Button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
              <label className="block text-sm font-medium mb-2">Start Date</label>
              <input
                type="date"
                value={exportOptions.start_date || ''}
                onChange={(e) => setExportOptions(prev => ({ ...prev, start_date: e.target.value || undefined }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">End Date</label>
              <input
                type="date"
                value={exportOptions.end_date || ''}
                onChange={(e) => setExportOptions(prev => ({ ...prev, end_date: e.target.value || undefined }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
              />
            </div>
            <div className="md:col-span-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exportOptions.include_read}
                  onChange={(e) => setExportOptions(prev => ({ ...prev, include_read: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-sm">Include read notifications</span>
              </label>
            </div>
          </div>
        )}

        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            {exportStats && (
              <>
                Ready to export {exportStats.total_count} notifications
                {!exportOptions.include_read && ` (${exportStats.unread_count} unread)`}
              </>
            )}
          </div>
          <Button onClick={handleExport} className="bg-blue-600 hover:bg-blue-700">
            <Download className="w-4 h-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>
    </div>
  )
}
