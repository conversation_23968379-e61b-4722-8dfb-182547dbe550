'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Activity, Clock, Zap, AlertTriangle } from 'lucide-react'

interface PerformanceMetric {
  id: string
  operation: string
  startTime: number
  endTime?: number
  duration?: number
  status: 'pending' | 'success' | 'error'
  error?: string
}

interface PerformanceMonitorProps {
  enabled?: boolean
  maxMetrics?: number
}

export function PerformanceMonitor({ 
  enabled = process.env.NODE_ENV === 'development',
  maxMetrics = 50 
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([])
  const [isVisible, setIsVisible] = useState(false)

  // Track performance metrics
  const trackOperation = useCallback((operation: string): string => {
    if (!enabled) return ''
    
    const id = `${operation}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const metric: PerformanceMetric = {
      id,
      operation,
      startTime: performance.now(),
      status: 'pending'
    }

    setMetrics(prev => [metric, ...prev.slice(0, maxMetrics - 1)])
    return id
  }, [enabled, maxMetrics])

  const completeOperation = useCallback((id: string, status: 'success' | 'error', error?: string) => {
    if (!enabled || !id) return

    setMetrics(prev => prev.map(metric => {
      if (metric.id === id) {
        const endTime = performance.now()
        return {
          ...metric,
          endTime,
          duration: endTime - metric.startTime,
          status,
          error
        }
      }
      return metric
    }))
  }, [enabled])

  // Expose tracking functions globally for easy access
  useEffect(() => {
    if (enabled) {
      (window as any).__performanceMonitor = {
        track: trackOperation,
        complete: completeOperation
      }
    }
    
    return () => {
      if ((window as any).__performanceMonitor) {
        delete (window as any).__performanceMonitor
      }
    }
  }, [enabled, trackOperation, completeOperation])

  const clearMetrics = useCallback(() => {
    setMetrics([])
  }, [])

  const getStatusColor = (status: PerformanceMetric['status']) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'success': return 'bg-green-100 text-green-800'
      case 'error': return 'bg-red-100 text-red-800'
    }
  }

  const getStatusIcon = (status: PerformanceMetric['status']) => {
    switch (status) {
      case 'pending': return <Clock className="w-3 h-3" />
      case 'success': return <Zap className="w-3 h-3" />
      case 'error': return <AlertTriangle className="w-3 h-3" />
    }
  }

  const getDurationColor = (duration?: number) => {
    if (!duration) return 'text-gray-500'
    if (duration < 100) return 'text-green-600' // Excellent
    if (duration < 300) return 'text-yellow-600' // Good
    if (duration < 1000) return 'text-orange-600' // Slow
    return 'text-red-600' // Very slow
  }

  const averageDuration = metrics
    .filter(m => m.duration && m.status === 'success')
    .reduce((acc, m, _, arr) => acc + (m.duration! / arr.length), 0)

  const successRate = metrics.length > 0 
    ? (metrics.filter(m => m.status === 'success').length / metrics.length) * 100 
    : 0

  if (!enabled) return null

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!isVisible ? (
        <Button
          onClick={() => setIsVisible(true)}
          size="sm"
          variant="outline"
          className="bg-white shadow-lg"
        >
          <Activity className="w-4 h-4 mr-2" />
          Performance ({metrics.length})
        </Button>
      ) : (
        <Card className="w-96 max-h-96 overflow-hidden shadow-lg">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm flex items-center">
                <Activity className="w-4 h-4 mr-2" />
                Performance Monitor
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Button onClick={clearMetrics} size="sm" variant="ghost">
                  Clear
                </Button>
                <Button onClick={() => setIsVisible(false)} size="sm" variant="ghost">
                  ×
                </Button>
              </div>
            </div>
            
            {/* Summary Stats */}
            <div className="flex items-center space-x-4 text-xs text-gray-600">
              <span>Avg: {averageDuration.toFixed(0)}ms</span>
              <span>Success: {successRate.toFixed(0)}%</span>
              <span>Total: {metrics.length}</span>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="max-h-64 overflow-y-auto space-y-1">
              {metrics.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">
                  No performance metrics yet
                </p>
              ) : (
                metrics.map(metric => (
                  <div key={metric.id} className="flex items-center justify-between text-xs p-2 bg-gray-50 rounded">
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <Badge className={`text-xs ${getStatusColor(metric.status)}`}>
                        {getStatusIcon(metric.status)}
                      </Badge>
                      <span className="truncate font-mono">{metric.operation}</span>
                    </div>
                    
                    <div className="flex items-center space-x-2 text-right">
                      {metric.duration && (
                        <span className={`font-mono ${getDurationColor(metric.duration)}`}>
                          {metric.duration.toFixed(0)}ms
                        </span>
                      )}
                      {metric.error && (
                        <span className="text-red-600 truncate max-w-20" title={metric.error}>
                          {metric.error}
                        </span>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Helper function to track operations from anywhere in the app
export const trackPerformance = (operation: string): string => {
  if (typeof window !== 'undefined' && (window as any).__performanceMonitor) {
    return (window as any).__performanceMonitor.track(operation)
  }
  return ''
}

export const completePerformance = (id: string, status: 'success' | 'error', error?: string) => {
  if (typeof window !== 'undefined' && (window as any).__performanceMonitor) {
    (window as any).__performanceMonitor.complete(id, status, error)
  }
}
