'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { createClientSupabaseClient } from '@/lib/auth'
import { getDefaultTasksForClientType } from '@/lib/default-tasks'
import toast from 'react-hot-toast'

interface DebugInfo {
  users: any[]
  clients: any[]
  projects: any[]
  schedules: any[]
  tasks: any[]
  templates: any[]
}

export function TaskCreationDebug() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null)
  const [loading, setLoading] = useState(false)
  const supabase = createClientSupabaseClient()

  const runDiagnostics = async () => {
    setLoading(true)
    try {
      console.log('🔍 Running task creation diagnostics...')

      // 1. Check users and roles
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, name, email, role')
        .order('created_at')

      if (usersError) throw usersError

      // 2. Check clients
      const { data: clients, error: clientsError } = await supabase
        .from('clients')
        .select('id, name, client_type')
        .limit(10)

      if (clientsError) throw clientsError

      // 3. Check projects
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select(`
          id, name, client_id, status,
          clients!inner(client_type)
        `)
        .limit(10)

      if (projectsError) throw projectsError

      // 4. Check schedules
      const { data: schedules, error: schedulesError } = await supabase
        .from('schedules')
        .select('id, custom_id, project_id, scheduled_date, status')
        .limit(10)
        .order('created_at', { ascending: false })

      if (schedulesError) throw schedulesError

      // 5. Check tasks
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select('id, title, project_id, shoot_id, assigned_to, status')
        .limit(20)
        .order('created_at', { ascending: false })

      if (tasksError) throw tasksError

      // 6. Get task templates for common client types
      const templates = [
        { clientType: 'Wedding', templates: getDefaultTasksForClientType('Wedding') },
        { clientType: 'Movie', templates: getDefaultTasksForClientType('Movie') },
        { clientType: 'Events', templates: getDefaultTasksForClientType('Events') }
      ]

      setDebugInfo({
        users,
        clients,
        projects,
        schedules,
        tasks,
        templates
      })

      console.log('✅ Diagnostics completed')
      toast.success('Diagnostics completed successfully')

    } catch (error) {
      console.error('❌ Diagnostics failed:', error)
      toast.error('Diagnostics failed: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  const testTaskCreation = async () => {
    if (!debugInfo || debugInfo.projects.length === 0) {
      toast.error('No projects available for testing')
      return
    }

    try {
      const testProject = debugInfo.projects[0]
      const assignedUser = debugInfo.users.find(u => u.role === 'admin') || debugInfo.users[0]

      if (!assignedUser) {
        toast.error('No users available for task assignment')
        return
      }

      const taskData = {
        title: 'Test Task Creation',
        description: 'This is a test task created for debugging',
        status: 'pending',
        priority: 'medium',
        project_id: testProject.id,
        assigned_to: assignedUser.id,
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      }

      console.log('Creating test task:', taskData)

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(taskData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create task')
      }

      const createdTask = await response.json()
      console.log('✅ Test task created:', createdTask)
      toast.success('Test task created successfully: ' + createdTask.title)

      // Refresh diagnostics to show the new task
      await runDiagnostics()

    } catch (error) {
      console.error('❌ Test task creation failed:', error)
      toast.error('Test task creation failed: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Task Creation Debug</h2>
        <div className="space-x-2">
          <Button onClick={runDiagnostics} disabled={loading}>
            {loading ? 'Running...' : 'Run Diagnostics'}
          </Button>
          <Button onClick={testTaskCreation} disabled={loading || !debugInfo} variant="outline">
            Test Task Creation
          </Button>
        </div>
      </div>

      {debugInfo && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Users */}
          <Card className="p-4">
            <h3 className="font-semibold mb-2">Users ({debugInfo.users.length})</h3>
            <div className="space-y-2">
              {debugInfo.users.slice(0, 5).map(user => (
                <div key={user.id} className="flex items-center justify-between text-sm">
                  <span>{user.name}</span>
                  <Badge variant="outline">{user.role}</Badge>
                </div>
              ))}
              {debugInfo.users.length > 5 && (
                <p className="text-xs text-gray-500">...and {debugInfo.users.length - 5} more</p>
              )}
            </div>
          </Card>

          {/* Clients */}
          <Card className="p-4">
            <h3 className="font-semibold mb-2">Clients ({debugInfo.clients.length})</h3>
            <div className="space-y-2">
              {debugInfo.clients.slice(0, 5).map(client => (
                <div key={client.id} className="flex items-center justify-between text-sm">
                  <span>{client.name}</span>
                  <Badge variant="outline">{client.client_type}</Badge>
                </div>
              ))}
              {debugInfo.clients.length > 5 && (
                <p className="text-xs text-gray-500">...and {debugInfo.clients.length - 5} more</p>
              )}
            </div>
          </Card>

          {/* Projects */}
          <Card className="p-4">
            <h3 className="font-semibold mb-2">Projects ({debugInfo.projects.length})</h3>
            <div className="space-y-2">
              {debugInfo.projects.slice(0, 5).map(project => (
                <div key={project.id} className="text-sm">
                  <div className="font-medium">{project.name}</div>
                  <div className="text-gray-500">{project.clients.client_type}</div>
                </div>
              ))}
              {debugInfo.projects.length > 5 && (
                <p className="text-xs text-gray-500">...and {debugInfo.projects.length - 5} more</p>
              )}
            </div>
          </Card>

          {/* Schedules */}
          <Card className="p-4">
            <h3 className="font-semibold mb-2">Schedules ({debugInfo.schedules.length})</h3>
            <div className="space-y-2">
              {debugInfo.schedules.slice(0, 5).map(schedule => (
                <div key={schedule.id} className="text-sm">
                  <div className="font-medium">{schedule.custom_id}</div>
                  <div className="text-gray-500">{new Date(schedule.scheduled_date).toLocaleDateString()}</div>
                </div>
              ))}
              {debugInfo.schedules.length > 5 && (
                <p className="text-xs text-gray-500">...and {debugInfo.schedules.length - 5} more</p>
              )}
            </div>
          </Card>

          {/* Tasks */}
          <Card className="p-4">
            <h3 className="font-semibold mb-2">Tasks ({debugInfo.tasks.length})</h3>
            <div className="space-y-2">
              {debugInfo.tasks.slice(0, 5).map(task => (
                <div key={task.id} className="text-sm">
                  <div className="font-medium">{task.title}</div>
                  <div className="text-gray-500">
                    {task.shoot_id ? 'Shoot Task' : 'Project Task'}
                  </div>
                </div>
              ))}
              {debugInfo.tasks.length > 5 && (
                <p className="text-xs text-gray-500">...and {debugInfo.tasks.length - 5} more</p>
              )}
            </div>
          </Card>

          {/* Templates */}
          <Card className="p-4">
            <h3 className="font-semibold mb-2">Task Templates</h3>
            <div className="space-y-2">
              {debugInfo.templates.map(({ clientType, templates }) => (
                <div key={clientType} className="text-sm">
                  <div className="font-medium">{clientType}</div>
                  <div className="text-gray-500">{templates.length} templates</div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {!debugInfo && !loading && (
        <Card className="p-8 text-center">
          <p className="text-gray-500 mb-4">Click "Run Diagnostics" to check task creation setup</p>
        </Card>
      )}
    </div>
  )
}
