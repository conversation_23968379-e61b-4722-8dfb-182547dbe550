'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { expensesApi, projectsApi } from '@/lib/api'
import toast from 'react-hot-toast'
import { useAuth } from '@/contexts/AuthContext'
import type { OutsourcingVendor, Expense } from '@/types'

const vendorPaymentSchema = z.object({
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  payment_date: z.string().min(1, 'Payment date is required'),
  payment_method: z.enum(['cash', 'bank_transfer', 'credit_card', 'cheque', 'other']),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
})

type VendorPaymentFormData = z.infer<typeof vendorPaymentSchema>

interface RecordVendorPaymentFormProps {
  vendor: OutsourcingVendor
  projectId?: string
  onSuccess: (expense: Expense) => void
  onCancel: () => void
}

export function RecordVendorPaymentForm({ vendor, projectId, onSuccess, onCancel }: RecordVendorPaymentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { user } = useAuth()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<VendorPaymentFormData>({
    resolver: zodResolver(vendorPaymentSchema),
    defaultValues: {
      amount: 0,
      payment_date: new Date().toISOString().slice(0, 10),
      payment_method: 'bank_transfer',
      reference_number: '',
      notes: '',
    },
  })

  const onSubmit = async (data: VendorPaymentFormData) => {
    try {
      if (!user) {
        toast.error('You must be logged in to record vendor payments')
        return
      }

      setIsSubmitting(true)

      // Record vendor payment as an expense with "outsourcing" category
      const expenseData = {
        description: data.notes ? `Vendor Payment to ${vendor.name}: ${data.notes}` : `Payment to vendor: ${vendor.name}`,
        amount: data.amount,
        category: 'outsourcing' as const,
        date: data.payment_date,
        project_id: projectId || null,
        user_id: user.id,
        vendor_id: vendor.id,
        receipt_url: data.reference_number ? `Reference: ${data.reference_number}` : null,
      }

      const expense = await expensesApi.create(expenseData)
      
      // Update vendor payment status for the project if projectId is provided
      if (projectId) {
        try {
          await projectsApi.updateVendorPaymentStatus(projectId)
        } catch (statusError: any) {
          console.warn('Failed to update vendor payment status:', statusError.message)
          // Don't fail the entire operation if status update fails
        }
      }
      
      toast.success('Vendor payment recorded as outsourcing expense successfully')
      reset()
      onSuccess(expense)
    } catch (error: any) {
      toast.error(error.message || 'Failed to record vendor payment')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Vendor Info */}
      <div className="bg-muted/50 p-4 rounded-lg">
        <h4 className="font-medium text-foreground mb-2">Record Vendor Payment (Outsourcing Expense)</h4>
        <div className="text-sm text-muted-foreground">
          <p><strong>Vendor:</strong> {vendor.name}</p>
          {vendor.specialization && <p><strong>Specialization:</strong> {vendor.specialization}</p>}
          {vendor.contact_person && <p><strong>Contact:</strong> {vendor.contact_person}</p>}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Payment Amount */}
        <div>
          <Label htmlFor="amount" className="text-sm font-medium">
            Payment Amount (₹) *
          </Label>
          <Input
            id="amount"
            type="number"
            step="0.01"
            min="0.01"
            {...register('amount', { valueAsNumber: true })}
            className="mt-1"
            placeholder="0.00"
          />
          {errors.amount && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.amount.message}</p>
          )}
        </div>

        {/* Payment Date */}
        <div>
          <Label htmlFor="payment_date" className="text-sm font-medium">
            Payment Date *
          </Label>
          <Input
            id="payment_date"
            type="date"
            {...register('payment_date')}
            className="mt-1"
          />
          {errors.payment_date && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.payment_date.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Payment Method */}
        <div>
          <Label htmlFor="payment_method" className="text-sm font-medium">
            Payment Method *
          </Label>
          <select
            id="payment_method"
            {...register('payment_method')}
            className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm ring-offset-background file:border-0 file:bg-background file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
          >
            <option value="bank_transfer">Bank Transfer</option>
            <option value="cash">Cash</option>
            <option value="credit_card">Credit Card</option>
            <option value="cheque">Cheque</option>
            <option value="other">Other</option>
          </select>
          {errors.payment_method && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.payment_method.message}</p>
          )}
        </div>

        {/* Reference Number */}
        <div>
          <Label htmlFor="reference_number" className="text-sm font-medium">
            Reference Number
          </Label>
          <Input
            id="reference_number"
            {...register('reference_number')}
            className="mt-1"
            placeholder="Transaction ID, Cheque number, etc."
          />
          {errors.reference_number && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.reference_number.message}</p>
          )}
        </div>
      </div>

      {/* Notes */}
      <div>
        <Label htmlFor="notes" className="text-sm font-medium">
          Payment Notes
        </Label>
        <textarea
          id="notes"
          {...register('notes')}
          rows={3}
          className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm ring-offset-background file:border-0 file:bg-background file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
          placeholder="Add payment notes, invoice details, or other information..."
        />
        {errors.notes && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.notes.message}</p>
        )}
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Recording Expense...' : 'Record as Expense'}
        </Button>
      </div>
    </form>
  )
}
