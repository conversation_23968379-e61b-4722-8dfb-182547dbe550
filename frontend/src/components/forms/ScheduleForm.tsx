'use client'

import { useState, useEffect, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { LocationSearch } from '@/components/ui/location-search'
import { useCreateSchedule, useUpdateSchedule, useProjects } from '@/hooks/useApi'
import { useAuth } from '@/contexts/AuthContext'
import { extractLocationFromMapsUrl } from '@/lib/maps-utils'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'
import Link from 'next/link'
import { Plus, Trash2 } from 'lucide-react'
import type { Schedule, User, OutsourcingVendor } from '@/types'
import { calculateScheduleGST, getDefaultGSTStatus, getGSTAmountsForStorage, getGSTBreakdownForDisplay } from '@/lib/schedule-gst-calculations'

const scheduleSchema = z.object({
  project_id: z.string().min(1, 'Project is required'),
  scheduled_date: z.string().min(1, 'Start date & time is required'),
  scheduled_end_date: z.string().optional(),
  pilot_id: z.string().optional(),
  amount: z.number().min(0, 'Amount must be positive').optional(),
  has_gst: z.boolean(),
  location: z.string().optional(),
  google_maps_link: z.string().optional(),
  notes: z.string().optional(),
  is_recurring: z.boolean(),
  recurring_pattern: z.enum(['weekly', 'monthly', 'quarterly']).optional(),
  // Schedule type for task template selection
  schedule_type: z.string().optional(),
  // Vendor/Outsourcing fields
  is_outsourced: z.boolean(),
  // Legacy fields for backward compatibility
  vendor_id: z.string().optional(),
  outsourcing_cost: z.number().min(0, 'Outsourcing cost must be positive').optional(),
})

type ScheduleFormData = z.infer<typeof scheduleSchema>

interface ScheduleFormProps {
  schedule?: Schedule
  onSuccess?: (schedule: Schedule) => void
  onCancel?: () => void
  preselectedProjectId?: string
}

export function ScheduleForm({ schedule, onSuccess, onCancel, preselectedProjectId }: ScheduleFormProps) {
  const isEditing = !!schedule
  const { user } = useAuth()
  const { createScheduleWithVendors, loading: createLoading } = useCreateSchedule()
  const { updateScheduleWithVendors, loading: updateLoading } = useUpdateSchedule()
  const { data: projects, loading: projectsLoading } = useProjects()
  const loading = createLoading || updateLoading
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [previewId, setPreviewId] = useState<string | null>(null)

  const [extractingLocation, setExtractingLocation] = useState(false)
  const [useProjectLocation, setUseProjectLocation] = useState(true)
  const [users, setUsers] = useState<User[]>([])
  const [loadingUsers, setLoadingUsers] = useState(false)
  const [vendors, setVendors] = useState<OutsourcingVendor[]>([])
  const [loadingVendors, setLoadingVendors] = useState(false)
  const [selectedVendors, setSelectedVendors] = useState<Array<{
    vendor_id: string
    cost: number
    notes: string
  }>>([])


  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ScheduleFormData>({
    resolver: zodResolver(scheduleSchema),
    defaultValues: schedule ? {
      project_id: schedule.project_id,
      scheduled_date: new Date(schedule.scheduled_date).toISOString().slice(0, 16),
      scheduled_end_date: schedule.scheduled_end_date ? new Date(schedule.scheduled_end_date).toISOString().slice(0, 16) : '',
      pilot_id: schedule.pilot_id || '',
      amount: schedule.amount || undefined,
      has_gst: schedule.has_gst || false,
      location: schedule.location || '',
      google_maps_link: schedule.google_maps_link || '',
      notes: schedule.notes || '',
      is_recurring: schedule.is_recurring,
      recurring_pattern: schedule.recurring_pattern || undefined,
      schedule_type: schedule.schedule_type || '',
      is_outsourced: schedule.is_outsourced || false,
      // Legacy fields for backward compatibility
      vendor_id: schedule.vendor_id || '',
      outsourcing_cost: schedule.outsourcing_cost || undefined,

    } : {
      project_id: preselectedProjectId || '',
      scheduled_date: '',
      scheduled_end_date: '',
      pilot_id: user?.role === 'pilot' ? user.id : '',
      amount: undefined,
      has_gst: false,
      location: '',
      google_maps_link: '',
      notes: '',
      is_recurring: false,
      recurring_pattern: undefined,
      schedule_type: '',
      is_outsourced: false,
      // Legacy fields for backward compatibility
      vendor_id: '',
      outsourcing_cost: undefined,

    },
  })

  // Preview ID for create mode only in browser
  useEffect(() => {
    let mounted = true
    async function loadPreview() {
      try {
        const { peekNextId } = await import('@/lib/api')
        const id = await peekNextId('schedule')
        if (mounted) setPreviewId(id)
      } catch (e) {
        console.warn('ScheduleForm preview load failed:', e)
      }
    }
    if (typeof window !== 'undefined' && !isEditing) {
      loadPreview()
    }
    return () => { mounted = false }
  }, [isEditing])

  const selectedProjectId = watch('project_id')
  const selectedProject = projects?.find(p => p.id === selectedProjectId)
  const isOutsourced = watch('is_outsourced')
  const isRecurring = watch('is_recurring')
  const googleMapsLink = watch('google_maps_link')
  const amount = watch('amount')
  const hasGst = watch('has_gst')

  // Calculate GST amounts in real-time
  const gstCalculation = calculateScheduleGST(amount || 0, hasGst)

  // Fetch users for pilot dropdown
  useEffect(() => {
    const fetchUsers = async () => {
      setLoadingUsers(true)
      try {
        const { data, error } = await supabase
          .from('users')
          .select('id, name, email, role, created_at, updated_at')
          .order('name')

        if (error) throw error
        setUsers(data || [])
      } catch (error) {
        console.error('Error fetching users:', error)
        toast.error('Failed to load users')
      } finally {
        setLoadingUsers(false)
      }
    }

    fetchUsers()
  }, [])

  // Fetch vendors for outsourcing dropdown
  useEffect(() => {
    const fetchVendors = async () => {
      setLoadingVendors(true)
      try {
        const { data, error } = await supabase
          .from('outsourcing_vendors')
          .select('*')
          .order('name')

        if (error) throw error
        setVendors(data || [])
      } catch (error) {
        console.error('Error fetching vendors:', error)
        toast.error('Failed to load vendors')
      } finally {
        setLoadingVendors(false)
      }
    }

    fetchVendors()
  }, [])

  // Load existing schedule vendors when editing
  useEffect(() => {
    const loadScheduleVendors = async () => {
      if (!schedule?.id) return
  
      try {
        let existingVendors: any[] | null = null
  
        // Prefer vendors already included on the schedule prop (via schedulesApi.getById)
        if (Array.isArray((schedule as any).vendors) && (schedule as any).vendors.length > 0) {
          existingVendors = (schedule as any).vendors
        } else {
          // Fallback: query schedule_vendors directly
          const { data, error } = await supabase
            .from('schedule_vendors')
            .select(`
              *,
              vendor:outsourcing_vendors(*)
            `)
            .eq('schedule_id', schedule.id)
  
          if (error) throw error
          existingVendors = data || []
        }
  
        // Initialize selectedVendors with existing data
        const vendorData = (existingVendors || []).map((sv: any) => ({
          vendor_id: sv.vendor_id || sv.vendor?.id || '',
          cost: Number(sv.cost) || 0,
          notes: sv.notes || ''
        }))
        setSelectedVendors(vendorData)
  
        // Ensure checkbox state reflects reality if vendors exist
        if (vendorData.length > 0 && !isOutsourced) {
          setValue('is_outsourced', true)
        }
      } catch (error) {
        console.error('Error loading schedule vendors:', error)
      }
    }
  
    loadScheduleVendors()
  // also react to schedule.vendors changes if parent re-fetches
  }, [schedule?.id, (schedule as any)?.vendors?.length])

  // Auto-fill location from project when project changes
  useEffect(() => {
    if (selectedProject && useProjectLocation && !isEditing) {
      setValue('location', selectedProject.location || '')
    }
  }, [selectedProject, useProjectLocation, setValue, isEditing])

  // Set default GST status based on client when project changes (only for new schedules)
  useEffect(() => {
    if (selectedProject?.client && !isEditing) {
      const defaultGstStatus = getDefaultGSTStatus(selectedProject.client)
      setValue('has_gst', defaultGstStatus)
    }
  }, [selectedProject?.client, setValue, isEditing])

  // Extract location from Google Maps link
  const handleExtractLocation = useCallback(async () => {
    if (!googleMapsLink) {
      toast.error('Please enter a Google Maps link first')
      return
    }

    setExtractingLocation(true)
    try {
      const location = await extractLocationFromMapsUrl(googleMapsLink)
      if (location) {
        setValue('location', location.formattedLocation)
        setUseProjectLocation(false)
        toast.success('Location extracted successfully')
      } else {
        toast.error('Could not extract location from the provided link')
      }
    } catch (error) {
      console.error('Error extracting location:', error)
      toast.error('Failed to extract location')
    } finally {
      setExtractingLocation(false)
    }
  }, [googleMapsLink, setValue])

  // Vendor management functions
  const addVendor = () => {
    setSelectedVendors([...selectedVendors, { vendor_id: '', cost: 0, notes: '' }])
  }

  const removeVendor = (index: number) => {
    setSelectedVendors(selectedVendors.filter((_, i) => i !== index))
  }

  const updateVendor = (index: number, field: string, value: any) => {
    const updated = [...selectedVendors]
    updated[index] = { ...updated[index], [field]: value }
    setSelectedVendors(updated)
  }


  const onSubmit = async (data: ScheduleFormData) => {
    // Prevent multiple submissions
    if (isSubmitting || loading) {
      return
    }

    setIsSubmitting(true)
    try {
      if (!user) {
        toast.error('You must be logged in to create schedules')
        return
      }

      // Client-side validation for outsourcing vendors
      const isOut = !!data.is_outsourced
      if (isOut && selectedVendors.length === 0) {
        toast.error('Add at least one outsourcing vendor')
        return
      }
      if (isOut) {
        // Ensure all selected vendors have vendor_id and non-negative cost
        const invalid = selectedVendors.some(v => !v.vendor_id || isNaN(Number(v.cost)) || Number(v.cost) < 0)
        if (invalid) {
          toast.error('Please select a vendor and enter a valid non-negative cost for each vendor')
          return
        }
      }

      // Calculate GST amounts for storage
      const baseAmount = data.amount || 0
      const gstAmounts = getGSTAmountsForStorage(baseAmount, data.has_gst)

      const scheduleData = {
        ...data,
        amount: baseAmount,
        ...gstAmounts,
        location: data.location || null,
        google_maps_link: data.google_maps_link || null,
        notes: data.notes || null,
        pilot_id: data.pilot_id || null,
        recurring_pattern: data.is_recurring ? data.recurring_pattern : null,
        is_outsourced: isOut,
        // Legacy fields no longer stored in DB; keep nulls to avoid insert into removed columns
        vendor_id: null,
        outsourcing_cost: null,
      }

      // Normalize vendors payload for RPC
      const normalizedVendors = (isOut ? selectedVendors : []).map(v => ({
        vendor_id: String(v.vendor_id),
        cost: Number(v.cost) || 0,
        notes: v.notes || ''
      }))

      let result: Schedule
      if (isEditing) {
        result = await updateScheduleWithVendors(schedule.id, scheduleData, normalizedVendors)
        // After update, fetch enriched schedule with vendors to reflect immediately
        try {
          const full = await (await import('@/lib/api')).schedulesApi.getById(schedule.id)
          if (full) result = full as Schedule
        } catch {}
        toast.success('Schedule updated successfully')
      } else {
        const created = await createScheduleWithVendors(scheduleData, normalizedVendors)

        // Show success message and close modal immediately after API response
        toast.success('Schedule created successfully! Setting up folders in background...')
        reset()
        setSelectedVendors([])

        // Call onSuccess immediately to close modal
        onSuccess?.(created)

        // Continue with enrichment in background (don't await)
        setTimeout(async () => {
          try {
            const full = await (await import('@/lib/api')).schedulesApi.getById(created.id)
            if (full) {
              // Optionally trigger a refresh or update if needed
              console.log('Schedule enriched with full data:', full)
            }
          } catch (error) {
            console.log('Background enrichment failed (non-critical):', error)
          }
        }, 100)

        return // Exit early to prevent duplicate onSuccess calls
      }

      onSuccess?.(result)
    } catch (error: any) {
      toast.error(error.message || 'Failed to save schedule')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (projectsLoading || loadingUsers || loadingVendors) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {isEditing && schedule?.custom_id && (
        <div className="text-xs text-muted-foreground -mb-2">
          ID: {schedule.custom_id}
        </div>
      )}
      {!isEditing && previewId && (
        <div className="text-xs text-muted-foreground -mb-2">
          ID (preview): {previewId}
        </div>
      )}
      {/* Project Selection */}
      <div>
        <Label htmlFor="project_id">Project *</Label>
        <select
          id="project_id"
          {...register('project_id')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="">Select a project</option>
          {projects?.map((project) => (
            <option key={project.id} value={project.id}>
              {project.name} - {project.client?.name}
            </option>
          ))}
        </select>
        {errors.project_id && (
          <p className="text-sm text-red-600 mt-1">{errors.project_id.message}</p>
        )}
      </div>

      {/* Schedule Type Selection */}
      <div>
        <Label htmlFor="schedule_type">Schedule Type</Label>
        <select
          id="schedule_type"
          {...register('schedule_type')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="">Select schedule type (optional)</option>
          <option value="Wedding">Wedding</option>
          <option value="Movie">Movie</option>
          <option value="Surveillance">Surveillance</option>
          <option value="Event">Event</option>
          <option value="News">News</option>
          <option value="Collaboration">Collaboration</option>
          <option value="Corporate">Corporate</option>
          <option value="Real Estate">Real Estate</option>
          <option value="Govt">Government</option>
          <option value="NGO">NGO</option>
          <option value="Survey">Survey</option>
        </select>
        {errors.schedule_type && (
          <p className="text-sm text-red-600 mt-1">{errors.schedule_type.message}</p>
        )}
        <p className="text-xs text-gray-500 mt-1">
          Select a schedule type to automatically create default tasks for this schedule
        </p>
      </div>

      {/* Project Info Display */}
      {selectedProject && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Project: {selectedProject.name}</h4>
          <p className="text-sm text-blue-700">{selectedProject.description}</p>
          <div className="mt-2 text-sm text-blue-600">
            Status: <span className="font-medium">{selectedProject.status}</span> |
            Location: <span className="font-medium">{selectedProject.location}</span>
          </div>
        </div>
      )}

      {/* Date and Time */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="scheduled_date">Start Date & Time *</Label>
          <Input
            id="scheduled_date"
            type="datetime-local"
            {...register('scheduled_date')}
            className="mt-1"
          />
          {errors.scheduled_date && (
            <p className="text-sm text-red-600 mt-1">{errors.scheduled_date.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="scheduled_end_date">End Date & Time (Optional)</Label>
          <Input
            id="scheduled_end_date"
            type="datetime-local"
            {...register('scheduled_end_date')}
            className="mt-1"
          />
          {errors.scheduled_end_date && (
            <p className="text-sm text-red-600 mt-1">{errors.scheduled_end_date.message}</p>
          )}
        </div>
      </div>

      {/* Amount */}
      <div>
        <Label htmlFor="amount">Base Amount (₹)</Label>
        <Input
          id="amount"
          type="number"
          step="0.01"
          min="0"
          {...register('amount', { valueAsNumber: true })}
          className="mt-1"
          placeholder="0.00"
        />
        {errors.amount && (
          <p className="text-sm text-red-600 mt-1">{errors.amount.message}</p>
        )}
      </div>

      {/* GST Checkbox */}
      <div>
        <div className="flex items-center space-x-2 mt-1">
          <input
            id="has_gst"
            type="checkbox"
            {...register('has_gst')}
            className="rounded border-border text-primary focus:ring-primary"
          />
          <Label htmlFor="has_gst" className="text-sm font-medium cursor-pointer">
            GST (18%)
            {selectedProject?.client?.has_gst && (
              <span className="text-xs text-muted-foreground ml-1">
                (Client has GST registration)
              </span>
            )}
          </Label>
        </div>
        {errors.has_gst && (
          <p className="text-sm text-red-600 mt-1">{errors.has_gst.message}</p>
        )}
      </div>

      {/* Amount Breakdown */}
      {(amount && amount > 0) && (
        <div className="bg-muted/30 border border-border rounded-lg p-4">
          <h4 className="font-medium text-sm text-muted-foreground mb-3">Amount Breakdown</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Base Amount:</span>
              <span className="font-medium">{gstCalculation.formattedBaseAmount}</span>
            </div>
            {hasGst && (
              <div className="flex justify-between">
                <span>GST (18%):</span>
                <span className="font-medium">{gstCalculation.formattedGstAmount}</span>
              </div>
            )}
            <div className="flex justify-between pt-2 border-t border-border font-semibold">
              <span>Total Amount:</span>
              <span className="text-green-600 dark:text-green-400">
                {gstCalculation.formattedTotalAmount}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Pilot Selection */}
      <div>
        <Label htmlFor="pilot_id">Assign Pilot</Label>
        <select
          id="pilot_id"
          {...register('pilot_id')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          disabled={loadingUsers}
        >
          <option value="">Select a pilot (optional)</option>
          {users.filter(u => u.role === 'pilot' || u.role === 'admin').map((pilot) => (
            <option key={pilot.id} value={pilot.id}>
              {pilot.name} ({pilot.email})
            </option>
          ))}
        </select>
        {loadingUsers && (
          <p className="text-xs text-blue-600 mt-1">Loading pilots...</p>
        )}
        {errors.pilot_id && (
          <p className="text-sm text-red-600 mt-1">{errors.pilot_id.message}</p>
        )}
      </div>

      {/* Location Section */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Location</Label>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="useProjectLocation"
              checked={useProjectLocation}
              onChange={(e) => {
                setUseProjectLocation(e.target.checked)
                if (e.target.checked && selectedProject) {
                  setValue('location', selectedProject.location || '')
                  setValue('google_maps_link', selectedProject.google_maps_link || '')
                } else if (!e.target.checked) {
                  setValue('location', '')
                  setValue('google_maps_link', '')
                }
              }}
              className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
            />
            <Label htmlFor="useProjectLocation" className="text-sm text-gray-600 dark:text-gray-400">
              Use project location
            </Label>
          </div>
        </div>

        {useProjectLocation ? (
          <div className="space-y-3">
            <Input
              id="location"
              {...register('location')}
              placeholder="Using project location"
              disabled={true}
              className="mt-1 bg-gray-100 dark:bg-gray-800"
            />
            {selectedProject && selectedProject.location && (
              <p className="text-xs text-muted-foreground">
                📍 {selectedProject.location}
                {selectedProject.google_maps_link && (
                  <span> • <a
                    href={selectedProject.google_maps_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    View on Maps
                  </a></span>
                )}
              </p>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            <div className="relative mb-6">
              <LocationSearch
                value={watch('location') || ''}
                onChange={(location) => setValue('location', location)}
                onLocationSelect={(locationInfo) => {
                  setValue('location', locationInfo.formattedLocation)
                  // Optionally store coordinates for future use
                  if (locationInfo.coordinates) {
                    // You could store coordinates in a hidden field or state if needed
                    console.log('Selected location coordinates:', locationInfo.coordinates)
                  }
                }}
                placeholder="Search for a location..."
                error={errors.location?.message}
                disabled={useProjectLocation}
                showCurrentLocationToggle={!useProjectLocation}
              />
            </div>

            <div className="mt-3">
              <Label htmlFor="google_maps_link" className="text-sm text-gray-600 dark:text-gray-400">
                Or paste Google Maps link
              </Label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="google_maps_link"
                  {...register('google_maps_link')}
                  placeholder="https://maps.google.com/..."
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleExtractLocation}
                  disabled={extractingLocation || !googleMapsLink}
                  className="px-3"
                >
                  {extractingLocation ? 'Extracting...' : 'Extract'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Notes */}
      <div>
        <Label htmlFor="notes">Notes</Label>
        <textarea
          id="notes"
          {...register('notes')}
          placeholder="Enter any notes about this shoot"
          className="mt-1 flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 resize-none"
          rows={3}
        />
        {errors.notes && (
          <p className="text-sm text-red-600 mt-1">{errors.notes.message}</p>
        )}
      </div>

      {/* Outsourcing Section */}
      <div className="border border-gray-200 rounded-lg p-4">
        <div className="flex items-center gap-3 mb-4">
          <input
            id="is_outsourced"
            type="checkbox"
            {...register('is_outsourced')}
            className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
          />
          <Label htmlFor="is_outsourced" className="text-sm font-medium">
            Outsourced Schedule
          </Label>
        </div>

        {isOutsourced && (
          <div className="space-y-4 mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between mb-4">
              <Label className="text-sm font-medium">Outsourcing Vendors</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addVendor}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Vendor
              </Button>
            </div>

            {selectedVendors.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p className="text-sm">No vendors added yet</p>
                <p className="text-xs mt-1">Click "Add Vendor" to add outsourcing vendors</p>
              </div>
            ) : (
              <div className="space-y-4">
                {selectedVendors.map((vendor, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4 bg-white">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="text-sm font-medium text-gray-700">Vendor {index + 1}</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeVendor(index)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 p-1"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium mb-2 block">
                          Select Vendor
                        </Label>
                        <select
                          value={vendor.vendor_id}
                          onChange={(e) => updateVendor(index, 'vendor_id', e.target.value)}
                          className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          disabled={loadingVendors}
                        >
                          <option value="">Choose a vendor</option>
                          {vendors.map((v) => (
                            <option key={v.id} value={v.id}>
                              {v.name} {v.specialization && `- ${v.specialization}`}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <Label className="text-sm font-medium mb-2 block">
                          Cost (₹)
                        </Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={vendor.cost}
                          onChange={(e) => updateVendor(index, 'cost', parseFloat(e.target.value) || 0)}
                          placeholder="Enter cost"
                        />
                      </div>
                    </div>

                    <div className="mt-4">
                      <Label className="text-sm font-medium mb-2 block">
                        Notes (Optional)
                      </Label>
                      <Input
                        value={vendor.notes}
                        onChange={(e) => updateVendor(index, 'notes', e.target.value)}
                        placeholder="Any specific notes for this vendor..."
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="mt-4 pt-4 border-t border-gray-200">
              <p className="text-xs text-blue-600">
                Don't see your vendor? <Link href="/vendors" className="font-medium hover:underline">Add a new vendor</Link>
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Recurring Section */}
      <div className="border border-gray-200 rounded-lg p-4">
        <div className="flex items-center gap-3 mb-4">
          <input
            id="is_recurring"
            type="checkbox"
            {...register('is_recurring')}
            className="w-4 h-4 rounded border-gray-300 text-green-600 focus:ring-green-500 focus:ring-2"
          />
          <Label htmlFor="is_recurring" className="text-sm font-medium">
            Recurring Shoot
          </Label>
        </div>

        {isRecurring && (
          <div className="mt-4">
            <Label htmlFor="recurring_pattern" className="text-sm font-medium mb-2 block">
              Recurring Pattern
            </Label>
            <select
              id="recurring_pattern"
              {...register('recurring_pattern')}
              className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">Choose pattern</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
            </select>
            {errors.recurring_pattern && (
              <p className="text-sm text-red-600 mt-1">{errors.recurring_pattern.message}</p>
            )}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-4">
        <Button
          type="submit"
          disabled={loading || isSubmitting}
          className="flex-1"
        >
          {(loading || isSubmitting) ? 'Saving...' : isEditing ? 'Update Schedule' : 'Create Schedule'}
        </Button>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading || isSubmitting}
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  )
}

// Keep ShootForm as alias for backward compatibility
export const ShootForm = ScheduleForm
