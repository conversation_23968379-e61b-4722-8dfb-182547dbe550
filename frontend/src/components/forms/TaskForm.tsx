'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreateTask, useUpdateTask, useProjects, useSchedules, useUsers } from '@/hooks/useApi'
import { useAuth } from '@/contexts/AuthContext'
import toast from 'react-hot-toast'
import type { Task, User } from '@/types'

const taskSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  due_date: z.string().optional(),
  project_id: z.string().optional(),
  shoot_id: z.string().optional(),
  assigned_to: z.string().optional(),
  assigned_role: z.enum(['pilot', 'editor', 'admin']).optional(),
})

type TaskFormData = z.infer<typeof taskSchema>

interface TaskFormProps {
  task?: Task
  onSuccess?: (task: Task) => void
  onCancel?: () => void
  preselectedProjectId?: string
  preselectedShootId?: string
}

export function TaskForm({ task, onSuccess, onCancel, preselectedProjectId, preselectedShootId }: TaskFormProps) {
  const isEditing = !!task
  const { user } = useAuth()
  const { createTask, loading: createLoading } = useCreateTask()
  const { updateTask, loading: updateLoading } = useUpdateTask()
  const { data: projects, loading: projectsLoading } = useProjects()
  const { data: schedules, loading: schedulesLoading } = useSchedules()
  const { data: users, loading: usersLoading } = useUsers()
  const loading = createLoading || updateLoading
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])

  // Hide project section if preselected
  const hideProjectSection = !!preselectedProjectId
  // Hide shoot section if preselected
  const hideShootSection = !!preselectedShootId

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: task ? {
      title: task.title,
      description: task.description || '',
      priority: task.priority,
      due_date: task.due_date ? new Date(task.due_date).toISOString().slice(0, 10) : '',
      project_id: task.project_id || '',
      shoot_id: task.shoot_id || '',
      assigned_to: task.assigned_to || '',
      assigned_role: task.assigned_role || undefined,
    } : {
      title: '',
      description: '',
      priority: 'medium',
      due_date: '',
      project_id: preselectedProjectId || '',
      shoot_id: preselectedShootId || '',
      assigned_to: '',
      assigned_role: undefined,
    },
  })

  const selectedProjectId = watch('project_id')
  const selectedAssignedRole = watch('assigned_role')
  const selectedProject = projects?.find(p => p.id === selectedProjectId)
  const filteredSchedules = schedules?.filter(s => !selectedProjectId || s.project_id === selectedProjectId)

  // Filter users by selected role
  useEffect(() => {
    if (!users) {
      setFilteredUsers([])
      return
    }

    if (!selectedAssignedRole) {
      setFilteredUsers(users)
      return
    }

    const roleMapping: Record<string, string[]> = {
      pilot: ['pilot'],
      editor: ['editor'],
      admin: ['manager', 'admin']
    }

    const allowedRoles = roleMapping[selectedAssignedRole] || []
    const filtered = users.filter(user => allowedRoles.includes(user.role))
    setFilteredUsers(filtered)

    // Clear assigned_to if current selection is not in filtered users
    const currentAssignedTo = watch('assigned_to')
    if (currentAssignedTo && !filtered.find(u => u.id === currentAssignedTo)) {
      setValue('assigned_to', '')
    }
  }, [users, selectedAssignedRole, setValue, watch])

  const onSubmit = async (data: TaskFormData) => {
    try {
      if (!user) {
        toast.error('You must be logged in to create tasks')
        return
      }

      const taskData = {
        ...data,
        status: 'pending', // Always start as pending
        description: data.description || null,
        due_date: data.due_date || null,
        project_id: data.project_id || null,
        shoot_id: data.shoot_id || null,
        assigned_to: data.assigned_to || user.id, // Default to current user if not assigned
        assigned_role: data.assigned_role || null,
        created_by: user.id,
      }

      let result: Task
      if (isEditing) {
        result = await updateTask(task.id, taskData)
        toast.success('Task updated successfully')
      } else {
        result = await createTask(taskData)
        toast.success('Task created successfully')
        reset()
      }

      onSuccess?.(result)
    } catch (error: any) {
      toast.error(error.message || 'Failed to save task')
    }
  }

  if (projectsLoading || schedulesLoading || usersLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="title">Title *</Label>
        <Input
          id="title"
          {...register('title')}
          placeholder="Enter task title"
          className="mt-1"
        />
        {errors.title && (
          <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <textarea
          id="description"
          {...register('description')}
          placeholder="Enter task description"
          className="mt-1 flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          rows={3}
        />
        {errors.description && (
          <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="priority">Priority *</Label>
        <select
          id="priority"
          {...register('priority')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="low">Low</option>
          <option value="medium">Medium</option>
          <option value="high">High</option>
          <option value="urgent">Urgent</option>
        </select>
        {errors.priority && (
          <p className="text-sm text-red-600 mt-1">{errors.priority.message}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="due_date">Due Date</Label>
          <Input
            id="due_date"
            type="date"
            {...register('due_date')}
            className="mt-1"
          />
          {errors.due_date && (
            <p className="text-sm text-red-600 mt-1">{errors.due_date.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="assigned_role">Assigned Role</Label>
          <select
            id="assigned_role"
            {...register('assigned_role')}
            className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          >
            <option value="">Select role</option>
            <option value="pilot">Pilot</option>
            <option value="editor">Editor</option>
            <option value="admin">Admin</option>
          </select>
          {errors.assigned_role && (
            <p className="text-sm text-red-600 mt-1">{errors.assigned_role.message}</p>
          )}
        </div>
      </div>

      {/* Assigned To Dropdown */}
      <div>
        <Label htmlFor="assigned_to">Assigned To</Label>
        <select
          id="assigned_to"
          {...register('assigned_to')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="">Select user (defaults to you)</option>
          {filteredUsers.map((user) => (
            <option key={user.id} value={user.id}>
              {user.name || user.email} ({user.role})
            </option>
          ))}
        </select>
        <p className="text-xs text-gray-500 mt-1">
          {selectedAssignedRole
            ? `Showing users with ${selectedAssignedRole} role`
            : 'Select a role first to filter users, or leave empty to assign to yourself'
          }
        </p>
        {errors.assigned_to && (
          <p className="text-sm text-red-600 mt-1">{errors.assigned_to.message}</p>
        )}
      </div>

      {!hideProjectSection && (
        <>
          <div>
            <Label htmlFor="project_id">Project (Optional)</Label>
            <select
              id="project_id"
              {...register('project_id')}
              className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">No specific project</option>
              {projects?.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.name} - {project.client?.name}
                </option>
              ))}
            </select>
            {errors.project_id && (
              <p className="text-sm text-red-600 mt-1">{errors.project_id.message}</p>
            )}
          </div>

          {selectedProject && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Project: {selectedProject.name}</h4>
              <p className="text-sm text-blue-700">{selectedProject.description}</p>
              <div className="mt-2 text-sm text-blue-600">
                Status: <span className="font-medium">{selectedProject.status}</span> |
                Location: <span className="font-medium">{selectedProject.location}</span>
              </div>
            </div>
          )}
        </>
      )}

      {!hideShootSection && (
        <div>
          <Label htmlFor="shoot_id">Schedule (Optional)</Label>
          <select
            id="shoot_id"
            {...register('shoot_id')}
            className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            disabled={!selectedProjectId && !hideProjectSection}
          >
            <option value="">No specific schedule</option>
            {filteredSchedules?.map((schedule) => (
              <option key={schedule.id} value={schedule.id}>
                Schedule - {new Date(schedule.scheduled_date).toLocaleDateString()}
              </option>
            ))}
          </select>
          {!selectedProjectId && !hideProjectSection && (
            <p className="text-xs text-gray-500 mt-1">
              Select a project first to see available schedules
            </p>
          )}
          {errors.shoot_id && (
            <p className="text-sm text-red-600 mt-1">{errors.shoot_id.message}</p>
          )}
        </div>
      )}

      <div className="flex gap-3 pt-4">
        <Button
          type="submit"
          disabled={loading}
          className="flex-1"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Task' : 'Create Task'}
        </Button>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  )
}
