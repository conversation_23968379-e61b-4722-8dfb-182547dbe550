'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreateUser, useUpdateUser } from '@/hooks/useApi'
import { useAuth } from '@/contexts/AuthContext'
import toast from 'react-hot-toast'
import type { User } from '@/types'

// Create a dynamic schema based on user role
const createUserFormSchema = (isAdmin: boolean, isEditing: boolean) => {
  const baseSchema = {
    email: z.string().email('Invalid email address'),
    name: z.string().min(1, 'Name is required'),
    role: z.enum(['pilot', 'editor', 'manager', 'admin'], {
      errorMap: () => ({ message: 'Role is required' }),
    }),
  }

  // Only admins can set passwords, and only when creating new users or explicitly changing passwords
  if (isAdmin) {
    return z.object({
      ...baseSchema,
      password: isEditing 
        ? z.string().optional().refine((val) => !val || val.length >= 8, {
            message: 'Password must be at least 8 characters long'
          })
        : z.string().min(8, 'Password must be at least 8 characters long'),
    })
  }

  return z.object(baseSchema)
}

interface UserFormProps {
  user?: User
  onSuccess?: (user: User) => void
  onCancel?: () => void
}

export function UserForm({ user, onSuccess, onCancel }: UserFormProps) {
  const isEditing = !!user
  const { user: currentUser } = useAuth()
  const isAdmin = currentUser?.role === 'admin'
  const { createUser, loading: createLoading } = useCreateUser()
  const { updateUser, loading: updateLoading } = useUpdateUser()
  const loading = createLoading || updateLoading

  // Create schema based on current user's admin status
  const userFormSchema = createUserFormSchema(isAdmin, isEditing)
  type UserFormData = z.infer<typeof userFormSchema>

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: user ? {
      name: user.name,
      email: user.email,
      role: user.role,
      ...(isAdmin && { password: '' }), // Only include password field for admins
    } : {
      name: '',
      email: '',
      role: 'pilot',
      ...(isAdmin && { password: '' }), // Only include password field for admins
    },
  })

  const onSubmit = async (data: UserFormData) => {
    try {
      let result: User
      
      // Prepare data for submission
      const submitData = { ...data }
      
      // Handle password field for admins
      if (isAdmin && 'password' in data) {
        // For editing: only include password if it's not empty
        if (isEditing && (!data.password || data.password.trim() === '')) {
          // Remove password from data if it's empty (keep existing password)
          delete submitData.password
        }
        // For creating: password is required and already validated by schema
      }

      if (isEditing && user) {
        result = await updateUser(user.id, submitData)
        toast.success('User updated successfully')
      } else {
        result = await createUser(submitData)
        toast.success('User created successfully')
      }

      reset()
      onSuccess?.(result)
    } catch (error) {
      console.error('Error saving user:', error)
      toast.error(isEditing ? 'Failed to update user' : 'Failed to create user')
    }
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      {/* Form Content */}
      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {/* Name */}
        <div className="space-y-2">
          <Label htmlFor="name" className="text-sm font-medium text-foreground">
            Full Name *
          </Label>
          <Input
            id="name"
            {...register('name')}
            placeholder="Enter full name"
            className="modern-input"
          />
          {errors.name && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {errors.name.message}
            </p>
          )}
        </div>

        {/* Email */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium text-foreground">
            Email Address *
          </Label>
          <Input
            id="email"
            type="email"
            {...register('email')}
            placeholder="Enter email address"
            className="modern-input"
            disabled={isEditing} // Don't allow email changes for existing users
          />
          {errors.email && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {errors.email.message}
            </p>
          )}
          {isEditing && (
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Email cannot be changed for existing users
            </p>
          )}
        </div>

        {/* Role */}
        <div className="space-y-2">
          <Label htmlFor="role" className="text-sm font-medium text-foreground">
            Role *
          </Label>
          <select
            id="role"
            {...register('role')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="pilot">Pilot</option>
            <option value="editor">Editor</option>
            <option value="manager">Manager</option>
            <option value="admin">Admin</option>
          </select>
          {errors.role && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {errors.role.message}
            </p>
          )}
        </div>

        {/* Password - Only visible to admins */}
        {isAdmin && (
          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm font-medium text-foreground">
              Password {!isEditing && '*'}
            </Label>
            <Input
              id="password"
              type="password"
              {...register('password' as keyof UserFormData)}
              placeholder={isEditing ? "Leave blank to keep current password" : "Enter password"}
              className="modern-input"
            />
            {errors.password && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {errors.password.message}
              </p>
            )}
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {isEditing 
                ? "Only admins can change passwords. Leave blank to keep the current password."
                : "Password must be at least 8 characters long."
              }
            </p>
          </div>
        )}





        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {loading ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {isEditing ? 'Updating...' : 'Creating...'}
              </div>
            ) : (
              isEditing ? 'Update User' : 'Create User'
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}