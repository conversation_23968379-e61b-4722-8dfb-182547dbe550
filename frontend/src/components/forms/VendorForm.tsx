'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'
import type { OutsourcingVendor } from '@/types'
import { OUTSOURCING_CATEGORIES } from '@/lib/constants'

const vendorSchema = z.object({
  name: z.string().min(1, 'Vendor name is required'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.string().optional(),
  contact_person: z.string().optional(),
  specialization: z.string().optional(),
  notes: z.string().optional(),
  is_active: z.boolean()
})

type VendorFormData = z.infer<typeof vendorSchema>

interface VendorFormProps {
  vendor?: OutsourcingVendor
  onSuccess: () => void
  onCancel: () => void
}

export function VendorForm({ vendor, onSuccess, onCancel }: VendorFormProps) {
  const [loading, setLoading] = useState(false)
  const isEditing = !!vendor
  const [previewId, setPreviewId] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<VendorFormData>({
    resolver: zodResolver(vendorSchema),
    defaultValues: vendor ? {
      name: vendor.name,
      email: vendor.email || '',
      phone: vendor.phone || '',
      address: vendor.address || '',
      contact_person: vendor.contact_person || '',
      specialization: vendor.specialization || '',
      notes: vendor.notes || '',
      is_active: vendor.is_active
    } : {
      name: '',
      email: '',
      phone: '',
      address: '',
      contact_person: '',
      specialization: '',
      notes: '',
      is_active: true
    }
  })

  // Non-authoritative preview: fetch on mount for create mode only
  useEffect(() => {
    let mounted = true
    async function loadPreview() {
      try {
        const { previewNextVendorId } = await import('@/lib/api')
        const id = await previewNextVendorId()
        if (mounted) setPreviewId(id)
      } catch (e) {
        console.warn('VendorForm preview load failed:', e)
      }
    }
    if (typeof window !== 'undefined' && !isEditing) {
      loadPreview()
    }
    return () => { mounted = false }
  }, [isEditing])

  const onSubmit = async (data: VendorFormData) => {
    try {
      setLoading(true)

      const vendorData = {
        name: data.name,
        email: data.email || null,
        phone: data.phone || null,
        address: data.address || null,
        contact_person: data.contact_person || null,
        specialization: data.specialization || null,
        notes: data.notes || null,
        is_active: data.is_active,
        updated_at: new Date().toISOString()
      }

      if (isEditing) {
        const { error } = await supabase
          .from('outsourcing_vendors')
          .update(vendorData)
          .eq('id', vendor.id)

        if (error) throw error
        toast.success('Vendor updated successfully')
      } else {
        const { error } = await supabase
          .from('outsourcing_vendors')
          .insert([{
            ...vendorData,
            created_at: new Date().toISOString()
          }])

        if (error) throw error
        toast.success('Vendor created successfully')
      }

      reset()
      onSuccess()
    } catch (error: any) {
      console.error('Error saving vendor:', error)
      toast.error(error.message || 'Failed to save vendor')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {/* Structured ID display */}
      {isEditing && (vendor as any)?.custom_id && (
        <div className="text-xs text-muted-foreground -mb-2">
          ID: {(vendor as any).custom_id}
        </div>
      )}
      {!isEditing && previewId && (
        <div className="text-xs text-muted-foreground -mb-2">
          ID (preview): {previewId}
        </div>
      )}

      {/* Name */}
      <div>
        <Label htmlFor="name" className="text-sm font-medium">
          Vendor Name *
        </Label>
        <Input
          id="name"
          {...register('name')}
          placeholder="Enter vendor name"
          className="mt-1"
        />
        {errors.name && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.name.message}</p>
        )}
      </div>

      {/* Email */}
      <div>
        <Label htmlFor="email" className="text-sm font-medium">
          Email
        </Label>
        <Input
          id="email"
          type="email"
          {...register('email')}
          placeholder="Enter email address"
          className="mt-1"
        />
        {errors.email && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.email.message}</p>
        )}
      </div>

      {/* Phone */}
      <div>
        <Label htmlFor="phone" className="text-sm font-medium">
          Phone
        </Label>
        <Input
          id="phone"
          {...register('phone')}
          placeholder="Enter phone number"
          className="mt-1"
        />
        {errors.phone && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.phone.message}</p>
        )}
      </div>

      {/* Contact Person */}
      <div>
        <Label htmlFor="contact_person" className="text-sm font-medium">
          Contact Person
        </Label>
        <Input
          id="contact_person"
          {...register('contact_person')}
          placeholder="Enter contact person name"
          className="mt-1"
        />
        {errors.contact_person && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.contact_person.message}</p>
        )}
      </div>

      {/* Specialization */}
      <div>
        <Label htmlFor="specialization" className="text-sm font-medium">
          Specialization
        </Label>
        <select
          id="specialization"
          {...register('specialization')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="">Select specialization</option>
          {OUTSOURCING_CATEGORIES.map((category) => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
        {errors.specialization && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.specialization.message}</p>
        )}
      </div>

      {/* Address */}
      <div>
        <Label htmlFor="address" className="text-sm font-medium">
          Address
        </Label>
        <textarea
          id="address"
          {...register('address')}
          placeholder="Enter vendor address"
          className="mt-1 flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          rows={2}
        />
        {errors.address && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.address.message}</p>
        )}
      </div>

      {/* Notes */}
      <div>
        <Label htmlFor="notes" className="text-sm font-medium">
          Notes
        </Label>
        <textarea
          id="notes"
          {...register('notes')}
          placeholder="Additional notes about the vendor"
          className="mt-1 flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          rows={3}
        />
        {errors.notes && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.notes.message}</p>
        )}
      </div>

      {/* Active Status */}
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="is_active"
          {...register('is_active')}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <Label htmlFor="is_active" className="text-sm font-medium">
          Active vendor
        </Label>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Vendor' : 'Create Vendor'}
        </Button>
      </div>
    </form>
  )
}
