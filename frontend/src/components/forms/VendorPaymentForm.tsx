'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { projectsApi } from '@/lib/api'
import toast from 'react-hot-toast'
import type { Project } from '@/types'

const vendorPaymentSchema = z.object({
  vendor_payment_status: z.enum(['pending', 'paid', 'overdue', 'cancelled']),
  vendor_payment_amount: z.number().min(0, 'Amount must be positive'),
  vendor_payment_due_date: z.string().optional(),
  vendor_payment_date: z.string().optional(),
  vendor_payment_notes: z.string().optional(),
})

type VendorPaymentFormData = z.infer<typeof vendorPaymentSchema>

interface VendorPaymentFormProps {
  project: Project
  onSuccess: () => void
  onCancel: () => void
}

export function VendorPaymentForm({ project, onSuccess, onCancel }: VendorPaymentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<VendorPaymentFormData>({
    resolver: zodResolver(vendorPaymentSchema),
    defaultValues: {
      vendor_payment_status: project.vendor_payment_status || 'pending',
      vendor_payment_amount: project.vendor_payment_amount || 0,
      vendor_payment_due_date: project.vendor_payment_due_date ? 
        new Date(project.vendor_payment_due_date).toISOString().slice(0, 10) : '',
      vendor_payment_date: project.vendor_payment_date ? 
        new Date(project.vendor_payment_date).toISOString().slice(0, 10) : '',
      vendor_payment_notes: project.vendor_payment_notes || '',
    },
  })

  const paymentStatus = watch('vendor_payment_status')

  const onSubmit = async (data: VendorPaymentFormData) => {
    try {
      setIsSubmitting(true)
      
      const updateData = {
        vendor_payment_status: data.vendor_payment_status,
        vendor_payment_amount: data.vendor_payment_amount,
        vendor_payment_due_date: data.vendor_payment_due_date || null,
        vendor_payment_date: data.vendor_payment_date || null,
        vendor_payment_notes: data.vendor_payment_notes || null,
      }

      await projectsApi.update(project.id, updateData)
      toast.success('Vendor payment updated successfully')
      onSuccess()
    } catch (error: any) {
      toast.error(error.message || 'Failed to update vendor payment')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Payment Status */}
        <div>
          <Label htmlFor="vendor_payment_status" className="text-sm font-medium">
            Payment Status
          </Label>
          <select
            id="vendor_payment_status"
            {...register('vendor_payment_status')}
            className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm ring-offset-background file:border-0 file:bg-background file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
          >
            <option value="pending">Pending Payment</option>
            <option value="paid">Paid</option>
            <option value="overdue">Overdue</option>
            <option value="cancelled">Cancelled</option>
          </select>
          {errors.vendor_payment_status && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.vendor_payment_status.message}</p>
          )}
        </div>

        {/* Payment Amount */}
        <div>
          <Label htmlFor="vendor_payment_amount" className="text-sm font-medium">
            Payment Amount (₹)
          </Label>
          <Input
            id="vendor_payment_amount"
            type="number"
            step="0.01"
            min="0"
            {...register('vendor_payment_amount', { valueAsNumber: true })}
            className="mt-1"
          />
          {errors.vendor_payment_amount && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.vendor_payment_amount.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Payment Due Date */}
        <div>
          <Label htmlFor="vendor_payment_due_date" className="text-sm font-medium">
            Payment Due Date
          </Label>
          <Input
            id="vendor_payment_due_date"
            type="date"
            {...register('vendor_payment_due_date')}
            className="mt-1"
          />
          {errors.vendor_payment_due_date && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.vendor_payment_due_date.message}</p>
          )}
        </div>

        {/* Payment Date - only show if status is paid */}
        {paymentStatus === 'paid' && (
          <div>
            <Label htmlFor="vendor_payment_date" className="text-sm font-medium">
              Payment Date
            </Label>
            <Input
              id="vendor_payment_date"
              type="date"
              {...register('vendor_payment_date')}
              className="mt-1"
            />
            {errors.vendor_payment_date && (
              <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.vendor_payment_date.message}</p>
            )}
          </div>
        )}
      </div>

      {/* Payment Notes */}
      <div>
        <Label htmlFor="vendor_payment_notes" className="text-sm font-medium">
          Payment Notes
        </Label>
        <textarea
          id="vendor_payment_notes"
          {...register('vendor_payment_notes')}
          rows={3}
          className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm ring-offset-background file:border-0 file:bg-background file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
          placeholder="Add payment notes, reference numbers, or other details..."
        />
        {errors.vendor_payment_notes && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.vendor_payment_notes.message}</p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-border">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Updating...' : 'Update Payment'}
        </Button>
      </div>
    </form>
  )
}
