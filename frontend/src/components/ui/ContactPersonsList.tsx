'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Modal } from '@/components/ui/modal'
import { ContactPersonForm } from '@/components/forms/ContactPersonForm'
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { contactPersonsApi } from '@/lib/api'
import { Phone, Mail, Star, Edit, Trash2, Plus, User, Briefcase, ExternalLink, MoreVertical } from 'lucide-react'
import toast from 'react-hot-toast'
import type { ContactPerson, Project } from '@/types'

interface ContactPersonsListProps {
  clientId: string
  projects?: Project[]
  onContactsChange?: () => void
}

export function ContactPersonsList({ clientId, projects = [], onContactsChange }: ContactPersonsListProps) {
  const [contacts, setContacts] = useState<ContactPerson[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingContact, setEditingContact] = useState<ContactPerson | null>(null)
  const [deletingContactId, setDeletingContactId] = useState<string | null>(null)
  const [selectedContact, setSelectedContact] = useState<ContactPerson | null>(null)

  const fetchContacts = async () => {
    try {
      setLoading(true)
      const data = await contactPersonsApi.getByClientId(clientId)
      setContacts(data)
    } catch (error: any) {
      toast.error('Failed to load contact persons')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchContacts()
  }, [clientId])

  const handleAddSuccess = () => {
    setShowAddForm(false)
    fetchContacts()
    onContactsChange?.()
  }

  const handleEditSuccess = () => {
    setEditingContact(null)
    fetchContacts()
    onContactsChange?.()
  }

  // Get project count for a contact person
  const getProjectCount = (contactId: string) => {
    // For now, we'll assume all projects are associated with all contacts
    // In a real scenario, you'd have a relationship table between contacts and projects
    return projects.length
  }

  // Get projects for a contact person
  const getContactProjects = (contactId: string) => {
    // For now, return all projects. In reality, you'd filter based on contact-project relationships
    return projects
  }

  const handleDelete = async (contact: ContactPerson) => {
    if (!confirm(`Are you sure you want to delete "${contact.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      setDeletingContactId(contact.id)
      await contactPersonsApi.delete(contact.id)
      toast.success('Contact person deleted successfully')
      fetchContacts()
      onContactsChange?.()
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete contact person')
    } finally {
      setDeletingContactId(null)
    }
  }

  const handleSetPrimary = async (contact: ContactPerson) => {
    try {
      await contactPersonsApi.setPrimary(contact.id, clientId)
      toast.success(`${contact.name} set as primary contact`)
      fetchContacts()
      onContactsChange?.()
    } catch (error: any) {
      toast.error('Failed to set primary contact')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-foreground">Contact Persons</h3>
        <Button
          onClick={() => setShowAddForm(!showAddForm)}
          size="sm"
          variant="outline"
        >
          <Plus className="w-4 h-4 mr-2" />
          {showAddForm ? 'Cancel' : 'Add Contact'}
        </Button>
      </div>

      {/* Inline Add Contact Form */}
      {showAddForm && (
        <div className="border border-border rounded-lg p-4 bg-muted/50">
          <h4 className="text-md font-medium text-foreground mb-4">Add New Contact Person</h4>
          <ContactPersonForm
            clientId={clientId}
            onSuccess={handleAddSuccess}
            onCancel={() => setShowAddForm(false)}
          />
        </div>
      )}

      {contacts.length === 0 ? (
        <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg">
          <User className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No contact persons</h3>
          <p className="text-muted-foreground mb-4">Add contact persons to manage client relationships</p>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add First Contact
          </Button>
        </div>
      ) : (
        <div className="space-y-3">
          {contacts.map((contact) => (
            <div
              key={contact.id}
              className="p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4
                      className="font-medium text-foreground hover:text-blue-600 cursor-pointer"
                      onClick={() => setSelectedContact(contact)}
                    >
                      {contact.name}
                    </h4>
                    {contact.is_primary && (
                      <div className="flex items-center space-x-1 text-yellow-600 dark:text-yellow-400">
                        <Star className="w-4 h-4 fill-current" />
                        <span className="text-xs font-medium">Primary</span>
                      </div>
                    )}
                  </div>
                  
                  {contact.designation && (
                    <p className="text-sm text-muted-foreground">{contact.designation}</p>
                  )}
                  
                  <div className="flex items-center space-x-4 mt-2">
                    {contact.phone && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Phone className="w-4 h-4 mr-1" />
                        {contact.phone}
                      </div>
                    )}
                    {contact.email && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Mail className="w-4 h-4 mr-1" />
                        {contact.email}
                      </div>
                    )}
                  </div>

                  {/* Project Count */}
                  <div className="flex items-center text-sm text-muted-foreground mt-2">
                    <Briefcase className="w-4 h-4 mr-1" />
                    <span>{getProjectCount(contact.id)} projects</span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {/* Primary Contact Star */}
                  {contact.is_primary && (
                    <div className="text-yellow-500">
                      <Star className="w-4 h-4 fill-current" />
                    </div>
                  )}

                  {/* Quick Action Buttons */}
                  {contact.phone && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        window.open(`tel:${contact.phone}`, '_self')
                      }}
                      title={`Call ${contact.phone}`}
                      className="h-8 w-8 p-0"
                    >
                      <Phone className="w-4 h-4" />
                    </Button>
                  )}
                  {contact.email && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        window.open(`mailto:${contact.email}`, '_self')
                      }}
                      title={`Email ${contact.email}`}
                      className="h-8 w-8 p-0"
                    >
                      <Mail className="w-4 h-4" />
                    </Button>
                  )}

                  {/* Actions Dropdown */}
                  <div onClick={(e) => e.stopPropagation()}>
                    <DropdownMenu
                      trigger={
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      }
                    >
                    {/* Management Actions */}
                    {!contact.is_primary && (
                      <DropdownMenuItem
                        onClick={() => handleSetPrimary(contact)}
                      >
                        <Star className="w-4 h-4 mr-2" />
                        Set as Primary
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      onClick={() => setEditingContact(contact)}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Contact
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleDelete(contact)}
                      disabled={deletingContactId === contact.id}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Contact
                    </DropdownMenuItem>
                  </DropdownMenu>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Edit Contact Modal */}
      <Modal
        isOpen={!!editingContact}
        onClose={() => setEditingContact(null)}
        title="Edit Contact Person"
      >
        {editingContact && (
          <ContactPersonForm
            clientId={clientId}
            contactPerson={editingContact}
            onSuccess={handleEditSuccess}
            onCancel={() => setEditingContact(null)}
          />
        )}
      </Modal>

      {/* Contact Person Projects Modal */}
      <Modal
        isOpen={!!selectedContact}
        onClose={() => setSelectedContact(null)}
        title={`${selectedContact?.name} - Projects`}
        size="lg"
      >
        {selectedContact && (
          <div className="space-y-4">
            {/* Contact Info */}
            <div className="border-b border-border pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-foreground">{selectedContact.name}</h3>
                  {selectedContact.designation && (
                    <p className="text-sm text-muted-foreground">{selectedContact.designation}</p>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {selectedContact.phone && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`tel:${selectedContact.phone}`, '_self')}
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Call
                    </Button>
                  )}
                  {selectedContact.email && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`mailto:${selectedContact.email}`, '_self')}
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Email
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Projects List */}
            <div>
              <h4 className="text-md font-medium text-foreground mb-3">Associated Projects</h4>
              {getContactProjects(selectedContact.id).length === 0 ? (
                <div className="text-center py-8">
                  <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No projects associated with this contact.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {getContactProjects(selectedContact.id).map((project) => (
                    <div
                      key={project.id}
                      className="border border-border rounded-lg p-4 hover:bg-muted/50 cursor-pointer transition-colors"
                      onClick={() => {
                        setSelectedContact(null)
                        window.open(`/projects/${project.id}`, '_blank')
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="font-medium text-foreground hover:text-blue-600">{project.name}</h5>
                          <p className="text-sm text-muted-foreground">{project.description}</p>
                          <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                            <span className="capitalize">{project.status}</span>
                            <span>₹{project.total_amount.toLocaleString()}</span>
                          </div>
                        </div>
                        <ExternalLink className="w-4 h-4 text-muted-foreground" />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}
