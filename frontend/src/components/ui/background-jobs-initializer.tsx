'use client'

import { useEffect } from 'react'

export function BackgroundJobsInitializer() {
  useEffect(() => {
    // Only initialize background jobs on dashboard pages
    if (typeof window !== 'undefined' && window.location.pathname.startsWith('/dashboard')) {
      // Lazy load background jobs initialization
      import('@/lib/init-background-jobs').then(({ initializeBackgroundJobs }) => {
        initializeBackgroundJobs()
      })
    }
  }, [])

  return null
}