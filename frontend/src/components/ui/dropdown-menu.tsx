'use client'

import React, { useState, useRef, useEffect, useContext } from 'react'
import { Button } from '@/components/ui/button'

interface DropdownMenuProps {
  trigger: React.ReactNode
  children: React.ReactNode
  align?: 'left' | 'right'
}

interface DropdownContextType {
  closeDropdown: () => void
}

const DropdownContext = React.createContext<DropdownContextType | null>(null)

interface DropdownMenuItemProps {
  onClick?: () => void
  children: React.ReactNode
  className?: string
  disabled?: boolean
}

export function DropdownMenu({ trigger, children, align = 'right' }: DropdownMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsOpen(!isOpen)
  }

  return (
    <DropdownContext.Provider value={{ closeDropdown: () => setIsOpen(false) }}>
      <div className="relative" ref={dropdownRef}>
        <div onClick={handleToggle} className="cursor-pointer">
          {trigger}
        </div>

        {isOpen && (
          <div className={`absolute top-full mt-1 w-48 bg-popover text-popover-foreground border border-border rounded-md shadow-2xl z-[100] ${
            align === 'right' ? 'right-0' : 'left-0'
          }`}>
            <div className="py-1 bg-popover rounded-md">
              {children}
            </div>
          </div>
        )}
      </div>
    </DropdownContext.Provider>
  )
}

export function DropdownMenuItem({ onClick, children, className = '', disabled = false }: DropdownMenuItemProps) {
  const context = useContext(DropdownContext)

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick()
      context?.closeDropdown()
    }
  }

  return (
    <button
      onClick={handleClick}
      disabled={disabled}
      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
      } ${className}`}
    >
      {children}
    </button>
  )
}

// Export aliases for compatibility with Radix UI naming convention
export const DropdownMenuTrigger = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => children
export const DropdownMenuContent = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => children
