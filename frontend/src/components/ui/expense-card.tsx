'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { formatDate } from '@/lib/utils'
import { 
  Fuel, 
  Wrench, 
  Car, 
  Settings, 
  Package, 
  Edit, 
  Trash2,
  User,
  Building,
  Receipt,
  ExternalLink
} from 'lucide-react'
import type { Expense, Project, User as AppUser } from '@/types'

type ExpenseWithRelations = Expense & {
  // These relation fields are provided by queries in the app layer (selected via API)
  project?: Pick<Project, 'id' | 'name' | 'custom_id'>
  user?: Pick<AppUser, 'id' | 'name'>
}

interface ExpenseCardProps {
  expense: ExpenseWithRelations
  onEdit?: (expense: ExpenseWithRelations) => void
  onDelete?: (expense: ExpenseWithRelations) => void
  compact?: boolean
}

export function ExpenseCard({ expense, onEdit, onDelete, compact = false }: ExpenseCardProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'cymatics':
        return <Package className="w-4 h-4" />
      case 'salary':
        return <Settings className="w-4 h-4" />
      case 'gadgets':
        return <Package className="w-4 h-4" />
      case 'outsourcing':
        return <Settings className="w-4 h-4" />
      case 'asset':
        return <Package className="w-4 h-4" />
      case 'loan_repayment':
        return <Settings className="w-4 h-4" />
      case 'investments':
        return <Settings className="w-4 h-4" />
      case 'fuel_travel':
        return <Car className="w-4 h-4" />
      case 'food_snacks':
        return <Package className="w-4 h-4" />
      case 'others':
        return <Settings className="w-4 h-4" />
      case 'entertainment':
        return <Settings className="w-4 h-4" />
      case 'gopi':
        return <Settings className="w-4 h-4" />
      case 'yaso':
        return <Settings className="w-4 h-4" />
      case 'adithyan':
        return <Settings className="w-4 h-4" />
      default:
        return <Package className="w-4 h-4" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'cymatics':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20'
      case 'salary':
        return 'bg-green-500/10 text-green-500 border-green-500/20'
      case 'gadgets':
        return 'bg-purple-500/10 text-purple-500 border-purple-500/20'
      case 'outsourcing':
        return 'bg-orange-500/10 text-orange-500 border-orange-500/20'
      case 'asset':
        return 'bg-indigo-500/10 text-indigo-500 border-indigo-500/20'
      case 'loan_repayment':
        return 'bg-red-500/10 text-red-500 border-red-500/20'
      case 'investments':
        return 'bg-emerald-500/10 text-emerald-500 border-emerald-500/20'
      case 'fuel_travel':
        return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20'
      case 'food_snacks':
        return 'bg-pink-500/10 text-pink-500 border-pink-500/20'
      case 'others':
        return 'bg-muted text-muted-foreground border-border'
      case 'entertainment':
        return 'bg-violet-500/10 text-violet-500 border-violet-500/20'
      case 'gopi':
        return 'bg-cyan-500/10 text-cyan-500 border-cyan-500/20'
      case 'yaso':
        return 'bg-teal-500/10 text-teal-500 border-teal-500/20'
      case 'adithyan':
        return 'bg-amber-500/10 text-amber-500 border-amber-500/20'
      default:
        return 'bg-muted text-muted-foreground border-border'
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'fuel':
        return 'Fuel'
      case 'equipment':
        return 'Equipment'
      case 'travel':
        return 'Travel'
      case 'maintenance':
        return 'Maintenance'
      case 'other':
        return 'Other'
      default:
        return category
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete this expense of ₹${expense.amount.toLocaleString()}? This action cannot be undone.`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(expense)
    } finally {
      setIsDeleting(false)
    }
  }

  if (compact) {
    return (
      <div className="app-card p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getCategoryColor(expense.category)}`}>
                {getCategoryIcon(expense.category)}
                <span className="ml-1">{getCategoryLabel(expense.category)}</span>
              </div>
              <div>
                <h4 className="font-medium text-foreground">₹{expense.amount.toLocaleString()}</h4>
                <p className="text-sm text-muted-foreground">{expense.description}</p>
              </div>
            </div>
            <div className="mt-2 flex items-center space-x-4 text-sm text-muted-foreground">
              <span>{formatDate(expense.date)}</span>
              {expense.project?.name && (
                <span>{expense.project.name}</span>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {expense.receipt_url && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(expense.receipt_url!, '_blank')}
              >
                <Receipt className="w-4 h-4" />
              </Button>
            )}
            {onEdit && (
              <Button variant="outline" size="sm" onClick={() => onEdit(expense)}>
                <Edit className="w-4 h-4" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="app-card hover:shadow-md transition-colors overflow-hidden">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <div className="text-2xl font-bold text-destructive">
                -₹{expense.amount.toLocaleString()}
              </div>
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getCategoryColor(expense.category)}`}>
                {getCategoryIcon(expense.category)}
                <span className="ml-1">{getCategoryLabel(expense.category)}</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-card-foreground mb-1">
                {expense.description}
              </h3>
              {expense.project?.custom_id && (
                <span
                  className="px-2 py-0.5 rounded border border-border text-[10px] text-muted-foreground bg-muted"
                  title="Project ID"
                >
                  {expense.project.custom_id}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Expense Details */}
        <div className="space-y-2 mb-4 text-sm text-muted-foreground">
          <div className="flex items-center">
            <span className="font-medium">Date:</span>
            <span className="ml-2">{formatDate(expense.date)}</span>
          </div>
          
          {expense.user?.name && (
            <div className="flex items-center">
              <User className="w-4 h-4 mr-1" />
              <span className="font-medium">By:</span>
              <span className="ml-2">{expense.user.name}</span>
            </div>
          )}

          {expense.project?.name && (
            <div className="flex items-center">
              <Building className="w-4 h-4 mr-1" />
              <span className="font-medium">Project:</span>
              <span className="ml-2">{expense.project.name}</span>
            </div>
          )}
        </div>

        {/* Receipt */}
        {expense.receipt_url && (
          <div className="mb-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(expense.receipt_url!, '_blank')}
              className="text-xs"
            >
              <Receipt className="w-3 h-3 mr-1" />
              View Receipt
              <ExternalLink className="w-3 h-3 ml-1" />
            </Button>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-muted border-t border-border">
        <div className="flex items-center justify-between">
          <div className="text-xs text-muted-foreground">
            Recorded {formatDate(expense.created_at)}
          </div>
          
          <div className="flex items-center space-x-2">
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(expense)}
                className="text-xs"
              >
                <Edit className="w-3 h-3" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-xs"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
