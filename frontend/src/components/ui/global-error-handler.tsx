'use client'

import { useEffect } from 'react'
import toast from 'react-hot-toast'

/**
 * Global Error Handler Component
 * Catches and handles unhandled errors, including external API calls
 */
export function GlobalErrorHandler() {
  useEffect(() => {
    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.warn('Unhandled promise rejection:', event.reason)
      
      // Check if it's a webviewClick related error
      if (event.reason?.message?.includes('webviewClick') || 
          event.reason?.message?.includes('请求超时')) {
        // Silently handle webviewClick errors as they're likely from external sources
        event.preventDefault()
        console.log('Handled external webviewClick error')
        return
      }
      
      // For other errors, show a toast notification
      if (event.reason?.message && !event.reason.message.includes('ChunkLoadError')) {
        toast.error('An unexpected error occurred. Please try again.')
      }
    }

    // Handle general JavaScript errors
    const handleError = (event: ErrorEvent) => {
      console.warn('Global error:', event.error)
      
      // Check if it's a webviewClick related error
      if (event.error?.message?.includes('webviewClick') || 
          event.error?.message?.includes('请求超时')) {
        // Silently handle webviewClick errors
        event.preventDefault()
        console.log('Handled external webviewClick error')
        return
      }
      
      // For other errors, show a toast notification
      if (event.error?.message && !event.error.message.includes('ChunkLoadError')) {
        toast.error('An unexpected error occurred. Please try again.')
      }
    }

    // Add event listeners
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    window.addEventListener('error', handleError)

    // Cleanup
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      window.removeEventListener('error', handleError)
    }
  }, [])

  return null // This component doesn't render anything
}