'use client'

import { useState } from 'react'
import { BarChart3, TrendingUp, TrendingDown, Calendar } from 'lucide-react'

interface SpendingData {
  month: string
  amount: number
}

interface InteractiveSpendingChartProps {
  data: SpendingData[]
  title?: string
}

export function InteractiveSpendingChart({ data, title = "Monthly Spending Trend" }: InteractiveSpendingChartProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null)

  if (!data || data.length === 0) {
    return (
      <div className="bg-card text-card-foreground rounded-xl border border-border p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <BarChart3 className="w-5 h-5" />
          {title}
        </h2>
        <div className="text-center py-8">
          <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No spending data available</p>
        </div>
      </div>
    )
  }

  const maxAmount = Math.max(...data.map(d => d.amount))
  const minAmount = Math.min(...data.map(d => d.amount))
  const totalSpending = data.reduce((sum, d) => sum + d.amount, 0)
  const averageSpending = totalSpending / data.length

  // Calculate trend
  const firstHalf = data.slice(0, Math.ceil(data.length / 2))
  const secondHalf = data.slice(Math.ceil(data.length / 2))
  const firstHalfAvg = firstHalf.reduce((sum, d) => sum + d.amount, 0) / firstHalf.length
  const secondHalfAvg = secondHalf.reduce((sum, d) => sum + d.amount, 0) / secondHalf.length
  const trendPercentage = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatMonth = (month: string) => {
    const date = new Date(month + '-01')
    return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' })
  }

  return (
    <div className="bg-card text-card-foreground rounded-xl border border-border p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold flex items-center gap-2">
          <BarChart3 className="w-5 h-5" />
          {title}
        </h2>
        <div className="flex items-center gap-2 text-sm">
          {trendPercentage > 0 ? (
            <div className="flex items-center gap-1 text-destructive">
              <TrendingUp className="w-4 h-4" />
              <span>+{trendPercentage.toFixed(1)}%</span>
            </div>
          ) : (
            <div className="flex items-center gap-1 text-green-600">
              <TrendingDown className="w-4 h-4" />
              <span>{trendPercentage.toFixed(1)}%</span>
            </div>
          )}
        </div>
      </div>

      {/* Chart Area */}
      <div className="relative">
        {/* Chart Container */}
        <div className="flex items-end justify-between gap-2 h-48 mb-4">
          {data.map((item, index) => {
            const percentage = (item.amount / maxAmount) * 100
            const isHovered = hoveredIndex === index
            const isSelected = selectedIndex === index
            
            return (
              <div
                key={index}
                className="flex-1 flex flex-col items-center cursor-pointer group"
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
                onClick={() => setSelectedIndex(selectedIndex === index ? null : index)}
              >
                {/* Tooltip */}
                {(isHovered || isSelected) && (
                  <div className="absolute -top-16 bg-popover text-popover-foreground px-3 py-2 rounded-lg text-sm font-medium shadow-lg z-10 transform -translate-x-1/2 left-1/2 border border-border">
                    <div className="text-center">
                      <div className="font-semibold">{formatMonth(item.month)}</div>
                      <div className="text-muted-foreground">{formatCurrency(item.amount)}</div>
                    </div>
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent" />
                  </div>
                )}
                
                {/* Bar */}
                <div
                  className={`w-full rounded-t-lg transition-all duration-300 ${
                    isHovered || isSelected
                      ? 'bg-primary shadow-lg'
                      : 'bg-primary/80 hover:bg-primary'
                  }`}
                  style={{ height: `${Math.max(percentage, 5)}%` }}
                />
                
                {/* Month Label */}
                <div className="mt-2 text-xs text-muted-foreground font-medium">
                  {formatMonth(item.month)}
                </div>
              </div>
            )
          })}
        </div>

        {/* Selected Item Details */}
        {selectedIndex !== null && (
          <div className="bg-accent text-accent-foreground rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-primary" />
                <span className="font-medium">
                  {formatMonth(data[selectedIndex].month)}
                </span>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold">
                  {formatCurrency(data[selectedIndex].amount)}
                </div>
                <div className="text-sm text-muted-foreground">
                  {((data[selectedIndex].amount / totalSpending) * 100).toFixed(1)}% of total
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t border-border">
        <div className="text-center">
          <div className="text-sm text-muted-foreground">Total</div>
          <div className="font-semibold text-foreground">{formatCurrency(totalSpending)}</div>
        </div>
        <div className="text-center">
          <div className="text-sm text-muted-foreground">Average</div>
          <div className="font-semibold text-foreground">{formatCurrency(averageSpending)}</div>
        </div>
        <div className="text-center">
          <div className="text-sm text-muted-foreground">Peak</div>
          <div className="font-semibold text-foreground">{formatCurrency(maxAmount)}</div>
        </div>
      </div>
    </div>
  )
}
