'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  MapPin, 
  TrendingUp, 
  Clock, 
  DollarSign,
  Camera,
  Building,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Calendar,
  Target
} from 'lucide-react'

interface LocationData {
  name: string
  totalProjects: number
  totalShoots: number
  revenue: number
  flightHours: number
  lastVisit: string
  popularMonths: string[]
  projectTypes: Array<{
    type: string
    count: number
    percentage: number
  }>
}

interface LocationAnalyticsProps {
  isOpen: boolean
  onClose: () => void
  locationName?: string
}

export function LocationAnalytics({ isOpen, onClose, locationName }: LocationAnalyticsProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'projects' | 'revenue' | 'trends'>('overview')

  // Mock data - in real app this would come from API
  const locationData: LocationData = {
    name: locationName || 'Mumbai, Maharashtra',
    totalProjects: 12,
    totalShoots: 28,
    revenue: 450000,
    flightHours: 45.5,
    lastVisit: '2024-01-15',
    popularMonths: ['October', 'November', 'December', 'January'],
    projectTypes: [
      { type: 'Real Estate', count: 8, percentage: 67 },
      { type: 'Events', count: 3, percentage: 25 },
      { type: 'Commercial', count: 1, percentage: 8 }
    ]
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-background flex items-center justify-center z-50 p-4">
       <div className="bg-card rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <BarChart3 className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-card-foreground">Location Analytics</h2>
                <p className="text-sm text-muted-foreground">{locationData.name}</p>
              </div>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-border">
          <div className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'projects', label: 'Projects', icon: Building },
              { id: 'revenue', label: 'Revenue', icon: DollarSign },
              { id: 'trends', label: 'Trends', icon: TrendingUp }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[500px] overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Building className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-800 dark:text-blue-300">Projects</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{locationData.totalProjects}</div>
                </div>

                <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Camera className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    <span className="text-sm font-medium text-purple-800 dark:text-purple-300">Shoots</span>
                  </div>
                  <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">{locationData.totalShoots}</div>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
                    <span className="text-sm font-medium text-green-800 dark:text-green-300">Revenue</span>
                  </div>
                  <div className="text-2xl font-bold text-green-900 dark:text-green-100">₹{locationData.revenue.toLocaleString()}</div>
                </div>

                <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Clock className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                    <span className="text-sm font-medium text-orange-800 dark:text-orange-300">Flight Hours</span>
                  </div>
                  <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">{locationData.flightHours}h</div>
                </div>
              </div>

              {/* Project Types Distribution */}
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="text-lg font-semibold text-card-foreground mb-4">Project Types</h3>
                <div className="space-y-3">
                  {locationData.projectTypes.map((type, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          index === 0 ? 'bg-blue-500' :
                          index === 1 ? 'bg-purple-500' :
                          'bg-green-500'
                        }`}></div>
                        <span className="text-sm font-medium text-card-foreground">{type.type}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">{type.count} projects</span>
                        <span className="text-sm font-bold text-card-foreground">{type.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="text-lg font-semibold text-card-foreground mb-4">Recent Activity</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-muted-foreground">Last visit:</span>
                    <span className="font-medium text-card-foreground">
                      {new Date(locationData.lastVisit).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-muted-foreground">Average project value:</span>
                    <span className="font-medium text-card-foreground">
                      ₹{Math.round(locationData.revenue / locationData.totalProjects).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-muted-foreground">Flight hours per project:</span>
                    <span className="font-medium text-card-foreground">
                      {(locationData.flightHours / locationData.totalProjects).toFixed(1)}h
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'projects' && (
            <div className="space-y-4">
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="text-lg font-semibold text-card-foreground mb-4">Project History</h3>
                <div className="space-y-3">
                  {[
                    { name: 'Luxury Villa Photography', type: 'Real Estate', status: 'Completed', date: '2024-01-15', revenue: 150000 },
                    { name: 'Corporate Office Showcase', type: 'Commercial', status: 'Active', date: '2024-01-10', revenue: 80000 },
                    { name: 'Wedding Venue Coverage', type: 'Events', status: 'Completed', date: '2023-12-20', revenue: 120000 },
                    { name: 'Residential Complex', type: 'Real Estate', status: 'Completed', date: '2023-12-15', revenue: 200000 }
                  ].map((project, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted">
                      <div className="flex-1">
                        <div className="font-medium text-card-foreground">{project.name}</div>
                        <div className="text-sm text-muted-foreground">{project.type} • {project.date}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-card-foreground">₹{project.revenue.toLocaleString()}</div>
                        <div className={`text-xs px-2 py-1 rounded-full ${
                          project.status === 'Completed'
                            ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300'
                            : 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300'
                        }`}>
                          {project.status}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'revenue' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <h4 className="font-semibold text-green-800 dark:text-green-300 mb-2">Total Revenue</h4>
                  <div className="text-3xl font-bold text-green-900 dark:text-green-100">₹{locationData.revenue.toLocaleString()}</div>
                  <div className="text-sm text-green-700 dark:text-green-400 mt-1">From {locationData.totalProjects} projects</div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">Average Project Value</h4>
                  <div className="text-3xl font-bold text-blue-900 dark:text-blue-100">
                    ₹{Math.round(locationData.revenue / locationData.totalProjects).toLocaleString()}
                  </div>
                  <div className="text-sm text-blue-700 dark:text-blue-400 mt-1">Per project average</div>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="text-lg font-semibold text-card-foreground mb-4">Revenue by Project Type</h3>
                <div className="space-y-3">
                  {locationData.projectTypes.map((type, index) => {
                    const typeRevenue = Math.round(locationData.revenue * (type.percentage / 100))
                    return (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-4 h-4 rounded ${
                            index === 0 ? 'bg-blue-500' :
                            index === 1 ? 'bg-purple-500' :
                            'bg-green-500'
                          }`}></div>
                          <span className="font-medium text-card-foreground">{type.type}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-card-foreground">₹{typeRevenue.toLocaleString()}</div>
                          <div className="text-sm text-muted-foreground">{type.percentage}% of total</div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'trends' && (
            <div className="space-y-6">
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="text-lg font-semibold text-card-foreground mb-4">Seasonal Trends</h3>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-card-foreground mb-2">Peak Months</h4>
                    <div className="flex flex-wrap gap-2">
                      {locationData.popularMonths.map((month, index) => (
                        <span key={index} className="px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium">
                          {month}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="text-lg font-semibold text-card-foreground mb-4">Performance Metrics</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 border border-border rounded-lg">
                    <div className="text-2xl font-bold text-card-foreground">{(locationData.totalShoots / locationData.totalProjects).toFixed(1)}</div>
                    <div className="text-sm text-muted-foreground">Shoots per Project</div>
                  </div>
                  <div className="text-center p-3 border border-border rounded-lg">
                    <div className="text-2xl font-bold text-card-foreground">{(locationData.flightHours / locationData.totalShoots).toFixed(1)}h</div>
                    <div className="text-sm text-muted-foreground">Hours per Shoot</div>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <h4 className="font-semibold text-yellow-800 dark:text-yellow-300 mb-2">Insights & Recommendations</h4>
                <ul className="space-y-1 text-sm text-yellow-700 dark:text-yellow-400">
                  <li>• This location shows high revenue potential with consistent bookings</li>
                  <li>• Real estate projects dominate, consider expanding commercial offerings</li>
                  <li>• Peak season is Oct-Jan, plan capacity accordingly</li>
                  <li>• Flight efficiency is good at {(locationData.flightHours / locationData.totalShoots).toFixed(1)}h per shoot</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-border bg-muted">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Data updated: {new Date().toLocaleDateString()}
            </div>
            <div className="flex space-x-3">
              <Button variant="outline">
                <Target className="w-4 h-4 mr-2" />
                Export Report
              </Button>
              <Button onClick={onClose}>
                Close
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
