'use client'

import { useState, useRef, useEffect, useMemo } from 'react'
import { Bell, Check, X, Filter, Download, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useNotifications } from '@/hooks/useNotifications'
import type { NotificationFilters } from '@/types/notifications'
import { useAuth } from '@/contexts/AuthContext'
import { formatDistanceToNow } from 'date-fns'
import type { Notification } from '@/types/notifications'

export function NotificationBell() {
  const { stats, loading, error } = useNotifications({ read: false, limit: 5 })

  if (loading) {
    return (
      <div className="relative p-2 rounded-full hover:bg-muted transition-colors">
        <Bell className="w-5 h-5 text-muted-foreground" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="relative p-2 rounded-full hover:bg-muted transition-colors" title={error}>
        <Bell className="w-5 h-5 text-red-500" />
      </div>
    )
  }

  const unreadCount = stats?.unread || 0

  return (
    <div className="relative p-2 rounded-full hover:bg-muted transition-colors cursor-pointer">
      <Bell className="w-5 h-5 text-muted-foreground" />
      {unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
          {unreadCount > 99 ? '99+' : unreadCount}
        </span>
      )}
    </div>
  )
}
