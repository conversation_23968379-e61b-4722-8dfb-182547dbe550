'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Circle, 
  Play, 
  CheckCircle, 
  Clock,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useOptimizedTaskUpdate } from '@/hooks/useOptimizedTaskUpdate'
import type { Task } from '@/types'

interface OptimizedTaskCardProps {
  task: Task
  onTaskUpdate?: (taskId: string, updates: Partial<Task>) => void
  onTaskRevert?: (taskId: string) => void
  className?: string
  showProject?: boolean
  compact?: boolean
}

export function OptimizedTaskCard({
  task,
  onTaskUpdate,
  onTaskRevert,
  className,
  showProject = false,
  compact = false
}: OptimizedTaskCardProps) {
  const [isUpdating, setIsUpdating] = useState(false)
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(0)

  // Memoize status-based styling to prevent unnecessary re-renders
  const statusConfig = useMemo(() => {
    switch (task.status) {
      case 'completed':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50 border-green-200',
          badge: 'bg-green-100 text-green-800'
        }
      case 'in_progress':
        return {
          icon: Play,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 border-blue-200',
          badge: 'bg-blue-100 text-blue-800'
        }
      case 'pending':
      default:
        return {
          icon: Circle,
          color: 'text-gray-400',
          bgColor: 'bg-white border-gray-200',
          badge: 'bg-gray-100 text-gray-800'
        }
    }
  }, [task.status])

  const { updateTaskStatus, isPending } = useOptimizedTaskUpdate({
    onOptimisticUpdate: useCallback((taskId: string, updates: Partial<Task>) => {
      setLastUpdateTime(Date.now())
      onTaskUpdate?.(taskId, updates)
    }, [onTaskUpdate]),
    
    onRevert: useCallback((taskId: string) => {
      onTaskRevert?.(taskId)
    }, [onTaskRevert]),
    
    onSuccess: useCallback((updatedTask: Task) => {
      setIsUpdating(false)
      console.log('✅ Task update confirmed:', updatedTask.id)
    }, []),
    
    onError: useCallback((error: Error, taskId: string) => {
      setIsUpdating(false)
      console.error('❌ Task update failed:', error)
    }, [])
  })

  // Optimized status change handler with instant feedback
  const handleStatusChange = useCallback(async (newStatus: Task['status']) => {
    // Prevent rapid double-clicks
    const now = Date.now()
    if (now - lastUpdateTime < 100) {
      console.log('🚫 Ignoring rapid click')
      return
    }

    setIsUpdating(true)
    setLastUpdateTime(now)

    try {
      await updateTaskStatus(task, newStatus)
    } catch (error) {
      setIsUpdating(false)
      // Error handling is done in the hook
    }
  }, [task, updateTaskStatus, lastUpdateTime])

  // Memoize button configurations to prevent re-renders
  const actionButtons = useMemo(() => {
    const buttons = []
    const isProcessing = isUpdating || isPending(task.id)

    if (task.status === 'pending') {
      buttons.push({
        key: 'start',
        label: 'Start',
        icon: Play,
        onClick: () => handleStatusChange('in_progress'),
        variant: 'outline' as const,
        className: 'text-blue-600 border-blue-200 hover:bg-blue-50'
      })
    }

    if (task.status === 'in_progress') {
      buttons.push({
        key: 'complete',
        label: 'Complete',
        icon: CheckCircle,
        onClick: () => handleStatusChange('completed'),
        variant: 'outline' as const,
        className: 'text-green-600 border-green-200 hover:bg-green-50'
      })
    }

    if (task.status === 'completed') {
      buttons.push({
        key: 'reopen',
        label: 'Reopen',
        icon: Circle,
        onClick: () => handleStatusChange('pending'),
        variant: 'ghost' as const,
        className: 'text-gray-600 hover:bg-gray-50'
      })
    }

    return buttons.map(button => ({
      ...button,
      disabled: isProcessing
    }))
  }, [task.status, task.id, isUpdating, isPending, handleStatusChange])

  const StatusIcon = statusConfig.icon

  return (
    <div className={cn(
      'rounded-lg border transition-all duration-200 hover:shadow-md',
      statusConfig.bgColor,
      compact ? 'p-3' : 'p-4',
      className
    )}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1 min-w-0">
          {/* Status Icon with Loading State */}
          <div className="flex-shrink-0 mt-0.5">
            {isUpdating || isPending(task.id) ? (
              <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
            ) : (
              <StatusIcon className={cn('w-5 h-5', statusConfig.color)} />
            )}
          </div>

          {/* Task Content */}
          <div className="flex-1 min-w-0">
            <h3 className={cn(
              'font-medium text-gray-900 truncate',
              compact ? 'text-sm' : 'text-base',
              task.status === 'completed' && 'line-through text-gray-500'
            )}>
              {task.title}
            </h3>

            {!compact && task.description && (
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {task.description}
              </p>
            )}

            {/* Metadata */}
            <div className="flex items-center space-x-4 mt-2">
              <Badge variant="secondary" className={cn('text-xs', statusConfig.badge)}>
                {task.status.replace('_', ' ')}
              </Badge>

              {task.priority && task.priority !== 'medium' && (
                <Badge 
                  variant={task.priority === 'high' ? 'destructive' : 'outline'}
                  className="text-xs"
                >
                  {task.priority}
                </Badge>
              )}

              {task.due_date && (
                <div className="flex items-center text-xs text-gray-500">
                  <Clock className="w-3 h-3 mr-1" />
                  {new Date(task.due_date).toLocaleDateString()}
                </div>
              )}

              {showProject && task.project && (
                <span className="text-xs text-gray-500 truncate">
                  {task.project.name}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2 ml-4">
          {actionButtons.map(button => {
            const Icon = button.icon
            return (
              <Button
                key={button.key}
                size={compact ? 'sm' : 'default'}
                variant={button.variant}
                onClick={button.onClick}
                disabled={button.disabled}
                className={cn(
                  'transition-all duration-150',
                  button.className,
                  button.disabled && 'opacity-50 cursor-not-allowed'
                )}
              >
                <Icon className={cn(
                  compact ? 'w-3 h-3' : 'w-4 h-4',
                  compact ? 'mr-1' : 'mr-2'
                )} />
                {button.label}
              </Button>
            )
          })}
        </div>
      </div>

      {/* Performance Debug Info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-2 text-xs text-gray-400 font-mono">
          Last update: {lastUpdateTime ? new Date(lastUpdateTime).toLocaleTimeString() : 'Never'}
          {isPending(task.id) && ' • Updating...'}
        </div>
      )}
    </div>
  )
}
