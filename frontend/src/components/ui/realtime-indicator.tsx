'use client'

import { useState, useEffect } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import { Wifi, WifiOff, Activity, Zap } from 'lucide-react'
import { cn } from '@/lib/utils'

interface RealtimeIndicatorProps {
  className?: string
  showText?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function RealtimeIndicator({ 
  className, 
  showText = false, 
  size = 'sm' 
}: RealtimeIndicatorProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [lastActivity, setLastActivity] = useState<Date | null>(null)
  const supabase = createClientSupabaseClient()

  useEffect(() => {
    // Check initial online status
    setIsOnline(navigator.onLine)

    // Listen for online/offline events
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Set up Supabase connection monitoring
    const channel = supabase
      .channel('connection-monitor')
      .on('presence', { event: 'sync' }, () => {
        setIsConnected(true)
        setLastActivity(new Date())
      })
      .on('presence', { event: 'join' }, () => {
        setIsConnected(true)
        setLastActivity(new Date())
      })
      .on('presence', { event: 'leave' }, () => {
        setIsConnected(false)
      })
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
          setLastActivity(new Date())
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          setIsConnected(false)
        }
      })

    // Monitor real-time activity with a test channel
    const activityChannel = supabase
      .channel('activity-monitor')
      .on('broadcast', { event: 'ping' }, () => {
        setLastActivity(new Date())
      })
      .subscribe()

    // Send periodic pings to test connectivity
    const pingInterval = setInterval(() => {
      if (isOnline) {
        activityChannel.send({
          type: 'broadcast',
          event: 'ping',
          payload: { timestamp: new Date().toISOString() }
        })
      }
    }, 30000) // Ping every 30 seconds

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      clearInterval(pingInterval)
      supabase.removeChannel(channel)
      supabase.removeChannel(activityChannel)
    }
  }, [supabase, isOnline])

  const getStatusColor = () => {
    if (!isOnline) return 'text-red-500'
    if (!isConnected) return 'text-yellow-500'
    return 'text-green-500'
  }

  const getStatusText = () => {
    if (!isOnline) return 'Offline'
    if (!isConnected) return 'Connecting...'
    return 'Real-time'
  }

  const getIcon = () => {
    if (!isOnline) return WifiOff
    if (!isConnected) return Wifi
    return Activity
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-3 h-3'
      case 'md':
        return 'w-4 h-4'
      case 'lg':
        return 'w-5 h-5'
      default:
        return 'w-3 h-3'
    }
  }

  const Icon = getIcon()

  return (
    <div className={cn(
      'flex items-center space-x-1',
      className
    )}>
      <div className="relative">
        <Icon className={cn(
          getSizeClasses(),
          getStatusColor(),
          isConnected && isOnline && 'animate-pulse'
        )} />
        
        {/* Connection pulse animation */}
        {isConnected && isOnline && (
          <div className={cn(
            'absolute inset-0 rounded-full animate-ping',
            getSizeClasses(),
            'bg-green-400 opacity-20'
          )} />
        )}
      </div>
      
      {showText && (
        <span className={cn(
          'text-xs font-medium',
          getStatusColor()
        )}>
          {getStatusText()}
        </span>
      )}
      
      {/* Last activity indicator */}
      {lastActivity && isConnected && isOnline && (
        <div className="flex items-center space-x-1">
          <Zap className="w-2 h-2 text-green-400" />
          <span className="text-xs text-gray-500">
            {lastActivity.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </span>
        </div>
      )}
    </div>
  )
}

// Hook to get real-time connection status
export function useRealtimeStatus() {
  const [isConnected, setIsConnected] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [lastActivity, setLastActivity] = useState<Date | null>(null)
  const supabase = createClientSupabaseClient()

  useEffect(() => {
    setIsOnline(navigator.onLine)

    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    const channel = supabase
      .channel('status-monitor')
      .on('presence', { event: 'sync' }, () => {
        setIsConnected(true)
        setLastActivity(new Date())
      })
      .subscribe((status) => {
        setIsConnected(status === 'SUBSCRIBED')
        if (status === 'SUBSCRIBED') {
          setLastActivity(new Date())
        }
      })

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      supabase.removeChannel(channel)
    }
  }, [supabase])

  return {
    isConnected,
    isOnline,
    lastActivity,
    status: !isOnline ? 'offline' : !isConnected ? 'connecting' : 'connected'
  }
}
