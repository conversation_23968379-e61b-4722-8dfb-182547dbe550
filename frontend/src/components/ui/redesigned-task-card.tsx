'use client'

import { useState, useEffect, memo, useMemo, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import {
  Circle,
  Play,
  CheckCircle,
  XCircle,
  Minus,
  Edit,
  Trash2,
  Save,
  X,
  Calendar as CalendarIcon,
  User,
  MessageSquare,
  ExternalLink,
  Target,
  Clock,
  AlertTriangle,
  ChevronDown
} from 'lucide-react'
import type { Task, User as UserType } from '@/types'
import { useAuth } from '@/contexts/AuthContext'
import { formatDate } from '@/lib/utils'

interface RedesignedTaskCardProps {
  task: Task
  users?: UserType[]
  onEdit?: (task: Task) => void
  onDelete?: (task: Task) => void
  onStatusChange?: (task: Task, newStatus: Task['status']) => void
  onAssignmentChange?: (taskId: string, userId: string) => void
  onRoleChange?: (taskId: string, role: string) => void
  onDueDateChange?: (taskId: string, dueDate: string | null) => void
  onInlineUpdate?: (taskId: string, updates: Partial<Task>) => void
  onCompleteSchedule?: (schedule: any) => void
  onCompleteTask?: (task: Task) => void
  schedule?: any
  compact?: boolean
  showRoleFilter?: boolean
  currentUserRole?: string
}

export const RedesignedTaskCard = memo(function RedesignedTaskCard({
  task,
  users = [],
  onEdit,
  onDelete,
  onStatusChange,
  onAssignmentChange,
  onRoleChange,
  onDueDateChange,
  onInlineUpdate,
  onCompleteSchedule,
  onCompleteTask,
  schedule,
  compact = false,
  showRoleFilter = true,
  currentUserRole
}: RedesignedTaskCardProps) {
  const { user } = useAuth()
  const router = useRouter()
  const [isEditing, setIsEditing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [editedTitle, setEditedTitle] = useState(task.title)
  const [editedPriority, setEditedPriority] = useState(task.priority)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    task.due_date ? new Date(task.due_date) : undefined
  )

  const isAdminOrManager = useMemo(() =>
    user?.role === 'admin' || user?.role === 'manager',
    [user?.role]
  )
  const canEdit = useMemo(() =>
    isAdminOrManager || task.assigned_to === user?.id,
    [isAdminOrManager, task.assigned_to, user?.id]
  )

  const getStatusIcon = useCallback((status: string) => {
    switch (status) {
      case 'pending':
        return <Circle className="w-4 h-4" />
      case 'in_progress':
        return <Play className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      case 'skipped':
        return <Minus className="w-4 h-4" />
      default:
        return <Circle className="w-4 h-4" />
    }
  }, [])

  const getStatusColor = useCallback((status: string) => {
    switch (status) {
      case 'pending':
        return 'border-gray-300 bg-gray-50 text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300'
      case 'in_progress':
        return 'border-blue-300 bg-blue-50 text-blue-700 dark:border-blue-600 dark:bg-blue-900/20 dark:text-blue-300'
      case 'completed':
        return 'border-green-300 bg-green-50 text-green-700 dark:border-green-600 dark:bg-green-900/20 dark:text-green-300'
      case 'cancelled':
        return 'border-red-300 bg-red-50 text-red-700 dark:border-red-600 dark:bg-red-900/20 dark:text-red-300'
      case 'skipped':
        return 'border-yellow-300 bg-yellow-50 text-yellow-700 dark:border-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-300'
      default:
        return 'border-gray-300 bg-gray-50 text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300'
    }
  }, [])

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-red-500 bg-red-100 text-red-800 dark:border-red-400 dark:bg-red-900/20 dark:text-red-300'
      case 'high':
        return 'border-orange-500 bg-orange-100 text-orange-800 dark:border-orange-400 dark:bg-orange-900/20 dark:text-orange-300'
      case 'medium':
        return 'border-blue-500 bg-blue-100 text-blue-800 dark:border-blue-400 dark:bg-blue-900/20 dark:text-blue-300'
      case 'low':
        return 'border-gray-500 bg-gray-100 text-gray-800 dark:border-gray-400 dark:bg-gray-900/20 dark:text-gray-300'
      default:
        return 'border-gray-500 bg-gray-100 text-gray-800 dark:border-gray-400 dark:bg-gray-900/20 dark:text-gray-300'
    }
  }

  const handleStatusChange = async (newStatus: Task['status']) => {
    // If trying to complete the task, only trigger completion form for "Shoot" tasks
    if (newStatus === 'completed' && onCompleteTask && task.title === 'Shoot') {
      onCompleteTask(task)
      return
    }

    // Set loading state briefly for user feedback, but don't wait for API
    setIsUpdatingStatus(true)

    // Call the status change handler (which should do optimistic updates)
    onStatusChange?.(task, newStatus)

    // Reset loading state quickly for better UX
    setTimeout(() => {
      setIsUpdatingStatus(false)
    }, 500)
  }

  const handleAssignmentChange = async (userId: string) => {
    try {
      // Convert 'unassigned' back to empty string for the API
      const actualUserId = userId === 'unassigned' ? '' : userId
      await onAssignmentChange?.(task.id, actualUserId)
    } catch (error) {
      console.error('Failed to update assignment:', error)
    }
  }

  const handleRoleChange = async (role: string) => {
    try {
      // Convert 'none' to empty string for the API
      const actualRole = role === 'none' ? '' : role
      await onRoleChange?.(task.id, actualRole)
    } catch (error) {
      console.error('Failed to update role:', error)
    }
  }

  const handleDueDateChange = async (date: Date | undefined) => {
    try {
      const dateString = date ? format(date, 'yyyy-MM-dd') : null
      setSelectedDate(date)
      await onDueDateChange?.(task.id, dateString)
    } catch (error) {
      console.error('Failed to update due date:', error)
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete the task "${task.title}"?`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(task)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleInlineUpdate = async () => {
    if (!onInlineUpdate) return

    try {
      await onInlineUpdate(task.id, {
        title: editedTitle,
        priority: editedPriority
      })
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update task:', error)
    }
  }

  const cancelEdit = () => {
    setEditedTitle(task.title)
    setEditedPriority(task.priority)
    setIsEditing(false)
  }

  const handleNavigateToShoot = () => {
    if (task.shoot_id) {
      router.push(`/shoots/${task.shoot_id}`)
    }
  }

  const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed'
  const assignedUser = users.find(u => u.id === task.assigned_to)

  return (
    <div className={cn(
      "app-card p-4 hover:shadow-lg transition-all duration-200",
      isOverdue && "border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/10",
      task.status === 'completed' && "opacity-75"
    )}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className={cn("flex items-center px-3 py-1 rounded-full text-xs font-medium border", getStatusColor(task.status))}>
            {getStatusIcon(task.status)}
            <span className="ml-1 capitalize">{task.status.replace('_', ' ')}</span>
          </div>
          <div className={cn("px-3 py-1 rounded-full text-xs font-medium border", getPriorityColor(task.priority))}>
            {task.priority}
          </div>
          {task.assigned_role && (
            <div className="flex items-center text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
              <User className="w-3 h-3 mr-1" />
              {task.assigned_role}
            </div>
          )}
          {isOverdue && (
            <div className="flex items-center px-3 py-1 rounded-full text-xs font-medium border border-red-500 bg-red-100 text-red-800 dark:border-red-400 dark:bg-red-900/20 dark:text-red-300">
              <AlertTriangle className="w-3 h-3 mr-1" />
              Overdue
            </div>
          )}
        </div>
        <div className="flex items-center space-x-1">

          {task.status !== 'completed' && task.status !== 'cancelled' && (
            <>
              {/* Special handling for Shoot tasks */}
              {task.title.toLowerCase() === 'shoot' ? (
                <>
                  {task.status === 'pending' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleStatusChange('in_progress')}
                      disabled={isUpdatingStatus}
                      className="h-8 w-8 p-0 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                      title="Start Shoot"
                    >
                      <Play className="w-4 h-4 text-blue-600" />
                    </Button>
                  )}
                  {task.status === 'in_progress' && schedule && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onCompleteSchedule?.(schedule)}
                      disabled={isUpdatingStatus}
                      className="h-8 w-8 p-0 hover:bg-green-50 dark:hover:bg-green-900/20"
                      title="Complete Shoot"
                    >
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    </Button>
                  )}
                </>
              ) : (
                /* Regular task buttons */
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleStatusChange(task.status === 'pending' ? 'in_progress' : 'completed')}
                  disabled={isUpdatingStatus}
                  className="h-8 w-8 p-0 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  title={task.status === 'pending' ? 'Start Task' : 'Complete Task'}
                >
                  {task.status === 'pending' ? (
                    <Play className="w-4 h-4 text-blue-600" />
                  ) : (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  )}
                </Button>
              )}
              {/* Skip Task Button - Available for all tasks */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleStatusChange('skipped')}
                disabled={isUpdatingStatus}
                className="h-8 w-8 p-0 hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                title="Skip Task"
              >
                <Minus className="w-4 h-4 text-yellow-600" />
              </Button>
            </>
          )}

          {/* Complete Schedule Button - Only show when Shoot task is completed */}
          {schedule && schedule.status !== 'completed' && onCompleteSchedule &&
           task.title.toLowerCase() === 'shoot' && task.status === 'completed' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onCompleteSchedule(schedule)}
              className="h-8 w-8 p-0 hover:bg-green-50 dark:hover:bg-green-900/20"
              title="Complete Schedule"
            >
              <Target className="w-4 h-4 text-green-600" />
            </Button>
          )}

          {canEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
              className="h-8 w-8 p-0 hover:bg-gray-50 dark:hover:bg-gray-700"
              title="Edit Task"
            >
              <Edit className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            </Button>
          )}
          {onDelete && isAdminOrManager && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              disabled={isDeleting}
              className="h-8 w-8 p-0 hover:bg-red-50 dark:hover:bg-red-900/20"
              title="Delete Task"
            >
              <Trash2 className="w-4 h-4 text-red-600 dark:text-red-400" />
            </Button>
          )}
        </div>
      </div>

      {/* Title and Description */}
      <div className="mb-3">
        {isEditing ? (
          <div className="space-y-2">
            <Input
              value={editedTitle}
              onChange={(e) => setEditedTitle(e.target.value)}
              className="font-medium"
              placeholder="Task title"
            />

            <select
              value={editedPriority}
              onChange={(e) => setEditedPriority(e.target.value as Task['priority'])}
              className="w-full px-3 py-2 text-sm border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="low">Low Priority</option>
              <option value="medium">Medium Priority</option>
              <option value="high">High Priority</option>
              <option value="urgent">Urgent Priority</option>
            </select>
          </div>
        ) : (
          <div>
            <h3 className="font-medium text-foreground mb-1">{task.title}</h3>
          </div>
        )}
      </div>

      {/* Role, Assignment and Due Date - Aligned in a single row */}
      <div className="flex items-center space-x-3 mb-4">
        {/* Role Dropdown */}
        <div className="flex items-center space-x-2 min-w-0">
          <Label className="text-xs text-muted-foreground whitespace-nowrap">Role:</Label>
          <Select
            value={task.assigned_role || 'none'}
            onValueChange={handleRoleChange}
            disabled={!canEdit || isUpdatingStatus}
          >
            <SelectTrigger className="h-7 text-xs w-24">
              <SelectValue placeholder="None">
                {task.assigned_role || 'None'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">None</SelectItem>
              <SelectItem value="pilot">Pilot</SelectItem>
              <SelectItem value="editor">Editor</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Assignment Dropdown - Smaller */}
        <div className="flex items-center space-x-2 min-w-0 flex-1">
          <Label className="text-xs text-muted-foreground whitespace-nowrap">Assigned:</Label>
          <Select
            value={task.assigned_to || 'unassigned'}
            onValueChange={handleAssignmentChange}
            disabled={!canEdit || isUpdatingStatus}
          >
            <SelectTrigger className="h-7 text-xs flex-1 min-w-0">
              <SelectValue placeholder="Unassigned">
                {assignedUser ? (
                  <div className="flex items-center truncate">
                    <User className="w-3 h-3 mr-1 flex-shrink-0" />
                    <span className="truncate">{assignedUser.name || assignedUser.email}</span>
                  </div>
                ) : (
                  <span className="text-muted-foreground">Unassigned</span>
                )}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="unassigned">
                <span className="text-muted-foreground">Unassigned</span>
              </SelectItem>
              {users.map((user) => (
                <SelectItem key={user.id} value={user.id}>
                  <div className="flex items-center">
                    <User className="w-3 h-3 mr-2" />
                    <span>{user.name || user.email}</span>
                    {user.role && (
                      <span className="ml-2 text-xs text-muted-foreground">({user.role})</span>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Due Date Picker - Smaller */}
        <div className="flex items-center space-x-2 min-w-0 flex-1">
          <Label className="text-xs text-muted-foreground whitespace-nowrap">Due:</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "h-7 text-xs justify-start text-left font-normal flex-1 min-w-0",
                  !selectedDate && "text-muted-foreground",
                  isOverdue && "border-red-300 text-red-600"
                )}
                disabled={!canEdit || isUpdatingStatus}
              >
                <CalendarIcon className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="truncate">
                  {selectedDate ? format(selectedDate, "MMM dd") : "No date"}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDueDateChange}
                initialFocus
              />
              {selectedDate && (
                <div className="p-3 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDueDateChange(undefined)}
                    className="w-full"
                  >
                    Clear due date
                  </Button>
                </div>
              )}
            </PopoverContent>
          </Popover>
        </div>

        {/* Time Tracking */}
        {(task.started_at || task.completed_at) && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            {task.started_at && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Play className="w-4 h-4 mr-2 text-blue-600" />
                <span className="font-medium">Started:</span>
                <span className="ml-2">{formatDate(task.started_at)}</span>
              </div>
            )}
            {task.completed_at && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                <span className="font-medium">Completed:</span>
                <span className="ml-2">{formatDate(task.completed_at)}</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Edit Actions - Only show when editing */}
      {isEditing && (
        <div className="flex items-center justify-end space-x-2 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            size="sm"
            onClick={handleInlineUpdate}
            className="h-8 px-3 text-sm"
          >
            <Save className="w-4 h-4 mr-1" />
            Save
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={cancelEdit}
            className="h-8 px-3 text-sm"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  )
})
