'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { formatDate, formatDateTime } from '@/lib/utils'
import { tasksApi, projectsApi } from '@/lib/api'
import {
  Calendar,
  Clock,
  User,
  MapPin,
  Edit,
  CheckCircle,
  AlertCircle,
  XCircle,
  Building,
  FileText,
  MoreVertical,
  X,
  RotateCcw,
  Trash2,
  ExternalLink,
  BarChart3
} from 'lucide-react'
import type { Schedule, Task } from '@/types'
import { getGSTBreakdownForDisplay } from '@/lib/schedule-gst-calculations'

interface ScheduleCardProps {
  schedule: Schedule
  onEdit?: (schedule: Schedule) => void
  onStatusChange?: (schedule: Schedule, status: string) => void
  onCancel?: (schedule: Schedule) => void
  onDelete?: (schedule: Schedule) => void
  compact?: boolean
}

export function ScheduleCard({ schedule, onEdit, onStatusChange, onCancel, onDelete, compact = false }: ScheduleCardProps) {
  const router = useRouter()
  const [isDeleting, setIsDeleting] = useState(false)
  const [tasks, setTasks] = useState<Task[]>([])
  const [tasksLoading, setTasksLoading] = useState(false)

  // Fetch tasks for this schedule
  useEffect(() => {
    console.log('ScheduleCard: useEffect triggered for schedule', schedule.id)
    const fetchTasks = async () => {
      try {
        setTasksLoading(true)
        console.log('ScheduleCard: Fetching all tasks, tasksApi:', tasksApi)
        const allTasks = await tasksApi.getAll()
        console.log('ScheduleCard: All tasks fetched', allTasks?.length || 0, 'tasks')
        // Include both schedule-specific tasks and project-level tasks (default tasks)
        const scheduleTasks = allTasks.filter(task => {
          const isScheduleTask = task.shoot_id === schedule.id
          const isProjectTask = task.project_id === schedule.project_id && !task.shoot_id
          console.log('ScheduleCard: Checking task', task.id, 'isScheduleTask:', isScheduleTask, 'isProjectTask:', isProjectTask)
          return isScheduleTask || isProjectTask
        })
        console.log('ScheduleCard: Fetched tasks for schedule', schedule.id, scheduleTasks)
        setTasks(scheduleTasks)
      } catch (error) {
        console.error('Error fetching tasks:', error)
      } finally {
        setTasksLoading(false)
      }
    }

    try {
      fetchTasks()
    } catch (error) {
      console.error('ScheduleCard: Error in useEffect:', error)
    }
  }, [schedule.id, schedule.project_id])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-700'
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-700'
      case 'cancelled':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-700'
      case 'rescheduled':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-700'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-700'
    }
  }

  const getTaskProgress = () => {
    if (tasks.length === 0) {
      console.log('ScheduleCard: No tasks found for schedule', schedule.id)
      return { completed: 0, total: 0, percentage: 0 }
    }
    
    const completed = tasks.filter(task => task.status === 'completed').length
    const total = tasks.length
    const percentage = Math.round((completed / total) * 100)
    console.log('ScheduleCard: Task progress for schedule', schedule.id, { completed, total, percentage })
    
    return { completed, total, percentage }
  }

  const taskProgress = getTaskProgress()
  console.log('ScheduleCard: taskProgress for schedule', schedule.id, taskProgress)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      case 'rescheduled':
        return <RotateCcw className="w-4 h-4" />
      default:
        return <AlertCircle className="w-4 h-4" />
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete this schedule? This action cannot be undone.`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(schedule)
    } finally {
      setIsDeleting(false)
    }
  }

  const isUpcoming = new Date(schedule.scheduled_date) > new Date()
  const isPast = new Date(schedule.scheduled_date) < new Date()

  if (compact) {
    return (
      <div className="app-card p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(schedule.status)}`}>
                {getStatusIcon(schedule.status)}
                <span className="ml-1 capitalize">{schedule.status}</span>
              </div>
              <div className="min-w-0 flex-1">
                <h4
                className="font-medium text-card-foreground truncate cursor-pointer hover:text-primary transition-colors"
                onClick={() => router.push(`/schedules/${schedule.id}`)}
              >
                {schedule.project?.name}
              </h4>
                <div className="flex items-center gap-2">
                  <p className="text-sm text-muted-foreground truncate">{schedule.project?.client?.name}</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1 flex-shrink-0" />
                <span className="truncate">{formatDateTime(schedule.scheduled_date)}</span>
              </div>
              {schedule.project?.location && (
                <div className="flex items-center min-w-0">
                  <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
                  <span className="truncate">{schedule.project.location}</span>
                </div>
              )}
            </div>

            {/* Task Progress Bar */}
            {!tasksLoading && (
              <div className="mt-3">
                <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                  <div className="flex items-center">
                    <BarChart3 className="w-3 h-3 mr-1" />
                    <span>Tasks</span>
                  </div>
                  <span>{taskProgress.completed}/{taskProgress.total}</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
                  <div
                    className="h-2 bg-primary rounded-full transition-all duration-300"
                    style={{ width: `${taskProgress.total > 0 ? taskProgress.percentage : 0}%` }}
                  />
                </div>
                {taskProgress.total === 0 && (
                  <div className="text-xs text-muted-foreground mt-1">
                    No tasks created yet
                  </div>
                )}
              </div>
            )}
            {tasksLoading && (
              <div className="mt-3">
                <div className="text-xs text-muted-foreground">Loading tasks...</div>
              </div>
            )}
          </div>

          {/* Amount */}
          {schedule.amount && schedule.amount > 0 && (
            <div className="text-right ml-4">
              {schedule.has_gst ? (
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">
                    Base: ₹{schedule.amount.toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    GST: ₹{(schedule.gst_amount || 0).toLocaleString()}
                  </div>
                  <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                    ₹{(schedule.total_amount || schedule.amount).toLocaleString()}
                  </div>
                </div>
              ) : (
                <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                  ₹{schedule.amount.toLocaleString()}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between mt-4 pt-3 border-t border-border">
          <div className="flex items-center space-x-2">
            {isUpcoming && (
              <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
                Upcoming
              </div>
            )}
            {isPast && schedule.status === 'scheduled' && (
              <div className="text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 px-2 py-1 rounded">
                Overdue
              </div>
            )}
          </div>

          {/* Desktop actions */}
          <div className="hidden sm:flex items-center space-x-2">
            {schedule.status === 'scheduled' && (
              <Button
                variant="outline"
                size="sm"
                disabled
                className="text-xs text-gray-400"
              >
                <CheckCircle className="w-3 h-3 mr-1" />
                Complete via Tasks
              </Button>
            )}
            {onCancel && schedule.status === 'scheduled' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCancel(schedule)}
                className="text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-950"
                title="Cancel schedule"
              >
                <X className="w-3 h-3 mr-1" />
                Cancel
              </Button>
            )}
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(schedule)}
                className="text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-950"
                title="Edit schedule"
              >
                <Edit className="w-3 h-3 mr-1" />
                Edit
              </Button>
            )}
          </div>

          {/* Mobile dropdown menu */}
          <div className="sm:hidden">
            <DropdownMenu
              trigger={
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0"
                >
                  <MoreVertical className="w-4 h-4" />
                </Button>
              }
            >
              {schedule.status === 'scheduled' && (
                <DropdownMenuItem disabled>
                  <CheckCircle className="w-4 h-4 mr-2 text-gray-400" />
                  Complete via Tasks
                </DropdownMenuItem>
              )}
              {onCancel && schedule.status === 'scheduled' && (
                <DropdownMenuItem
                  onClick={() => onCancel(schedule)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel Schedule
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem
                  onClick={() => onEdit(schedule)}
                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Schedule
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </DropdownMenuItem>
              )}
            </DropdownMenu>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="app-card overflow-hidden will-change-transform">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3
                className="text-lg font-semibold text-card-foreground cursor-pointer hover:text-primary transition-colors truncate"
                onClick={() => router.push(`/schedules/${schedule.id}`)}
              >
                {schedule.custom_id || schedule.id}
              </h3>
            </div>
            <div className="flex items-center text-sm text-muted-foreground mb-2 gap-2">
              <div className="flex items-center">
                <Building className="w-4 h-4 mr-1" />
                <span className="truncate">{schedule.project?.name || 'Project'}</span>
              </div>
              {schedule.project?.client?.name && (
                <span className="text-muted-foreground/80">• {schedule.project.client.name}</span>
              )}
            </div>
          </div>
          <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(schedule.status)}`}>
            {getStatusIcon(schedule.status)}
            <span className="ml-1 capitalize">{schedule.status}</span>
          </div>
        </div>

        {/* Date and Time */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="w-4 h-4 mr-2" />
            <span className="font-medium">Scheduled:</span>
            <span className="ml-2">{formatDateTime(schedule.scheduled_date)}</span>
          </div>

          {schedule.actual_date && (
            <div className="flex items-center text-sm text-muted-foreground">
              <CheckCircle className="w-4 h-4 mr-2" />
              <span className="font-medium">Completed:</span>
              <span className="ml-2">{formatDateTime(schedule.actual_date)}</span>
            </div>
          )}

          {isUpcoming && (
            <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
              Upcoming schedule
            </div>
          )}

          {isPast && schedule.status === 'scheduled' && (
            <div className="text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 px-2 py-1 rounded">
              Overdue schedule
            </div>
          )}
        </div>

        {/* Task Progress Bar */}
        {!tasksLoading && (
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
              <div className="flex items-center">
                <BarChart3 className="w-4 h-4 mr-2" />
                <span>Task Progress</span>
              </div>
              <span>{taskProgress.completed}/{taskProgress.total} completed</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <div
                className="h-2 bg-primary rounded-full transition-all duration-300"
                style={{ width: `${taskProgress.total > 0 ? taskProgress.percentage : 0}%` }}
              />
            </div>
            {taskProgress.total > 0 ? (
              <div className="text-xs text-muted-foreground mt-1">
                {taskProgress.percentage}% complete
              </div>
            ) : (
              <div className="text-xs text-muted-foreground mt-1">
                No tasks created yet
              </div>
            )}
          </div>
        )}
        {tasksLoading && (
          <div className="mt-4">
            <div className="text-sm text-muted-foreground">Loading tasks...</div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="px-6 py-4 bg-muted/30 border-t border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {schedule.amount && schedule.amount > 0 && (
              <div>
                {schedule.has_gst ? (
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">
                      Base: ₹{schedule.amount.toLocaleString()} + GST: ₹{(schedule.gst_amount || 0).toLocaleString()}
                    </div>
                    <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                      Total: ₹{(schedule.total_amount || schedule.amount).toLocaleString()}
                    </div>
                  </div>
                ) : (
                  <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                    ₹{schedule.amount.toLocaleString()}
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(schedule)}
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            )}
            {onCancel && schedule.status === 'scheduled' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCancel(schedule)}
                className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
              >
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Keep ShootCard as alias for backward compatibility
export const ShootCard = ScheduleCard
