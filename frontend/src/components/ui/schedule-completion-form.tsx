'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Modal } from '@/components/ui/modal'
import { CheckCircle, Clock, Battery, Camera, Receipt, Plus, X } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useCreateExpense } from '@/hooks/useApi'
import toast from 'react-hot-toast'
import type { Schedule, ExpenseCategory, Task } from '@/types'

interface ScheduleCompletionFormProps {
  schedule: Schedule | null
  shootTask?: Task | null
  isOpen: boolean
  onClose: () => void
  onComplete: (completionData: ScheduleCompletionData) => void
}

export interface ScheduleCompletionData {
  device_used?: string
  battery_count?: number
  shoot_start_time: string
  shoot_end_time: string
  completion_notes?: string
  expenses?: ExpenseData[]
}

export interface ExpenseData {
  description: string
  amount: number
  category: ExpenseCategory
  receipt_url?: string
}

interface ExpenseFormData {
  description: string
  amount: string
  category: ExpenseCategory
  receipt_url?: string
}

export function ScheduleCompletionForm({ schedule, shootTask, isOpen, onClose, onComplete }: ScheduleCompletionFormProps) {
  const { user } = useAuth()
  const { createExpense } = useCreateExpense()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showExpenseForm, setShowExpenseForm] = useState(false)
  
  // Helper function to format timestamp to time string
  const formatTimeFromTimestamp = (timestamp?: string) => {
    if (!timestamp) return ''
    try {
      const date = new Date(timestamp)
      if (isNaN(date.getTime())) return ''
      return date.toTimeString().slice(0, 5) // HH:MM format
    } catch (error) {
      console.error('Error formatting timestamp:', error)
      return ''
    }
  }

  const [formData, setFormData] = useState<ScheduleCompletionData>({
    device_used: '',
    battery_count: undefined,
    shoot_start_time: '',
    shoot_end_time: '',
    completion_notes: '',
    expenses: []
  })

  useEffect(() => {
    if (isOpen && schedule) {
      setFormData({
        device_used: schedule.device_used || '',
        battery_count: schedule.battery_count || undefined,
        shoot_start_time: formatTimeFromTimestamp(shootTask?.started_at),
        shoot_end_time: formatTimeFromTimestamp(shootTask?.completed_at || schedule.shoot_end_time) || new Date().toTimeString().slice(0, 5),
        completion_notes: schedule.completion_notes || '',
        expenses: []
      })
    }
  }, [isOpen, schedule, shootTask])

  const [expenseForm, setExpenseForm] = useState<ExpenseFormData>({
    description: '',
    amount: '',
    category: 'travel',
    receipt_url: ''
  })

  const handleInputChange = (field: keyof ScheduleCompletionData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) {
      toast.error('You must be logged in to complete a schedule')
      return
    }

    setIsSubmitting(true)
    try {
      // Complete the schedule first
      await onComplete(formData)

      // Create expenses if any
      if (formData.expenses && formData.expenses.length > 0) {
        for (const expense of formData.expenses) {
          await createExpense({
            description: expense.description,
            amount: expense.amount,
            category: expense.category,
            date: schedule?.scheduled_date ? new Date(schedule.scheduled_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            project_id: schedule?.project_id || null,
            user_id: user.id,
            receipt_url: expense.receipt_url || null
          })
        }
        toast.success(`Schedule completed and ${formData.expenses.length} expense(s) recorded`)
      } else {
        toast.success('Schedule completed successfully')
      }

      onClose()
      // Reset form
      setFormData({
        device_used: '',
        battery_count: undefined,
        shoot_start_time: '',
        shoot_end_time: '',
        completion_notes: '',
        expenses: []
      })
      setShowExpenseForm(false)
    } catch (error) {
      console.error('Error completing schedule:', error)
      toast.error('Failed to complete schedule')
    } finally {
      setIsSubmitting(false)
    }
  }

  const addExpense = () => {
    if (!expenseForm.description || !expenseForm.amount) {
      toast.error('Please fill in expense description and amount')
      return
    }

    const newExpense = {
      description: expenseForm.description,
      amount: parseFloat(expenseForm.amount),
      category: expenseForm.category,
      receipt_url: expenseForm.receipt_url || undefined
    }

    setFormData(prev => ({
      ...prev,
      expenses: [...(prev.expenses || []), newExpense]
    }))

    // Reset expense form
    setExpenseForm({
      description: '',
      amount: '',
      category: 'travel',
      receipt_url: ''
    })

    toast.success('Expense added')
  }

  const removeExpense = (index: number) => {
    setFormData(prev => ({
      ...prev,
      expenses: prev.expenses?.filter((_, i) => i !== index) || []
    }))
    
    // If we removed the last expense, hide the form
    if (formData.expenses?.length === 1) {
      setShowExpenseForm(false)
    }
  }

  if (!schedule) return null

  if (!schedule) {
    return null
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Complete Schedule">
      <div className="space-y-6">
        {/* Schedule Info */}
        <div className="bg-muted p-4 rounded-lg">
          <h3 className="font-medium text-sm text-muted-foreground mb-2">Completing Schedule</h3>
          <p className="font-medium">{new Date(schedule.scheduled_date).toLocaleDateString()}</p>
          <p className="text-sm text-muted-foreground">{schedule.location}</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Device Used */}
          <div className="space-y-2">
            <Label htmlFor="device_used" className="flex items-center gap-2">
              <Camera className="w-4 h-4" />
              Device Used
            </Label>
            <Input
              id="device_used"
              value={formData.device_used || ''}
              onChange={(e) => handleInputChange('device_used', e.target.value)}
              placeholder="e.g., DJI Mavic 3, DJI Mini 4 Pro"
            />
          </div>

          {/* Battery Percentage */}
          <div className="space-y-2">
            <Label htmlFor="battery_count" className="flex items-center gap-2">
              <Battery className="w-4 h-4" />
              Batteries Used
            </Label>
            <Input
              id="battery_count"
              type="number"
              min="0"
              value={formData.battery_count || ''}
              onChange={(e) => handleInputChange('battery_count', e.target.value ? parseInt(e.target.value) : undefined)}
              placeholder="e.g., 4"
            />
          </div>


          {/* Timing */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="shoot_start_time" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Start Time *
              </Label>
              <Input
                id="shoot_start_time"
                type="time"
                value={formData.shoot_start_time || ''}
                onChange={(e) => handleInputChange('shoot_start_time', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="shoot_end_time" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                End Time *
              </Label>
              <Input
                id="shoot_end_time"
                type="time"
                value={formData.shoot_end_time || ''}
                onChange={(e) => handleInputChange('shoot_end_time', e.target.value)}
                required
              />
            </div>
          </div>

          {/* Expenses Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="flex items-center gap-2">
                <Receipt className="w-4 h-4" />
                Schedule Expenses
              </Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowExpenseForm(!showExpenseForm)}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Expense
              </Button>
            </div>

            {/* Existing Expenses */}
            {formData.expenses && formData.expenses.length > 0 && (
              <div className="space-y-2">
                {formData.expenses.map((expense, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium">{expense.description}</p>
                      <p className="text-sm text-muted-foreground">
                        ${expense.amount.toFixed(2)} • {expense.category}
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeExpense(index)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {/* Add Expense Form */}
            {showExpenseForm && (
              <div className="p-4 border rounded-lg space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="expense_description">Description</Label>
                    <Input
                      id="expense_description"
                      value={expenseForm.description}
                      onChange={(e) => setExpenseForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="e.g., Gas, Parking"
                    />
                  </div>
                  <div>
                    <Label htmlFor="expense_amount">Amount</Label>
                    <Input
                      id="expense_amount"
                      type="number"
                      step="0.01"
                      min="0"
                      value={expenseForm.amount}
                      onChange={(e) => setExpenseForm(prev => ({ ...prev, amount: e.target.value }))}
                      placeholder="0.00"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="expense_category">Category</Label>
                  <select
                    id="expense_category"
                    value={expenseForm.category}
                    onChange={(e) => setExpenseForm(prev => ({ ...prev, category: e.target.value as ExpenseCategory }))}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="travel">Travel</option>
                    <option value="equipment">Equipment</option>
                    <option value="outsourcing">Outsourcing</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div className="flex gap-2">
                  <Button type="button" onClick={addExpense} size="sm">
                    Add Expense
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setShowExpenseForm(false)} size="sm">
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Completion Notes */}
          <div className="space-y-2">
            <Label htmlFor="completion_notes">Additional Notes</Label>
            <Textarea
              id="completion_notes"
              value={formData.completion_notes}
              onChange={(e) => handleInputChange('completion_notes', e.target.value)}
              placeholder="Any additional notes about the schedule completion..."
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isSubmitting ||
                !formData.shoot_start_time ||
                !formData.shoot_end_time
              }
              className="flex-1"
            >
              {isSubmitting ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  Completing...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Complete Schedule
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  )
}

// Keep ShootCompletionForm as alias for backward compatibility
export const ShootCompletionForm = ScheduleCompletionForm
export type ShootCompletionData = ScheduleCompletionData
