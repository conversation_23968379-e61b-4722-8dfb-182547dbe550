'use client'

import { useEffect } from 'react'

export function ServiceWorkerRegistration() {
  useEffect(() => {
    // Register service worker for performance optimization
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      // Use requestIdleCallback to defer registration
      const registerSW = () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration)
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError)
          })
      }

      if ('requestIdleCallback' in window) {
        requestIdleCallback(registerSW)
      } else {
        setTimeout(registerSW, 1000)
      }
    }
  }, [])

  return null
}