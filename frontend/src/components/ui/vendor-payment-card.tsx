'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  Building2, 
  Mail, 
  Phone, 
  User, 
  DollarSign,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'
import type { OutsourcingVendor } from '@/types'

interface VendorPaymentCardProps {
  vendor: OutsourcingVendor
  onRecordPayment: (vendor: OutsourcingVendor) => void
  compact?: boolean
  totalOwed?: number
  totalPaid?: number
  projectCustomId?: string
}

export function VendorPaymentCard({ vendor, onRecordPayment, compact = false, totalOwed = 0, totalPaid = 0, projectCustomId }: VendorPaymentCardProps) {
  const getSpecializationIcon = (specialization?: string) => {
    if (!specialization) return <Building2 className="w-4 h-4" />
    
    switch (specialization.toLowerCase()) {
      case 'photo':
      case 'photography':
        return <Building2 className="w-4 h-4" />
      case 'video':
        return <Building2 className="w-4 h-4" />
      case 'editor':
        return <Building2 className="w-4 h-4" />
      case 'drone':
        return <Building2 className="w-4 h-4" />
      case 'pilot':
        return <User className="w-4 h-4" />
      default:
        return <Building2 className="w-4 h-4" />
    }
  }

  const getSpecializationColor = (specialization?: string) => {
    if (!specialization) return 'bg-muted text-muted-foreground border-border'
    
    switch (specialization.toLowerCase()) {
      case 'photo':
      case 'photography':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20'
      case 'video':
        return 'bg-purple-500/10 text-purple-500 border-purple-500/20'
      case 'editor':
        return 'bg-green-500/10 text-green-500 border-green-500/20'
      case 'drone':
        return 'bg-orange-500/10 text-orange-500 border-orange-500/20'
      case 'pilot':
        return 'bg-indigo-500/10 text-indigo-500 border-indigo-500/20'
      case 'post-processing':
        return 'bg-pink-500/10 text-pink-500 border-pink-500/20'
      case 'softwares':
        return 'bg-cyan-500/10 text-cyan-500 border-cyan-500/20'
      default:
        return 'bg-muted text-muted-foreground border-border'
    }
  }

  const paymentProgress = totalOwed > 0 ? (totalPaid / totalOwed) * 100 : 0
  const remainingAmount = totalOwed - totalPaid

  if (compact) {
    return (
      <div className="app-card p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getSpecializationColor(vendor.specialization)}`}>
                {getSpecializationIcon(vendor.specialization)}
                <span className="ml-1">{vendor.specialization || 'General'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <h4 className="font-medium text-foreground">{vendor.name}</h4>
                {projectCustomId && (
                  <span
                    className="px-2 py-0.5 rounded border border-border text-[10px] text-muted-foreground bg-muted/50"
                    title="Project ID"
                  >
                    {projectCustomId}
                  </span>
                )}
              </div>
            </div>

            {/* Payment Progress */}
            {totalOwed > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Payment Progress</span>
                  <span className="font-medium">
                    ₹{totalPaid.toLocaleString()} / ₹{totalOwed.toLocaleString()}
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(paymentProgress, 100)}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{paymentProgress.toFixed(1)}% paid</span>
                  {remainingAmount > 0 && (
                    <span className="text-orange-600">₹{remainingAmount.toLocaleString()} pending</span>
                  )}
                </div>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2 ml-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onRecordPayment(vendor)}
            >
              <DollarSign className="w-4 h-4 mr-1" />
              Record Payment
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="app-card hover:shadow-md transition-colors overflow-hidden">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-card-foreground">
                {vendor.name}
              </h3>
              {projectCustomId && (
                <span
                  className="px-2 py-0.5 rounded border border-border text-[10px] text-muted-foreground bg-muted/50"
                  title="Project ID"
                >
                  {projectCustomId}
                </span>
              )}
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getSpecializationColor(vendor.specialization)}`}>
                {getSpecializationIcon(vendor.specialization)}
                <span className="ml-1">{vendor.specialization || 'General'}</span>
              </div>
            </div>
            {vendor.contact_person && (
              <p className="text-sm text-muted-foreground mb-2">
                Contact: {vendor.contact_person}
              </p>
            )}
          </div>
        </div>

        {/* Payment Progress */}
        {totalOwed > 0 && (
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Payment Progress</span>
              <span className="font-medium">
                ₹{totalPaid.toLocaleString()} / ₹{totalOwed.toLocaleString()}
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-3">
              <div
                className="bg-green-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(paymentProgress, 100)}%` }}
              />
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">{paymentProgress.toFixed(1)}% paid</span>
              {remainingAmount > 0 && (
                <span className="text-orange-600 font-medium">₹{remainingAmount.toLocaleString()} pending</span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-muted/50 border-t border-border">
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Status: <span className={vendor.is_active ? 'text-green-600' : 'text-red-600'}>
              {vendor.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
          <Button 
            variant="default" 
            size="sm" 
            onClick={() => onRecordPayment(vendor)}
          >
            <DollarSign className="w-4 h-4 mr-1" />
            Record Payment
          </Button>
        </div>
      </div>
    </div>
  )
}
