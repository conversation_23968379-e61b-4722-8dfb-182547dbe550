import { useEffect, useState, useCallback } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import type { Task } from '@/types'

interface UseFastTasksOptions {
  projectId?: string
  shootId?: string
  assignedTo?: string
  onTaskUpdate?: (task: Task) => void
  onTaskCreate?: (task: Task) => void
  onTaskDelete?: (taskId: string) => void
}

export function useFastTasks(options: UseFastTasksOptions = {}) {
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientSupabaseClient()

  const {
    projectId,
    shootId,
    assignedTo,
    onTaskUpdate,
    onTaskCreate,
    onTaskDelete
  } = options

  // Fast task fetching with minimal joins
  const fetchTasks = useCallback(async () => {
    try {
      console.log('🔄 Fetching tasks with filters:', { projectId, shootId, assignedTo })
      if (tasks.length === 0) {
        setLoading(true)
      }
      setError(null)

      let query = supabase
        .from('tasks')
        .select(`
          id, title, description, status, priority, due_date, assigned_to, assigned_role,
          project_id, shoot_id, created_at, updated_at, started_at, completed_at, order,
          assigned_user:users(id, name, email, role),
          project:projects(id, custom_id, name, status),
          shoot:schedules(id, custom_id, scheduled_date)
        `)

      // Apply filters
      if (projectId) {
        query = query.eq('project_id', projectId)
      }
      if (shootId) {
        query = query.eq('shoot_id', shootId)
      }
      if (assignedTo) {
        query = query.eq('assigned_to', assignedTo)
      }

      query = query.order('created_at', { ascending: false })

      const { data, error: fetchError } = await query

      if (fetchError) {
        console.warn(`Warning fetching tasks: ${fetchError.message}`)
        setError(`Failed to load tasks: ${fetchError.message}`)
        setTasks([])
        return
      }

      console.log('✅ Tasks fetched successfully:', data?.length || 0, 'tasks')
      setTasks((data as unknown as Task[]) || [])
    } catch (err) {
      console.error('Unexpected error fetching tasks:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch tasks')
      setTasks([])
    } finally {
      setLoading(false)
    }
  }, [supabase, projectId, shootId, assignedTo])

  // Simplified real-time subscription - only listen for basic changes
  useEffect(() => {
    let isMounted = true;
    
    // Initial fetch
    fetchTasks();

    // Lightweight real-time subscription - no additional queries
    const channel = supabase
      .channel('fast-tasks-realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tasks'
        },
        (payload) => {
          if (!isMounted) return;
          
          console.log('Task change detected:', payload.eventType, (payload.new as any)?.id)
          
          // For fast updates, just trigger a refresh instead of complex logic
          // This is more reliable and faster than trying to merge data
          if (payload.eventType === 'UPDATE') {
            // For updates, we rely on optimistic updates from the UI
            // The real-time subscription is just a backup
            return;
          }
          
          // For INSERT/DELETE, do a quick refresh
          setTimeout(() => {
            if (isMounted) {
              fetchTasks();
            }
          }, 100);
        }
      )
      .subscribe()

    return () => {
      isMounted = false;
      try {
        supabase.removeChannel(channel)
      } catch (err) {
        console.error('Error removing channel:', err)
      }
    }
  }, [supabase, fetchTasks, projectId, shootId, assignedTo])

  // Fast refresh function
  const refresh = useCallback(() => {
    fetchTasks();
  }, [fetchTasks])

  // Optimistic update functions
  const updateTaskOptimistically = useCallback((taskId: string, updates: Partial<Task>) => {
    setTasks(prev => 
      prev.map(task => 
        task.id === taskId 
          ? { ...task, ...updates, updated_at: new Date().toISOString() }
          : task
      )
    )
  }, [])

  const addTaskOptimistically = useCallback((task: Task) => {
    setTasks(prev => [task, ...prev])
  }, [])

  const removeTaskOptimistically = useCallback((taskId: string) => {
    setTasks(prev => prev.filter(t => t.id !== taskId))
  }, [])

  return {
    tasks,
    loading,
    error,
    refresh,
    updateTaskOptimistically,
    addTaskOptimistically,
    removeTaskOptimistically
  }
}
