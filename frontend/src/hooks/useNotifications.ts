import { useState, useEffect } from 'react'
import type {
  Notification,
  NotificationFilters,
  NotificationStats,
  CreateNotificationRequest
} from '@/types/notifications'

export function useNotifications(filters?: NotificationFilters) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [stats, setStats] = useState<NotificationStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  console.log('🔔 useNotifications hook called with filters:', filters)

  // Fetch notifications and stats
  useEffect(() => {
    console.log('🔔 useNotifications effect running')
    
    const fetchNotifications = async () => {
      try {
        setLoading(true)
        setError(null)

        // Provide fallback data instead of making API calls
        console.warn('🔔 Notifications API disabled, using empty data')
        setNotifications([])
        setStats({
          total: 0,
          unread: 0,
          by_type: {
            payment: 0,
            schedule: 0,
            task: 0,
            info: 0,
            success: 0,
            warning: 0,
            error: 0,
            project: 0,
            system: 0
          },
          by_category: {
            task_assigned: 0,
            task_due: 0,
            task_overdue: 0,
            task_completed: 0,
            project_created: 0,
            project_updated: 0,
            schedule_upcoming: 0,
            schedule_changed: 0,
            payment_received: 0,
            payment_overdue: 0,
            system_update: 0,
            user_mention: 0,
            deadline_reminder: 0,
            general: 0
          },
          by_priority: {
            low: 0,
            medium: 0,
            high: 0,
            urgent: 0
          }
        })
      } catch (err) {
        console.error('Error in useNotifications:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch notifications')
        // Ensure we still set empty data even if there's an error
        setNotifications([])
        setStats({
          total: 0,
          unread: 0,
          by_type: {
            payment: 0,
            schedule: 0,
            task: 0,
            info: 0,
            success: 0,
            warning: 0,
            error: 0,
            project: 0,
            system: 0
          },
          by_category: {
            task_assigned: 0,
            task_due: 0,
            task_overdue: 0,
            task_completed: 0,
            project_created: 0,
            project_updated: 0,
            schedule_upcoming: 0,
            schedule_changed: 0,
            payment_received: 0,
            payment_overdue: 0,
            system_update: 0,
            user_mention: 0,
            deadline_reminder: 0,
            general: 0
          },
          by_priority: {
            low: 0,
            medium: 0,
            high: 0,
            urgent: 0
          }
        })
      } finally {
        setLoading(false)
      }
    }
    
    fetchNotifications()
  }, [filters?.read, filters?.type, filters?.category, filters?.priority, filters?.limit, filters?.offset])

  // Return minimal interface
  return {
    notifications,
    stats,
    loading,
    error,
    refetch: () => Promise.resolve(),
    markAsRead: () => Promise.resolve(),
    markAsUnread: () => Promise.resolve(),
    markAllAsRead: () => Promise.resolve(),
    deleteNotification: () => Promise.resolve(),
    createNotification: () => Promise.resolve({} as any)
  }
}