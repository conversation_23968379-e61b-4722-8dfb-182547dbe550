import { useCallback, useRef } from 'react'
import { toast } from 'sonner'
import { optimizedTaskAPI } from '@/lib/optimized-task-api'
import { trackPerformance, completePerformance } from '@/components/debug/PerformanceMonitor'
import type { Task } from '@/types'

interface UseOptimizedTaskUpdateOptions {
  onOptimisticUpdate?: (taskId: string, updates: Partial<Task>) => void
  onRevert?: (taskId: string) => void
  onSuccess?: (task: Task) => void
  onError?: (error: Error, taskId: string) => void
}

interface TaskUpdateQueue {
  [taskId: string]: {
    updates: Partial<Task>
    timestamp: number
    retryCount: number
  }
}

export function useOptimizedTaskUpdate(options: UseOptimizedTaskUpdateOptions = {}) {
  const {
    onOptimisticUpdate,
    onRevert,
    onSuccess,
    onError
  } = options

  // Queue for batching rapid updates
  const updateQueue = useRef<TaskUpdateQueue>({})
  const processingQueue = useRef<Set<string>>(new Set())

  const updateTaskStatus = useCallback(async (
    task: Task,
    newStatus: Task['status'],
    additionalUpdates: Partial<Task> = {}
  ) => {
    const taskId = task.id
    const timestamp = Date.now()

    // Track performance
    const perfId = trackPerformance(`task-status-update-${task.status}-to-${newStatus}`)

    // OPTIMIZATION 1: Instant UI feedback (0ms delay)
    const optimisticUpdates: Partial<Task> = {
      status: newStatus,
      updated_at: new Date().toISOString(),
      ...additionalUpdates
    }

    // Add timestamps based on status transitions
    if (newStatus === 'in_progress' && task.status === 'pending') {
      optimisticUpdates.started_at = new Date().toISOString()
    } else if (newStatus === 'completed' && task.status === 'in_progress') {
      optimisticUpdates.completed_at = new Date().toISOString()
    }

    // Apply optimistic update immediately
    onOptimisticUpdate?.(taskId, optimisticUpdates)

    // OPTIMIZATION 2: Debounce rapid updates
    // If user clicks multiple times quickly, only send the latest update
    updateQueue.current[taskId] = {
      updates: optimisticUpdates,
      timestamp,
      retryCount: 0
    }

    // Wait 50ms to batch rapid clicks
    await new Promise(resolve => setTimeout(resolve, 50))

    // Check if this is still the latest update
    const queuedUpdate = updateQueue.current[taskId]
    if (!queuedUpdate || queuedUpdate.timestamp !== timestamp) {
      console.log('🚫 Skipping outdated update for task:', taskId)
      return
    }

    // Prevent duplicate processing
    if (processingQueue.current.has(taskId)) {
      console.log('🚫 Update already in progress for task:', taskId)
      return
    }

    processingQueue.current.add(taskId)

    try {
      // OPTIMIZATION 3: Use ultra-fast API with minimal payload
      const updatedTask = await optimizedTaskAPI.updateStatus(taskId, newStatus, {
        ...(optimisticUpdates.started_at && { started_at: optimisticUpdates.started_at }),
        ...(optimisticUpdates.completed_at && { completed_at: optimisticUpdates.completed_at }),
        ...additionalUpdates
      })

      // Success callback
      onSuccess?.(updatedTask as Task)

      // Track success
      completePerformance(perfId, 'success')

      // Clean up queue
      delete updateQueue.current[taskId]

      console.log('✅ Task status updated successfully:', taskId, newStatus)

    } catch (error) {
      console.error('❌ Task update failed:', error)
      
      const queuedUpdate = updateQueue.current[taskId]
      if (queuedUpdate && queuedUpdate.retryCount < 2) {
        // OPTIMIZATION 4: Automatic retry with exponential backoff
        queuedUpdate.retryCount++
        const retryDelay = Math.pow(2, queuedUpdate.retryCount) * 1000 // 2s, 4s
        
        console.log(`🔄 Retrying task update in ${retryDelay}ms (attempt ${queuedUpdate.retryCount})`)
        
        setTimeout(() => {
          processingQueue.current.delete(taskId)
          updateTaskStatus(task, newStatus, additionalUpdates)
        }, retryDelay)
        
        return
      }

      // OPTIMIZATION 5: Graceful error handling with revert
      onRevert?.(taskId)
      onError?.(error as Error, taskId)

      // Track error
      const errorMessage = error instanceof Error ? error.message : 'Failed to update task'
      completePerformance(perfId, 'error', errorMessage)

      // Show user-friendly error message
      toast.error(`Failed to update task: ${errorMessage}`)

      // Clean up
      delete updateQueue.current[taskId]
    } finally {
      processingQueue.current.delete(taskId)
    }
  }, [onOptimisticUpdate, onRevert, onSuccess, onError])

  // Batch update multiple tasks (for bulk operations)
  const updateMultipleTasks = useCallback(async (
    updates: Array<{ task: Task; status: Task['status']; additionalUpdates?: Partial<Task> }>
  ) => {
    // Apply all optimistic updates immediately
    updates.forEach(({ task, status, additionalUpdates = {} }) => {
      const optimisticUpdates: Partial<Task> = {
        status,
        updated_at: new Date().toISOString(),
        ...additionalUpdates
      }
      onOptimisticUpdate?.(task.id, optimisticUpdates)
    })

    // Process updates in parallel for maximum speed
    const promises = updates.map(({ task, status, additionalUpdates }) =>
      updateTaskStatus(task, status, additionalUpdates)
    )

    try {
      await Promise.allSettled(promises)
    } catch (error) {
      console.error('❌ Batch update failed:', error)
    }
  }, [updateTaskStatus, onOptimisticUpdate])

  // Cancel pending updates (useful for component unmount)
  const cancelPendingUpdates = useCallback(() => {
    Object.keys(updateQueue.current).forEach(taskId => {
      delete updateQueue.current[taskId]
      processingQueue.current.delete(taskId)
    })
  }, [])

  return {
    updateTaskStatus,
    updateMultipleTasks,
    cancelPendingUpdates,
    isPending: (taskId: string) => processingQueue.current.has(taskId)
  }
}
