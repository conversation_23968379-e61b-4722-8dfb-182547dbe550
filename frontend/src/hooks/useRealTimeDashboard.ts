import { useEffect, useState, useCallback, useRef } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import { dashboardApi } from '@/lib/api'
import type { DashboardStats } from '@/types'

export function useRealTimeDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientSupabaseClient()
  const debounceTimeoutRef = useRef<NodeJS.Timeout>()
  const refreshTimeoutRef = useRef<NodeJS.Timeout>()

  // Fetch dashboard stats
  const fetchStats = useCallback(async () => {
    try {
      setError(null)
      const dashboardStats = await dashboardApi.getStats()
      setStats(dashboardStats)
    } catch (err) {
      console.error('Error fetching dashboard stats:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard stats')
    } finally {
      setLoading(false)
    }
  }, [])

  // Debounced fetch to prevent excessive API calls with longer delay
  const debouncedFetchStats = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }
    debounceTimeoutRef.current = setTimeout(() => {
      console.log('Dashboard data changed, refreshing stats')
      fetchStats()
    }, 5000) // Increased to 5 seconds debounce to reduce API load
  }, [fetchStats])

  // Set up real-time subscriptions with optimized filters
  useEffect(() => {
    fetchStats()

    // Create a single channel for all dashboard updates to reduce connection overhead
    const channel = supabase
      .channel('dashboard-realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'projects'
        },
        debouncedFetchStats
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'projects'
        },
        debouncedFetchStats
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'schedules'
        },
        debouncedFetchStats
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'schedules'
        },
        debouncedFetchStats
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tasks'
        },
        debouncedFetchStats
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'tasks'
        },
        debouncedFetchStats
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'payments'
        },
        debouncedFetchStats
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'payments'
        },
        debouncedFetchStats
      )
      .subscribe()

    // Periodic refresh every 5 minutes to ensure data consistency
    refreshTimeoutRef.current = setInterval(() => {
      console.log('Performing periodic dashboard refresh')
      fetchStats()
    }, 5 * 60 * 1000) // 5 minutes

    // Cleanup subscription on unmount
    return () => {
      console.log('Unsubscribing from dashboard real-time updates')
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
      if (refreshTimeoutRef.current) {
        clearInterval(refreshTimeoutRef.current)
      }
      supabase.removeChannel(channel)
    }
  }, [supabase, debouncedFetchStats, fetchStats])

  // Manual refresh function
  const refresh = useCallback(() => {
    setLoading(true)
    fetchStats()
  }, [fetchStats])

  return {
    stats,
    loading,
    error,
    refresh
  }
}
