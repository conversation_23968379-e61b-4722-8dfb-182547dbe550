import { useState, useEffect, useCallback } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import { schedulesApi } from '@/lib/api'
import type { Schedule } from '@/types'

interface UseRealTimeSchedulesOptions {
  projectId?: string
  status?: string
  pilotId?: string
  upcoming?: boolean
  onScheduleCreate?: (schedule: Schedule) => void
  onScheduleUpdate?: (schedule: Schedule) => void
  onScheduleDelete?: (scheduleId: string) => void
}

export function useRealTimeSchedules(options: UseRealTimeSchedulesOptions = {}) {
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientSupabaseClient()

  const { projectId, status, pilotId, upcoming, onScheduleCreate, onScheduleUpdate, onScheduleDelete } = options

  // Fetch schedules
  const fetchSchedules = useCallback(async () => {
    try {
      setError(null)
      console.log('Fetching schedules with filters:', { projectId, status, pilotId, upcoming })
      
      let data: Schedule[]
      
      if (upcoming) {
        data = await schedulesApi.getUpcoming()
      } else {
        data = await schedulesApi.getAll()
      }
      
      // Apply client-side filters
      if (projectId) {
        data = data.filter(schedule => schedule.project_id === projectId)
      }
      if (status) {
        data = data.filter(schedule => schedule.status === status)
      }
      if (pilotId) {
        data = data.filter(schedule => schedule.pilot_id === pilotId)
      }
      
      setSchedules(data)
      console.log(`Loaded ${data.length} schedules`)
    } catch (err) {
      console.error('Error fetching schedules:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch schedules')
    } finally {
      setLoading(false)
    }
  }, [projectId, status, pilotId, upcoming])

  // Optimistic update function
  const updateScheduleOptimistically = useCallback((scheduleId: string, updates: Partial<Schedule>) => {
    setSchedules(prev => 
      prev.map(schedule => 
        schedule.id === scheduleId 
          ? { ...schedule, ...updates, updated_at: new Date().toISOString() }
          : schedule
      )
    )
  }, [])

  // Set up real-time subscriptions
  useEffect(() => {
    fetchSchedules()

    // Create channel for real-time updates
    const channel = supabase
      .channel('schedules-realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'schedules'
        },
        async (payload) => {
          console.log('Schedule created:', payload)
          
          // Fetch the complete schedule with relations
          try {
            const { data: newSchedule, error } = await supabase
              .from('schedules')
              .select(`
                *,
                project:projects(id, custom_id, name, status),
                pilot:users(id, name, email, role),
                vendors:schedule_vendors(
                  id,
                  cost,
                  notes,
                  vendor:outsourcing_vendors(id, name, specialization, contact_person, phone, email)
                )
              `)
              .eq('id', payload.new.id)
              .single()

            if (!error && newSchedule) {
              const schedule = newSchedule as unknown as Schedule
              
              // Apply filters before adding
              const shouldInclude = (!projectId || schedule.project_id === projectId) &&
                                  (!status || schedule.status === status) &&
                                  (!pilotId || schedule.pilot_id === pilotId) &&
                                  (!upcoming || new Date(schedule.scheduled_date) >= new Date())
              
              if (shouldInclude) {
                setSchedules(prev => [schedule, ...prev])
                onScheduleCreate?.(schedule)
              }
            }
          } catch (error) {
            console.error('Error fetching new schedule details:', error)
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'schedules'
        },
        async (payload) => {
          console.log('Schedule updated:', payload)
          
          // Fetch the complete updated schedule with relations
          try {
            const { data: updatedSchedule, error } = await supabase
              .from('schedules')
              .select(`
                *,
                project:projects(id, custom_id, name, status),
                pilot:users(id, name, email, role),
                vendors:schedule_vendors(
                  id,
                  cost,
                  notes,
                  vendor:outsourcing_vendors(id, name, specialization, contact_person, phone, email)
                )
              `)
              .eq('id', payload.new.id)
              .single()

            if (!error && updatedSchedule) {
              const schedule = updatedSchedule as unknown as Schedule
              
              setSchedules(prev => {
                const exists = prev.find(s => s.id === schedule.id)
                const shouldInclude = (!projectId || schedule.project_id === projectId) &&
                                    (!status || schedule.status === status) &&
                                    (!pilotId || schedule.pilot_id === pilotId) &&
                                    (!upcoming || new Date(schedule.scheduled_date) >= new Date())
                
                if (exists && shouldInclude) {
                  // Update existing schedule
                  return prev.map(s => s.id === schedule.id ? schedule : s)
                } else if (!exists && shouldInclude) {
                  // Add new schedule (status changed to match filter)
                  return [schedule, ...prev]
                } else if (exists && !shouldInclude) {
                  // Remove schedule (no longer matches filter)
                  return prev.filter(s => s.id !== schedule.id)
                }
                
                return prev
              })
              
              onScheduleUpdate?.(schedule)
            }
          } catch (error) {
            console.error('Error fetching updated schedule details:', error)
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'schedules'
        },
        (payload) => {
          console.log('Schedule deleted:', payload)
          const deletedScheduleId = payload.old.id
          
          setSchedules(prev => prev.filter(s => s.id !== deletedScheduleId))
          onScheduleDelete?.(deletedScheduleId)
        }
      )
      .subscribe()

    // Cleanup subscription on unmount
    return () => {
      console.log('Unsubscribing from schedules real-time updates')
      supabase.removeChannel(channel)
    }
  }, [supabase, fetchSchedules, projectId, status, pilotId, upcoming, onScheduleCreate, onScheduleUpdate, onScheduleDelete])

  // Manual refresh function
  const refresh = useCallback(() => {
    setLoading(true)
    fetchSchedules()
  }, [fetchSchedules])

  return {
    schedules,
    loading,
    error,
    refresh,
    updateScheduleOptimistically
  }
}
