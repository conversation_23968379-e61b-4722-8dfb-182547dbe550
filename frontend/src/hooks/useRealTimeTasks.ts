import { useEffect, useState, useCallback } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import type { Task } from '@/types'

interface UseRealTimeTasksOptions {
  projectId?: string
  shootId?: string
  assignedTo?: string
  onTaskUpdate?: (task: Task) => void
  onTaskCreate?: (task: Task) => void
  onTaskDelete?: (taskId: string) => void
}

export function useRealTimeTasks(options: UseRealTimeTasksOptions = {}) {
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [optimisticUpdateIds, setOptimisticUpdateIds] = useState<Set<string>>(new Set())
  const supabase = createClientSupabaseClient()

  const {
    projectId,
    shootId,
    assignedTo,
    onTaskUpdate,
    onTaskCreate,
    onTaskDelete
  } = options

  // Optimized function to fetch tasks with minimal delays
  const fetchTasks = useCallback(async () => {
    try {
      console.log('🔄 useRealTimeTasks: Starting fetchTasks', { projectId, shootId, assignedTo })

      // Only set loading to true on initial fetch
      if (tasks.length === 0) {
        setLoading(true)
      }
      setError(null)

      let query = supabase
        .from('tasks')
        .select(`
          id, title, description, status, priority, due_date, assigned_to, assigned_role,
          project_id, shoot_id, created_at, updated_at, started_at, completed_at, order,
          assigned_user:users(id, name, email, role),
          project:projects(id, custom_id, name, status),
          shoot:schedules(id, custom_id, scheduled_date)
        `)

      // Apply filters if provided
      if (projectId) {
        console.log('🔍 useRealTimeTasks: Filtering by projectId:', projectId)
        query = query.eq('project_id', projectId)
      }
      if (shootId) {
        query = query.eq('shoot_id', shootId)
      }
      if (assignedTo) {
        query = query.eq('assigned_to', assignedTo)
      }

      query = query.order('created_at', { ascending: false })

      console.log('📡 useRealTimeTasks: Executing query...')
      const { data, error: fetchError } = await query

      if (fetchError) {
        console.error('❌ useRealTimeTasks: Error fetching tasks:', fetchError)
        setError(`Failed to load tasks: ${fetchError.message}`)
        setTasks([])
        return
      }

      console.log('✅ useRealTimeTasks: Tasks fetched successfully:', { count: data?.length || 0, data })
      setTasks((data as unknown as Task[]) || [])
    } catch (err) {
      console.error('💥 useRealTimeTasks: Unexpected error fetching tasks:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch tasks')
      setTasks([])
    } finally {
      // Remove delay for immediate loading state update
      setLoading(false)
    }
  }, [supabase, projectId, shootId, assignedTo, tasks.length])

  // Set up real-time subscription with optimized performance
  useEffect(() => {
    let isMounted = true;

    // Initial fetch without delay
    fetchTasks();

    // Create channel for real-time updates with error handling
    let channel;
    try {
      channel = supabase
        .channel('tasks-realtime')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'tasks'
          },
          async (payload) => {
            if (!isMounted) return;
            
            console.log('Task created:', payload)
            
            // Handle task creation immediately (no delay)
            try {
              // Fetch the complete task with relations
              const { data: newTask, error } = await supabase
                .from('tasks')
                .select(`
                  id, title, description, status, priority, due_date, assigned_to, assigned_role,
                  project_id, shoot_id, comments, created_at, updated_at, started_at, completed_at,
                  assigned_user:users(id, name, email, role),
                  project:projects(id, custom_id, name, status),
                  shoot:schedules(id, custom_id, scheduled_date)
                `)
                .eq('id', payload.new.id)
                .single()

              if (!isMounted) return;

              if (!error && newTask) {
                const task = newTask as unknown as Task

                // Check if task matches our filters
                const matchesFilters = (
                  (!projectId || task.project_id === projectId) &&
                  (!shootId || task.shoot_id === shootId) &&
                  (!assignedTo || task.assigned_to === assignedTo)
                )

                if (matchesFilters) {
                  // Update state only if task doesn't already exist
                  setTasks(prev => {
                    if (prev.some(t => t.id === task.id)) {
                      return prev; // Task already exists, don't update
                    }
                    return [task, ...prev];
                  });
                  onTaskCreate?.(task)
                }
              }
            } catch (err) {
              console.error('Error handling task creation:', err)
            }
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'tasks'
          },
          async (payload) => {
            if (!isMounted) return;
            
            console.log('Task updated:', payload)
            
            // Skip real-time updates for tasks that were recently optimistically updated
            if (optimisticUpdateIds.has(payload.new.id)) {
              console.log('🚫 Ignoring real-time update for optimistically updated task:', payload.new.id)
              return
            }

            // Handle task update immediately (no delay)
            try {
              // Fetch the complete updated task with relations
              const { data: updatedTask, error } = await supabase
                .from('tasks')
                .select(`
                  id, title, description, status, priority, due_date, assigned_to, assigned_role,
                  project_id, shoot_id, created_at, updated_at, started_at, completed_at, order,
                  assigned_user:users(id, name, email, role),
                  project:projects(id, custom_id, name, status),
                  shoot:schedules(id, custom_id, scheduled_date)
                `)
                .eq('id', payload.new.id)
                .single()

              if (!isMounted) return;

              if (!error && updatedTask) {
                const task = updatedTask as unknown as Task

                // Only update if the task exists in our current state
                setTasks(prev => {
                  const taskExists = prev.some(t => t.id === task.id);
                  if (!taskExists) return prev; // Don't update if task doesn't exist

                  return prev.map(t => t.id === task.id ? task : t);
                });

                onTaskUpdate?.(task)
              }
            } catch (err) {
              console.error('Error handling task update:', err)
            }
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'DELETE',
            schema: 'public',
            table: 'tasks'
          },
          (payload) => {
            if (!isMounted) return;
            
            console.log('Task deleted:', payload)
            
            // Use a debounce mechanism to prevent rapid state updates
            const handleTaskDeletion = () => {
              try {
                if (!isMounted) return; // Check again if component is still mounted
                
                const deletedTaskId = payload.old.id
                
                // Only update if the task exists in our current state
                setTasks(prev => {
                  const taskExists = prev.some(t => t.id === deletedTaskId);
                  if (!taskExists) return prev; // Don't update if task doesn't exist
                  
                  return prev.filter(t => t.id !== deletedTaskId);
                });
                
                onTaskDelete?.(deletedTaskId)
              } catch (err) {
                console.error('Error handling task deletion:', err)
              }
            };
            
            // Execute with a small delay to prevent rapid updates
            setTimeout(handleTaskDeletion, 100);
          }
        )
        .subscribe()
        
      // Subscribe to the channel
      channel.subscribe((status) => {
        console.log(`Subscription status: ${status}`)
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to tasks')
        }
      })
    } catch (err) {
      console.error('Error setting up real-time subscription:', err)
      setError('Failed to set up real-time updates')
    }

    // Cleanup function
    return () => {
      isMounted = false;

      // Safely remove Supabase channel
      if (channel) {
        try {
          console.log('Unsubscribing from tasks real-time updates')
          supabase.removeChannel(channel)
        } catch (err) {
          console.error('Error removing channel:', err)
        }
      }
    }
  }, [supabase, fetchTasks, projectId, shootId, assignedTo, onTaskCreate, onTaskUpdate, onTaskDelete])

  // Manual refresh function with debounce to prevent flickering
  const refresh = useCallback(() => {
    // We'll use the fetchTasks function which already has debounce logic built in
    // This prevents multiple refreshes in quick succession
    fetchTasks();
  }, [fetchTasks])

  // Update task optimistically
  const updateTaskOptimistically = useCallback((taskId: string, updates: Partial<Task>) => {
    // Track this task as optimistically updated
    setOptimisticUpdateIds(prev => new Set(prev).add(taskId))

    // Clear the optimistic update flag after 2 seconds
    setTimeout(() => {
      setOptimisticUpdateIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(taskId)
        return newSet
      })
    }, 2000)

    setTasks(prev =>
      prev.map(task =>
        task.id === taskId
          ? { ...task, ...updates, updated_at: new Date().toISOString() }
          : task
      )
    )
  }, [])

  // Add task optimistically
  const addTaskOptimistically = useCallback((task: Task) => {
    setTasks(prev => [task, ...prev])
  }, [])

  // Remove task optimistically
  const removeTaskOptimistically = useCallback((taskId: string) => {
    setTasks(prev => prev.filter(t => t.id !== taskId))
  }, [])

  return {
    tasks,
    loading,
    error,
    refresh,
    updateTaskOptimistically,
    addTaskOptimistically,
    removeTaskOptimistically
  }
}
