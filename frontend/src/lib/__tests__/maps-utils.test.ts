import { extractLocationFromMapsUrl } from '../maps-utils'

describe('Maps Utils', () => {
  describe('extractLocationFromMapsUrl', () => {
    it('should extract location from Google Maps place URL', async () => {
      const url = 'https://www.google.com/maps/place/Taj+Mahal,+Dharmapuri,+Forest+Colony,+Tajganj,+Agra,+Uttar+Pradesh+282001/@27.1751448,78.0399665,17z'
      const result = await extractLocationFromMapsUrl(url)
      
      expect(result).toBeTruthy()
      expect(result?.formattedLocation).toContain('Taj Mahal')
    })

    it('should extract location from Google Maps search URL', async () => {
      const url = 'https://www.google.com/maps/search/Pizza+Hut+Mumbai'
      const result = await extractLocationFromMapsUrl(url)
      
      expect(result).toBeTruthy()
      expect(result?.formattedLocation).toContain('Pizza Hut')
    })

    it('should extract location from Google Maps query parameter', async () => {
      const url = 'https://maps.google.com/?q=Red+Fort,+Delhi'
      const result = await extractLocationFromMapsUrl(url)
      
      expect(result).toBeTruthy()
      expect(result?.formattedLocation).toContain('Red Fort')
      expect(result?.formattedLocation).toContain('Delhi')
    })

    it('should return null for invalid URLs', async () => {
      const url = 'https://example.com/not-a-maps-url'
      const result = await extractLocationFromMapsUrl(url)
      
      expect(result).toBeNull()
    })

    it('should handle URLs with coordinates', async () => {
      const url = 'https://www.google.com/maps/@28.6139,77.2090,15z'
      const result = await extractLocationFromMapsUrl(url)
      
      // Without API key, this should return null as we can't reverse geocode
      // With API key, it would return location info
      expect(result).toBeDefined()
    })

    it('should extract from maps.app.goo.gl URLs', async () => {
      const url = 'https://maps.app.goo.gl/example'
      const result = await extractLocationFromMapsUrl(url)
      
      // This would need the actual shortened URL to work
      expect(result).toBeDefined()
    })
  })
})

// Example URLs for manual testing:
/*
1. Place URL: https://www.google.com/maps/place/India+Gate,+Kartavya+Path,+India+Gate,+New+Delhi,+Delhi+110001/@28.6129,77.2295,17z
2. Search URL: https://www.google.com/maps/search/Starbucks+Connaught+Place+Delhi
3. Query URL: https://maps.google.com/?q=Gateway+of+India,+Mumbai
4. Coordinates: https://www.google.com/maps/@19.2183,72.8781,15z
5. Short URL: https://goo.gl/maps/example (would need real URL)
*/
