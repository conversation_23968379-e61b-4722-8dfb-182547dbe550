import { createClientSupabaseClient } from './auth'
import { getUpdatedProjectAmounts } from './project-calculations'
import type {
  User,
  UserRole,
  Client,
  ContactPerson,
  Project,
  Schedule,
  Expense,
  Task,
  Payment,
  OutsourcingVendor,
  CreateClientForm,
  CreateContactPersonForm,
  CreateProjectForm,
  CreateScheduleForm,
  CreateExpenseForm,
  CreateTaskForm,
  DashboardStats,
  ActivityLog
} from '@/types'
import { apiCache, cacheKeys, invalidateCache } from '@/lib/cache'
import { measureApiCall } from '@/lib/performance'

const supabase = createClientSupabaseClient()

// Preview next structured ID for create-mode UX only
export async function peekNextId(entity: 'client' | 'project' | 'schedule' | 'vendor'): Promise<string | null> {
  try {
    const { data, error } = await (supabase as any).rpc('peek_next_entity_id', { entity_type: entity })
    if (error) {
      console.warn('peekNextId RPC failed:', error)
      return null
    }
    // data is expected to be text; normalize to string or null
    return (data ?? null) as string | null
  } catch (e) {
    console.warn('peekNextId unexpected error:', e)
    return null
  }
}

// Convenience helper specifically for Outsourcing Vendors (CYMOU series)
export async function previewNextVendorId(): Promise<string | null> {
  return peekNextId('vendor')
}

// Users API
export const usersApi = {
  async getAll(): Promise<User[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('name')

    if (error) throw error
    return data || []
  },

  async getByRole(role: UserRole): Promise<User[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('role', role)
      .order('name')

    if (error) throw error
    return data || []
  },

  async getUsersByRoles(): Promise<Record<string, User[]>> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .in('role', ['pilot', 'editor', 'manager', 'admin'])
      .order('name')

    if (error) throw error

    const usersByRole: Record<string, User[]> = {
      pilot: [],
      editor: [],
      admin: [], // manager and admin can handle admin tasks
    }

    data?.forEach((user: any) => {
      if (user.role === 'pilot') {
        usersByRole.pilot.push(user)
      } else if (user.role === 'editor') {
        usersByRole.editor.push(user)
      } else if (user.role === 'manager' || user.role === 'admin') {
        usersByRole.admin.push(user)
      }
    })

    return usersByRole
  },

  async create(userData: { name: string; email: string; role: UserRole }): Promise<User> {
    // Use the server-side API endpoint for user creation
    const response = await fetch('/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to create user')
    }

    const result = await response.json()
    return result.data
  },

  async update(id: string, updates: Partial<{ name: string; email: string; role: UserRole }>): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    // First delete from users table
    const { error: userError } = await supabase
      .from('users')
      .delete()
      .eq('id', id)

    if (userError) throw userError

    // Then delete from Supabase Auth
    const { error: authError } = await supabase.auth.admin.deleteUser(id)
    if (authError) {
      console.warn('Failed to delete user from auth:', authError)
      // Don't throw here as the user record is already deleted
    }
  }
}

// Clients API
export const clientsApi = {
  async getAll(): Promise<Client[]> {
    const response = await fetch('/api/clients')
    
    if (!response.ok) {
      throw new Error('Failed to fetch clients')
    }
    
    const data = await response.json()
    return data || []
  },

  async getById(id: string): Promise<Client | null> {
    const { data, error } = await supabase
      .from('clients')
      .select(`
        id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, created_at, updated_at,
        contact_persons (*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async create(client: CreateClientForm): Promise<Client> {
    const response = await fetch('/api/clients', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(client),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to create client')
    }

    const data = await response.json()
    return data
  },

  async update(id: string, updates: Partial<CreateClientForm>): Promise<Client> {
    const { data, error } = await (supabase as any)
      .from('clients')
      .update(updates)
      .eq('id', id)
      .select('id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, created_at, updated_at')
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    console.log('Deleting client via API endpoint:', id)

    const response = await fetch(`/api/clients/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Delete client API error:', errorData)
      throw new Error(errorData.error || 'Failed to delete client')
    }

    const result = await response.json()
    console.log('Client deleted successfully:', result)
  }
}

// Contact Persons API
export const contactPersonsApi = {
  async getByClientId(clientId: string): Promise<ContactPerson[]> {
    const { data, error } = await supabase
      .from('contact_persons')
      .select('*')
      .eq('client_id', clientId)
      .order('is_primary', { ascending: false })
      .order('name')

    if (error) throw error
    return data || []
  },

  async create(contactPerson: CreateContactPersonForm & { client_id: string }): Promise<ContactPerson> {
    const { data, error } = await (supabase as any)
      .from('contact_persons')
      .insert(contactPerson)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, contactPerson: Partial<CreateContactPersonForm>): Promise<ContactPerson> {
    const { data, error } = await (supabase as any)
      .from('contact_persons')
      .update(contactPerson)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await (supabase as any)
      .from('contact_persons')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  async setPrimary(id: string, clientId: string): Promise<void> {
    // First, unset all primary contacts for this client
    await (supabase as any)
      .from('contact_persons')
      .update({ is_primary: false })
      .eq('client_id', clientId)

    // Then set the specified contact as primary
    const { error } = await (supabase as any)
      .from('contact_persons')
      .update({ is_primary: true })
      .eq('id', id)

    if (error) throw error
  }
}

// Projects API
export const projectsApi = {
  async getAll(): Promise<Project[]> {
    // Check cache first
    const cacheKey = cacheKeys.projects()
    const cached = apiCache.get<Project[]>(cacheKey)
    if (cached) return cached

    const { data, error } = await measureApiCall('projects.getAll', async () =>
      await supabase
        .from('projects')
        .select(`
          id, custom_id, name, description, client_id, location, status, total_amount, amount_received, amount_pending, vendor_payment_status, created_at, updated_at,
          client:clients(id, custom_id, name, client_type),
          contact_person:contact_persons(id, name, phone, email),
          schedules:schedules(count)
        `)
        .order('created_at', { ascending: false })
        .limit(50) // Reduced limit for better performance
    )

    if (error) throw error
    const projects = (data as unknown as Project[]) || []

    // Cache for 5 minutes (increased from 2 minutes)
    apiCache.set(cacheKey, projects, 5 * 60 * 1000)
    return projects
  },

  async getById(id: string): Promise<Project | null> {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        id, custom_id, name, description, client_id, location, google_maps_link, status, total_amount, gst_inclusive, amount_received, amount_pending, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date, vendor_payment_date, vendor_payment_notes, created_at, updated_at,
        client:clients(*),
        contact_person:contact_persons(*),
        schedules:schedules(*),
        payments:payments(*),
        tasks:tasks(*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return (data as unknown as Project)
  },

  async create(project: CreateProjectForm): Promise<Project> {
    // Use server-side API endpoint for project creation with SharePoint integration
    const response = await fetch('/api/projects', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(project),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to create project')
    }

    const data = await response.json()

    // Auto-generate project-level default tasks based on client type
    // First, get the client data to determine client type
    const { data: clientData, error: clientError } = await supabase
      .from('clients')
      .select('client_type')
      .eq('id', data.client_id)
      .single()

    if (!clientError && clientData && (clientData as any).client_type) {
      try {
        console.log('Creating project-level default tasks for new project:', {
          projectId: data.id,
          clientType: (clientData as any).client_type
        })

        // Call without shootId to create only project-level tasks
        await this.createDefaultTasks(data.id, (clientData as any).client_type, undefined, undefined)

        console.log('Project-level default tasks created successfully for project:', data.id)
      } catch (taskError) {
        console.error('Failed to create default tasks:', taskError)
        // Don't fail the project creation if task creation fails
      }
    }

    // Invalidate cache
    invalidateCache.projects()

    return data as Project
  },

  async createDefaultTasks(projectId: string, clientType: string, shootDate?: string, shootId?: string): Promise<void> {
    console.log('=== createDefaultTasks START ===')
    console.log('Parameters:', { projectId, clientType, shootDate, shootId })
    console.log('Task type:', shootId ? 'SHOOT-BASED TASKS' : 'PROJECT-LEVEL TASKS')

    try {
      const { getDefaultTasksForClientType, createTasksFromTemplates } = await import('@/lib/default-tasks')

      // Validate inputs
      if (!projectId || !clientType) {
        console.error('❌ VALIDATION ERROR: Missing required parameters:', { projectId, clientType })
        throw new Error('Project ID and client type are required')
      }

      // Validate projectId format (should be UUID)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
      if (!uuidRegex.test(projectId)) {
        console.error('❌ VALIDATION ERROR: Invalid project ID format:', projectId)
        throw new Error('Invalid project ID format')
      }

      console.log('✅ Input validation passed')

      // Check if tasks already exist to prevent duplicates
    const existingTasksQuery = supabase
      .from('tasks')
      .select('id, title, shoot_id')
      .eq('project_id', projectId)

    // If shootId is provided, check for shoot-specific tasks
    if (shootId) {
      existingTasksQuery.eq('shoot_id', shootId)
    } else {
      // Check for project-level tasks (no shoot_id)
      existingTasksQuery.is('shoot_id', null)
    }

    const { data: existingTasks, error: existingTasksError } = await existingTasksQuery

    if (existingTasksError) {
      console.error('Error checking existing tasks:', existingTasksError)
      throw existingTasksError
    }

    if (existingTasks && existingTasks.length > 0) {
      console.log(`Tasks already exist for ${shootId ? 'shoot ' + shootId : 'project ' + projectId}:`)
      existingTasks.forEach((task: any) => {
        console.log(`  - ${task.title} (${task.shoot_id ? 'shoot task' : 'project task'})`)
      })
      console.log('=== createDefaultTasks END (SKIPPED - DUPLICATES) ===')
      return // Don't create duplicate tasks
    }

    // Get default task templates for this client type
    const templates = getDefaultTasksForClientType(clientType)
    console.log('Templates found for client type "' + clientType + '":', templates.length, 'templates')

    if (templates.length === 0) {
      console.log('No templates found for client type:', clientType)
      console.log('Available client types in templates:', Object.keys((await import('@/lib/default-tasks')).DEFAULT_TASK_TEMPLATES))
      return
    }

    // Get users by role for task assignment
    const usersByRole = await usersApi.getUsersByRoles()
    console.log('Users by role:', usersByRole)

    // Create a mapping of role to first available user ID
    const roleToUserId: Record<string, string> = {}
    if (usersByRole.pilot.length > 0) roleToUserId.pilot = usersByRole.pilot[0].id
    if (usersByRole.editor.length > 0) roleToUserId.editor = usersByRole.editor[0].id
    if (usersByRole.admin.length > 0) roleToUserId.admin = usersByRole.admin[0].id

    // Fallback: if no specific role users exist, use admin for all tasks
    const fallbackUserId = usersByRole.admin.length > 0 ? usersByRole.admin[0].id : ''
    if (!roleToUserId.pilot && fallbackUserId) roleToUserId.pilot = fallbackUserId
    if (!roleToUserId.editor && fallbackUserId) roleToUserId.editor = fallbackUserId
    if (!roleToUserId.admin && fallbackUserId) roleToUserId.admin = fallbackUserId

    // Ultimate fallback: if no role-specific users exist, use the current authenticated user
    if (!fallbackUserId) {
      console.log('No role-specific users found, trying to use current user')
      try {
        const { data: { user: currentUser } } = await supabase.auth.getUser()
        if (currentUser) {
          console.log('Using current user as fallback:', currentUser.id)
          roleToUserId.pilot = currentUser.id
          roleToUserId.editor = currentUser.id
          roleToUserId.admin = currentUser.id
        } else {
          console.log('No authenticated user found, cannot create tasks')
          return
        }
      } catch (authError) {
        console.error('Error getting current user:', authError)
        return
      }
    }

    console.log('Role to user mapping:', roleToUserId)

    // Separate shoot-based and project-level tasks
    const shootBasedTemplates = templates.filter(t => !t.isProjectTask)
    const projectLevelTemplates = templates.filter(t => t.isProjectTask)

    let createdCount = 0
    let skippedCount = 0

    // Create shoot-based tasks if shootId is provided
    if (shootId && shootBasedTemplates.length > 0) {
      console.log('Creating shoot-based tasks for shoot:', shootId)
      const shootTaskForms = createTasksFromTemplates(shootBasedTemplates, projectId, roleToUserId, shootDate, shootId)

      for (const taskForm of shootTaskForms) {
        if (taskForm.assigned_to) {
          console.log('Creating shoot task:', {
            title: taskForm.title,
            assigned_to: taskForm.assigned_to,
            project_id: taskForm.project_id,
            shoot_id: taskForm.shoot_id
          })
          try {
            const createdTask = await tasksApi.create(taskForm)
            console.log('Shoot task created successfully:', createdTask.id, createdTask.title)
            createdCount++
          } catch (taskCreateError) {
            console.error('Failed to create shoot task:', taskCreateError)
            throw taskCreateError
          }
        } else {
          console.log('Skipping shoot task without assigned user:', taskForm.title)
          skippedCount++
        }
      }
    }

    // Create project-level tasks only if they don't already exist and only when called without shootId
    if (projectLevelTemplates.length > 0 && !shootId) {
      // Check if project-level tasks already exist
      const { data: existingProjectTasks } = await supabase
        .from('tasks')
        .select('id, title')
        .eq('project_id', projectId)
        .is('shoot_id', null)

      if (!existingProjectTasks || existingProjectTasks.length === 0) {
        console.log('Creating project-level tasks for project:', projectId)
        const projectTaskForms = createTasksFromTemplates(projectLevelTemplates, projectId, roleToUserId, shootDate, undefined)

        for (const taskForm of projectTaskForms) {
          if (taskForm.assigned_to) {
            console.log('Creating project task:', {
              title: taskForm.title,
              assigned_to: taskForm.assigned_to,
              project_id: taskForm.project_id,
              shoot_id: taskForm.shoot_id
            })
            try {
              const createdTask = await tasksApi.create(taskForm)
              console.log('Project task created successfully:', createdTask.id, createdTask.title)
              createdCount++
            } catch (taskCreateError) {
              console.error('Failed to create project task:', taskCreateError)
              throw taskCreateError
            }
          } else {
            console.log('Skipping project task without assigned user:', taskForm.title)
            skippedCount++
          }
        }
      } else {
        console.log('Project-level tasks already exist, skipping creation')
      }
    } else if (projectLevelTemplates.length > 0 && shootId) {
      console.log('Skipping project-level task creation during shoot creation - they should already exist from project creation')
    }

      console.log(`=== createDefaultTasks END ===`)
      console.log(`Summary: ${createdCount} tasks created, ${skippedCount} tasks skipped`)
      console.log(`Task type: ${shootId ? 'SHOOT-BASED' : 'PROJECT-LEVEL'}`)
      console.log('=======================================')
    } catch (error) {
      console.error('❌ CRITICAL ERROR in createDefaultTasks:', error)
      console.error('Parameters that caused the error:', { projectId, clientType, shootDate, shootId })
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      })
      console.log('=== createDefaultTasks END (ERROR) ===')
      throw error
    }
  },

  async cleanupDuplicateTasks(projectId: string): Promise<void> {
    console.log('=== cleanupDuplicateTasks START ===')
    console.log('Project ID:', projectId)

    try {
      // Get all tasks for the project
      const { data: allTasks, error } = await supabase
        .from('tasks')
        .select('id, title, shoot_id, created_at')
        .eq('project_id', projectId)
        .order('created_at', { ascending: true })

      if (error) {
        console.error('Error fetching tasks:', error)
        throw error
      }

      if (!allTasks || allTasks.length === 0) {
        console.log('No tasks found for project')
        return
      }

      // Group tasks by title and shoot_id to find duplicates
      const taskGroups: Record<string, any[]> = {}

      allTasks.forEach((task: any) => {
        const key = `${task.title}_${task.shoot_id || 'project'}`
        if (!taskGroups[key]) {
          taskGroups[key] = []
        }
        taskGroups[key].push(task)
      })

      // Find and remove duplicates (keep the oldest one)
      let deletedCount = 0
      for (const [key, tasks] of Object.entries(taskGroups)) {
        if (tasks.length > 1) {
          console.log(`Found ${tasks.length} duplicates for: ${key}`)
          // Keep the first (oldest) task, delete the rest
          const tasksToDelete = tasks.slice(1)

          for (const task of tasksToDelete) {
            console.log(`Deleting duplicate task: ${task.title} (${task.id})`)
            const { error: deleteError } = await supabase
              .from('tasks')
              .delete()
              .eq('id', task.id)

            if (deleteError) {
              console.error('Error deleting duplicate task:', deleteError)
            } else {
              deletedCount++
            }
          }
        }
      }

      console.log(`=== cleanupDuplicateTasks END ===`)
      console.log(`Summary: ${deletedCount} duplicate tasks removed`)
    } catch (error) {
      console.error('Error in cleanupDuplicateTasks:', error)
      throw error
    }
  },

  async fixTaskOrder(): Promise<void> {
    console.log('=== fixTaskOrder START ===')

    try {
      // Get all tasks
      const { data: allTasks, error } = await supabase
        .from('tasks')
        .select('id, title, shoot_id, project_id')

      if (error) {
        console.error('Error fetching tasks:', error)
        throw error
      }

      if (!allTasks || allTasks.length === 0) {
        console.log('No tasks found')
        return
      }

      console.log(`Found ${allTasks.length} tasks to fix`)

      // Define task order mapping
      const taskOrderMap: Record<string, number> = {
        'Plan Flight': 1,
        'Mark GCPs': 2,
        'Script Confirmation': 1,
        'Shoot': 2, // or 3 if Script Confirmation exists
        'File Upload': 3, // or 4 if Script Confirmation exists
        'File Backup': 4, // or 5 if Script Confirmation exists
        'Edit': 5, // or 6 if Script Confirmation exists
        'Post-Processing': 6,
        'Deliver Files': 100,
        'Payment Collect': 101,
      }

      let updatedCount = 0

      // Update tasks in batches
      for (const task of allTasks as any[]) {
        let newOrder = taskOrderMap[(task as any).title]

        if (!newOrder) {
          console.log(`Unknown task title: ${(task as any).title}, skipping`)
          continue
        }

        // Adjust order for tasks that come after Script Confirmation
        if ((task as any).shoot_id && ['Shoot', 'File Upload', 'File Backup', 'Edit'].includes((task as any).title)) {
          // Check if this project has Script Confirmation
          const hasScriptConfirmation = (allTasks as any[]).some((t: any) =>
            t.project_id === (task as any).project_id && t.title === 'Script Confirmation'
          )

          if (hasScriptConfirmation) {
            newOrder += 1 // Shift by 1 if Script Confirmation exists
          }
        }

        // Update the task
        const { error: updateError } = await (supabase as any)
          .from('tasks')
          .update({ order: newOrder })
          .eq('id', (task as any).id)

        if (updateError) {
          console.error(`Error updating task ${(task as any).id}:`, updateError)
        } else {
          console.log(`Updated task "${(task as any).title}" to order ${newOrder}`)
          updatedCount++
        }
      }

      console.log(`=== fixTaskOrder END ===`)
      console.log(`Summary: ${updatedCount} tasks updated`)
    } catch (error) {
      console.error('Error in fixTaskOrder:', error)
      throw error
    }
  },

  async completeShootTasks(shootId: string): Promise<void> {
    console.log('=== completeShootTasks START ===')
    console.log('Shoot ID:', shootId)

    try {
      // Get all shoot-based tasks for this shoot that are not already completed
      const { data: shootTasks, error } = await supabase
        .from('tasks')
        .select('id, title, status')
        .eq('shoot_id', shootId)
        .neq('status', 'completed')
        .neq('status', 'cancelled')

      if (error) {
        console.error('Error fetching shoot tasks:', error)
        throw error
      }

      if (!shootTasks || shootTasks.length === 0) {
        console.log('No incomplete shoot tasks found')
        return
      }

      console.log(`Found ${shootTasks.length} tasks to complete:`, (shootTasks as any[]).map((t: any) => t.title))

      // Complete all shoot tasks
      const { error: updateError } = await (supabase as any)
        .from('tasks')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('shoot_id', shootId)
        .neq('status', 'completed')
        .neq('status', 'cancelled')

      if (updateError) {
        console.error('Error completing shoot tasks:', updateError)
        throw updateError
      }

      console.log(`Successfully completed ${shootTasks.length} shoot tasks`)
      console.log('=== completeShootTasks END ===')
    } catch (error) {
      console.error('Error in completeShootTasks:', error)
      throw error
    }
  },

  async update(id: string, updates: Partial<CreateProjectForm & {
    vendor_payment_status?: 'pending' | 'paid' | 'overdue' | 'cancelled'
    vendor_payment_amount?: number
    vendor_payment_due_date?: string | null
    vendor_payment_date?: string | null
    vendor_payment_notes?: string | null
  }>): Promise<Project> {
    const { data, error } = await (supabase as any)
      .from('projects')
      .update(updates)
      .eq('id', id)
      .select(`
        id, custom_id, name, description, client_id, location, google_maps_link, status, total_amount, gst_inclusive, amount_received, amount_pending, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date, vendor_payment_date, vendor_payment_notes, created_at, updated_at,
        client:clients(*),
        contact_person:contact_persons(*)
      `)
      .single()

    if (error) throw error
    return (data as unknown as Project)
  },

  async delete(id: string): Promise<void> {
    console.log('Deleting project via API endpoint:', id)

    const response = await fetch(`/api/projects/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Delete project API error:', errorData)
      throw new Error(errorData.error || 'Failed to delete project')
    }

    const result = await response.json()
    console.log('Project deleted successfully:', result)
  },

  async recalculateProjectTotal(projectId: string): Promise<Project> {
    // Get project with client and schedules including vendors for accurate outsourcing calculations
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*),
        schedules:schedules(
          *,
          vendors:schedule_vendors(
            *,
            vendor:outsourcing_vendors(*)
          )
        )
      `)
      .eq('id', projectId)
      .single()

    if (projectError) throw projectError
    if (!project) throw new Error('Project not found')

    // Calculate new amounts
    const updatedAmounts = getUpdatedProjectAmounts(
      (project as any).schedules || [],
      (project as any).client,
      (project as any).amount_received
    )

    // Update project with new amounts
    const { data: updatedProject, error: updateError } = await (supabase as any)
      .from('projects')
      .update(updatedAmounts)
      .eq('id', projectId)
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*)
      `)
      .single()

    if (updateError) throw updateError
    return updatedProject
  },

  async updateVendorPaymentStatus(projectId: string): Promise<Project> {
    // Get project with schedules and expenses
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*),
        schedules:schedules(
          *,
          vendors:schedule_vendors(
            *,
            vendor:outsourcing_vendors(*)
          )
        )
      `)
      .eq('id', projectId)
      .single()

    if (projectError) throw projectError
    if (!project) throw new Error('Project not found')

    // Get all expenses for this project with category 'outsourcing'
    const { data: expenses, error: expensesError } = await supabase
      .from('expenses')
      .select('amount, category')
      .eq('project_id', projectId)
      .eq('category', 'outsourcing')

    if (expensesError) throw expensesError

    // Calculate outsourcing expenses
    const outsourcingExpenses = (expenses as any[])?.reduce((sum, expense) => sum + (expense as any).amount, 0) || 0

    // Calculate vendor payment status using the new function
    const { calculateVendorPaymentStatus } = await import('./project-calculations')
    const paymentStatus = calculateVendorPaymentStatus((project as any).schedules || [], outsourcingExpenses)

    // Determine the status to store in the database
    let dbStatus: 'pending' | 'paid' | 'overdue' | 'cancelled' = 'pending'
    if (paymentStatus.status === 'paid') {
      dbStatus = 'paid'
    } else if (paymentStatus.status === 'partial') {
      dbStatus = 'pending' // Partial payments are still considered pending
    }

    // Update project with new vendor payment status
    const { data: updatedProject, error: updateError } = await (supabase as any)
      .from('projects')
      .update({
        vendor_payment_status: dbStatus,
        vendor_payment_amount: paymentStatus.totalPaid
      })
      .eq('id', projectId)
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*)
      `)
      .single()

    if (updateError) throw updateError
    return updatedProject
  }
}

// Schedules API (renamed from Shoots)
export const schedulesApi = {
  async getAll(): Promise<Schedule[]> {
    const { data, error } = await supabase
      .from('schedules')
      .select(`
        id, custom_id, project_id, scheduled_date, scheduled_end_date, actual_date, status, pilot_id, amount, location, google_maps_link, notes, weather_conditions, onedrive_folder_path, is_recurring, recurring_pattern, is_outsourced, device_used, battery_count, shoot_start_time, shoot_end_time, completion_notes, created_at, updated_at,
        project:projects(*),
        pilot:users(*),
        vendors:schedule_vendors(
          *,
          vendor:outsourcing_vendors(*)
        )
      `)
      .order('scheduled_date')

    if (error) throw error
    return (data as unknown as Schedule[]) || []
  },

  async getById(id: string): Promise<Schedule | null> {
    const { data, error } = await supabase
      .from('schedules')
      .select(`
        id, custom_id, project_id, scheduled_date, scheduled_end_date, actual_date, status, pilot_id, amount, location, google_maps_link, notes, weather_conditions, onedrive_folder_path, is_recurring, recurring_pattern, is_outsourced, device_used, battery_count, shoot_start_time, shoot_end_time, completion_notes, created_at, updated_at,
        project:projects(*),
        pilot:users(*),
        vendors:schedule_vendors(
          *,
          vendor:outsourcing_vendors(*)
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return (data as unknown as Schedule)
  },

  async getUpcoming(): Promise<Schedule[]> {
    const { data, error } = await supabase
      .from('schedules')
      .select(`
        *,
        project:projects(*),
        pilot:users(*)
      `)
      .gte('scheduled_date', new Date().toISOString())
      .eq('status', 'scheduled')
      .order('scheduled_date')
      .limit(10)

    if (error) throw error
    return data || []
  },

  async create(schedule: CreateScheduleForm): Promise<Schedule> {
    const { data, error } = await (supabase as any)
      .from('schedules')
      .insert(schedule)
      .select(`
        id, custom_id, project_id, scheduled_date, scheduled_end_date, actual_date, status, pilot_id, amount, location, google_maps_link, notes, weather_conditions, onedrive_folder_path, is_recurring, recurring_pattern, is_outsourced, device_used, battery_count, shoot_start_time, shoot_end_time, completion_notes, created_at, updated_at,
        project:projects(
          id, custom_id, name, description, client_id, location, google_maps_link, status, total_amount, gst_inclusive, amount_received, amount_pending, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date, vendor_payment_date, vendor_payment_notes, created_at, updated_at,
          client:clients(id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, created_at, updated_at)
        ),
        pilot:users(id, email, name, role, avatar_url, created_at, updated_at)
      `)
      .single()

    if (error) throw error

    // Recalculate project total
    await projectsApi.recalculateProjectTotal(schedule.project_id)

    // Create default tasks with due dates based on schedule date
    {
      const projAny = (data as any).project
      const project = Array.isArray(projAny) ? projAny[0] : projAny
      const clientAny = project?.client
      const client = Array.isArray(clientAny) ? clientAny[0] : clientAny

      if (client?.client_type) {
        try {
          console.log('Creating default tasks for new schedule:', {
            projectId: (data as any).project_id,
            scheduleId: (data as any).id,
            clientType: client.client_type,
            scheduleDate: (data as any).scheduled_date
          })

          await projectsApi.createDefaultTasks(
            (data as any).project_id,
            client.client_type,
            (data as any).scheduled_date,
            (data as any).id // Pass the schedule ID for shoot-based tasks
          )

          console.log('Default tasks created successfully for schedule:', (data as any).id)
        } catch (taskError) {
          console.error('Failed to create default tasks for schedule:', taskError)
          // Don't fail the schedule creation if task creation fails
        }
      } else {
        console.log('No client type found, skipping task creation for schedule:', (data as any).id)
      }
    }

    // Queue SharePoint folder creation as background job
    try {
      console.log('Queuing SharePoint folder creation for schedule:', (data as any).id)
      
      const { queueSharePointFolderCreation } = await import('@/lib/background-jobs')
      
      const jobId = queueSharePointFolderCreation('schedule', (data as any).id, {
        scheduleId: (data as any).id,
        customId: (data as any).custom_id,
        scheduledDate: (data as any).scheduled_date
      })
      
      console.log('SharePoint folder creation queued for schedule:', {
        scheduleId: (data as any).id,
        customId: (data as any).custom_id,
        jobId
      })
    } catch (jobError) {
      console.error('Failed to queue SharePoint folder creation for schedule:', jobError)
      // Don't fail the schedule creation if job queuing fails
    }

    return (data as unknown as Schedule)
  },

  async update(id: string, updates: Partial<CreateScheduleForm & { status?: Schedule['status']; actual_date?: string; device_used?: string; battery_count?: number; shoot_start_time?: string; shoot_end_time?: string; completion_notes?: string }>): Promise<Schedule> {
    // Use the API endpoint that includes task due date synchronization
    const response = await fetch(`/api/schedules/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to update schedule')
    }

    const data = await response.json()

    // Recalculate project total
    try {
      await projectsApi.recalculateProjectTotal(data.project_id)
    } catch (error) {
      console.warn('Failed to recalculate project total:', error)
      // Don't fail the update if project total calculation fails
    }

    return data as Schedule
  },

  async delete(id: string): Promise<void> {
    // Get the schedule to find the project_id before deleting
    const { data: schedule, error: getError } = await supabase
      .from('schedules')
      .select('project_id')
      .eq('id', id)
      .single()

    if (getError) throw getError

    const { error } = await supabase
      .from('schedules')
      .delete()
      .eq('id', id)

    if (error) throw error

    // Recalculate project total
      if (schedule) {
        await projectsApi.recalculateProjectTotal((schedule as any).project_id)
      }
    },
  
    async createWithVendors(schedule: CreateScheduleForm, vendors: any[]): Promise<Schedule> {
      // Normalize vendors payload; DB function inserts vendors solely based on p_vendors array
      const normalizedVendors = Array.isArray(vendors)
        ? vendors.map(v => ({
            vendor_id: String((v as any).vendor_id),
            cost: Number((v as any).cost) || 0,
            notes: (v as any).notes || ''
          }))
        : []
  
      // Call the PostgreSQL function to create schedule with vendors
      const { data, error } = await (supabase as any).rpc('create_schedule_with_vendors', {
        p_project_id: schedule.project_id,
        p_scheduled_date: schedule.scheduled_date,
        p_scheduled_end_date: schedule.scheduled_end_date,
        p_pilot_id: schedule.pilot_id,
        p_amount: schedule.amount,
        p_location: schedule.location,
        p_google_maps_link: schedule.google_maps_link,
        p_notes: schedule.notes,
        p_is_recurring: schedule.is_recurring,
        p_recurring_pattern: schedule.recurring_pattern,
        // p_is_outsourced kept for column, but vendors insert does NOT depend on it
        p_is_outsourced: !!schedule.is_outsourced,
        p_schedule_type: schedule.schedule_type,
        p_vendors: normalizedVendors
      })
  
      if (error) throw error
  
      // Recalculate project total
      await projectsApi.recalculateProjectTotal(schedule.project_id)
  
      // Create default tasks with due dates based on schedule date
      const project = await projectsApi.getById(schedule.project_id)
      if (project?.client?.client_type) {
        try {
          console.log('Creating default tasks for new schedule:', {
            projectId: schedule.project_id,
            clientType: project.client.client_type,
            scheduleDate: schedule.scheduled_date
          })
  
          await projectsApi.createDefaultTasks(
            schedule.project_id,
            project.client.client_type,
            schedule.scheduled_date,
            (data as any).id // Pass the schedule ID for shoot-based tasks
          )
  
          console.log('Default tasks created successfully for schedule:', (data as any).id)
        } catch (taskError) {
          console.error('Failed to create default tasks for schedule:', taskError)
          // Don't fail the schedule creation if task creation fails
        }
      } else {
        console.log('No client type found, skipping task creation for schedule:', (data as any).id)
      }
  
      // Queue SharePoint folder creation based on environment configuration
      try {
        console.log('Processing SharePoint folder creation for schedule:', (data as any).id)
        
        const { queueSharePointFolderCreation } = await import('@/lib/background-jobs')
        
        // Determine execution mode from environment variable
        const mode = process.env.SHAREPOINT_FOLDER_CREATION_MODE || 'background-with-fallback'
        
        let executeSync = false
        let fallbackToSync = false
        
        switch (mode) {
          case 'sync':
            executeSync = true
            fallbackToSync = false
            break
          case 'background':
            executeSync = false
            fallbackToSync = false
            break
          case 'background-with-fallback':
          default:
            executeSync = false
            fallbackToSync = true
            break
        }
        
        console.log(`SharePoint folder creation mode: ${mode} (executeSync: ${executeSync}, fallbackToSync: ${fallbackToSync})`)
        
        // Execute SharePoint folder creation based on configuration
        const jobId = await queueSharePointFolderCreation('schedule', (data as any).id, {
          scheduleId: (data as any).id,
          customId: (data as any).custom_id,
          scheduledDate: schedule.scheduled_date
        }, {
          executeSync,
          fallbackToSync
        })
        
        console.log('SharePoint folder creation processed for schedule:', {
          scheduleId: (data as any).id,
          customId: (data as any).custom_id,
          jobId,
          mode,
          executionType: jobId.startsWith('sync') ? 'synchronous' : 'background'
        })
      } catch (jobError) {
        console.error('Failed to process SharePoint folder creation for schedule:', jobError)
        // Don't fail the schedule creation if SharePoint folder creation fails
        // This ensures the user can still create schedules even if SharePoint is down
      }
  
      // Re-fetch full schedule with vendors for immediate UI consumption
      try {
        const full = await schedulesApi.getById((data as any).id)
        if (full) return full
      } catch {}
  
      return data as Schedule
    },
  
    async updateWithVendors(id: string, updates: Partial<CreateScheduleForm>, vendors: any[]): Promise<Schedule> {
      // Align with DB function signature:
      // public.update_schedule_with_vendors(p_schedule_id uuid, p_updates jsonb, p_vendors jsonb)
      // Build updates JSON object with fields the function expects
      const p_updates = {
        scheduled_date: updates.scheduled_date ?? null,
        scheduled_end_date: updates.scheduled_end_date ?? null,
        pilot_id: updates.pilot_id ?? null,
        amount: updates.amount ?? null,
        has_gst: updates.has_gst ?? null,
        gst_amount: updates.gst_amount ?? null,
        total_amount: updates.total_amount ?? null,
        location: updates.location ?? null,
        google_maps_link: updates.google_maps_link ?? null,
        notes: updates.notes ?? null,
        is_recurring: updates.is_recurring ?? null,
        recurring_pattern: updates.recurring_pattern ?? null,
        is_outsourced: updates.is_outsourced ?? null,
        schedule_type: updates.schedule_type ?? null
      };

      // App-side safeguard: send vendors only if is_outsourced is true, else send empty array.
      const shouldSendVendors = !!updates.is_outsourced;
      const normalizedVendors = Array.isArray(vendors) ? vendors : (vendors ? [vendors] : []);
      const p_vendors = shouldSendVendors ? normalizedVendors : [];

      const { data, error } = await (supabase as any).rpc('update_schedule_with_vendors', {
        p_schedule_id: id,
        p_updates,
        p_vendors
      });

      if (error) throw error;

      // Recalculate project total
      await projectsApi.recalculateProjectTotal(
        updates.project_id || (await this.getById(id))?.project_id || ''
      );

      return data as Schedule;
    }
  }

// Keep shootsApi as alias for backward compatibility
export const shootsApi = schedulesApi

// Expenses API
export const expensesApi = {
  async getAll(): Promise<Expense[]> {
    const { data, error } = await supabase
      .from('expenses')
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .order('date', { ascending: false })

    if (error) throw error
    return data || []
  },

  async getByProjectId(projectId: string): Promise<Expense[]> {
    const { data, error } = await supabase
      .from('expenses')
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .eq('project_id', projectId)
      .order('date', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(expense: CreateExpenseForm): Promise<Expense> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data, error } = await (supabase as any)
      .from('expenses')
      .insert({
        ...expense,
        user_id: user.id
      })
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<CreateExpenseForm>): Promise<Expense> {
    const { data, error } = await (supabase as any)
      .from('expenses')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('expenses')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Tasks API
export const tasksApi = {
  async getAll(): Promise<Task[]> {
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:schedules(*)
      `)
      .order('order', { ascending: true, nullsFirst: false })
      .order('created_at', { ascending: true })

    if (error) throw error
    return data || []
  },

  async getAllWithFilters(filters?: {
    assignedTo?: string
    status?: string
    priority?: string
    projectId?: string
    shootId?: string
    limit?: number
    offset?: number
  }): Promise<Task[]> {
    let query = supabase
      .from('tasks')
      .select(`
        id, title, description, status, priority, assigned_to, assigned_role, project_id, shoot_id, due_date, created_at, updated_at, started_at, completed_at, order,
        assigned_user:users(id, name, email, role),
        project:projects(id, custom_id, name, status),
        shoot:schedules(id, custom_id, scheduled_date)
      `)

    // Apply filters
    if (filters?.assignedTo) {
      query = query.eq('assigned_to', filters.assignedTo)
    }
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.priority) {
      query = query.eq('priority', filters.priority)
    }
    if (filters?.projectId) {
      query = query.eq('project_id', filters.projectId)
    }
    if (filters?.shootId) {
      query = query.eq('shoot_id', filters.shootId)
    }

    // Add pagination with reasonable defaults
    const limit = filters?.limit || 25 // Reduced default from 100 to 25
    const offset = filters?.offset || 0
    query = query.range(offset, offset + limit - 1)

    const { data, error } = await query
      .order('order', { ascending: true, nullsFirst: false })
      .order('created_at', { ascending: true })

    if (error) throw error
    return (data as unknown as Task[]) || []
  },

  async getMyTasks(): Promise<Task[]> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    // Get user profile to check role
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError) throw profileError

    // Admin and Manager can see all tasks, others see only assigned tasks
    const isAdminOrManager = userProfile && ((userProfile as any).role === 'admin' || (userProfile as any).role === 'manager')

    const query = supabase
      .from('tasks')
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:schedules(*)
      `)

    // Apply filter based on role
    if (!isAdminOrManager) {
      query.eq('assigned_to', user.id)
    }

    const { data, error } = await query
      .order('order', { ascending: true, nullsFirst: false })
      .order('created_at', { ascending: true })

    if (error) throw error
    return data || []
  },

  async create(task: CreateTaskForm): Promise<Task> {
    console.log('Creating task via API endpoint:', task.title)

    const response = await fetch('/api/tasks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(task),
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Create task API error:', errorData)
      throw new Error(errorData.error || 'Failed to create task')
    }

    const result = await response.json()
    console.log('Task created successfully:', result.title, result.id)
    return result
  },

  async update(id: string, updates: Partial<CreateTaskForm & { started_at?: string; completed_at?: string; comments?: string }>): Promise<Task> {
    // Get the current task data first
    const { data: currentTask, error: fetchError } = await supabase
      .from('tasks')
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:schedules(*)
      `)
      .eq('id', id)
      .single()

    if (fetchError) throw fetchError

    // Add timestamp updates based on status changes
    const finalUpdates = { ...updates }

    if (updates.status && currentTask && updates.status !== (currentTask as any).status) {
      if (updates.status === 'in_progress' && (currentTask as any).status === 'pending') {
        finalUpdates.started_at = new Date().toISOString()
      } else if (updates.status === 'completed' && (currentTask as any).status === 'in_progress') {
        finalUpdates.completed_at = new Date().toISOString()
      }
    }

    const { data, error } = await (supabase as any)
      .from('tasks')
      .update(finalUpdates)
      .eq('id', id)
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:schedules(*)
      `)
      .single()

    if (error) throw error

    // Handle automatic status completion if status changed
    if (updates.status && currentTask && updates.status !== (currentTask as any).status) {
      try {
        const { handleTaskStatusChange } = await import('@/lib/status-completion')
        await handleTaskStatusChange(data, updates.status)
      } catch (statusError) {
        console.error('Error handling task status change:', statusError)
        // Don't fail the task update if status completion fails
      }
    }

    return data
  },

  async delete(id: string): Promise<void> {
    console.log('Deleting task via API endpoint:', id)

    const response = await fetch(`/api/tasks/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Delete task API error:', errorData)
      throw new Error(errorData.error || 'Failed to delete task')
    }

    const result = await response.json()
    console.log('Task deleted successfully:', result)
  },

  async assignTask(taskId: string, userId: string): Promise<Task> {
    // Check if current user has permission to assign tasks
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError) throw profileError

    const isAdminOrManager = userProfile && ((userProfile as any).role === 'admin' || (userProfile as any).role === 'manager')
    if (!isAdminOrManager) {
      throw new Error('Only admin and manager users can assign tasks')
    }

    // Update the task assignment
    const { data, error } = await (supabase as any)
      .from('tasks')
      .update({ assigned_to: userId })
      .eq('id', taskId)
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:schedules(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async bulkAssignTasks(taskIds: string[], userId: string): Promise<void> {
    // Check if current user has permission to assign tasks
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError) throw profileError

    const isAdminOrManager = userProfile && ((userProfile as any).role === 'admin' || (userProfile as any).role === 'manager')
    if (!isAdminOrManager) {
      throw new Error('Only admin and manager users can assign tasks')
    }

    // Update multiple tasks
    const { error } = await (supabase as any)
      .from('tasks')
      .update({ assigned_to: userId })
      .in('id', taskIds)

    if (error) throw error
  },

  async updateAssignment(id: string, assignedTo: string): Promise<Task> {
    const { data, error } = await (supabase as any)
      .from('tasks')
      .update({ assigned_to: assignedTo })
      .eq('id', id)
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:schedules(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async updateDueDate(id: string, dueDate: string | null): Promise<Task> {
    const { data, error } = await (supabase as any)
      .from('tasks')
      .update({ due_date: dueDate })
      .eq('id', id)
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:schedules(*)
      `)
      .single()

    if (error) throw error
    return data
  }
}

// Vendors API
export const vendorsApi = {
  async getAll(): Promise<OutsourcingVendor[]> {
    const { data, error } = await supabase
      .from('outsourcing_vendors')
      .select(`
        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at
      `)
      .order('name')

    if (error) throw error
    return data || []
  },

  async getActive(): Promise<OutsourcingVendor[]> {
    const { data, error } = await supabase
      .from('outsourcing_vendors')
      .select(`
        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data || []
  },

  async getById(id: string): Promise<OutsourcingVendor | null> {
    const { data, error } = await supabase
      .from('outsourcing_vendors')
      .select(`
        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async create(vendor: Omit<OutsourcingVendor, 'id' | 'created_at' | 'updated_at'>): Promise<OutsourcingVendor> {
    const { data, error } = await (supabase as any)
      .from('outsourcing_vendors')
      .insert(vendor)
      .select(`
        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at
      `)
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<Omit<OutsourcingVendor, 'id' | 'created_at' | 'updated_at'>>): Promise<OutsourcingVendor> {
    const { data, error } = await (supabase as any)
      .from('outsourcing_vendors')
      .update(updates)
      .eq('id', id)
      .select(`
        id, custom_id, name, email, phone, address, contact_person, specialization, notes, is_active, created_at, updated_at
      `)
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await (supabase as any)
      .from('outsourcing_vendors')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Payments API
export const paymentsApi = {
  async getAll(): Promise<Payment[]> {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        project:projects(*)
      `)
      .order('payment_date', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(payment: Omit<Payment, 'id' | 'created_at' | 'updated_at'>): Promise<Payment> {
    const { data, error } = await (supabase as any)
      .from('payments')
      .insert(payment)
      .select(`
        *,
        project:projects(*)
      `)
      .single()

    if (error) throw error

    // Update project's amount_received and recalculate totals
    await paymentsApi.updateProjectAmountReceived(payment.project_id)

    return data
  },

  async updateProjectAmountReceived(projectId: string): Promise<void> {
    // Get all payments for this project
    const { data: payments, error: paymentsError } = await (supabase as any)
      .from('payments')
      .select('amount')
      .eq('project_id', projectId)

    if (paymentsError) throw paymentsError

    // Calculate total amount received
    const totalReceived = payments?.reduce((sum: number, p: any) => sum + p.amount, 0) || 0

    // Get project with client and shoots to recalculate totals
    const { data: project, error: projectError } = await (supabase as any)
      .from('projects')
      .select(`
        *,
        client:clients(*),
        schedules:schedules(*)
      `)
      .eq('id', projectId)
      .single()

    if (projectError) throw projectError
    if (!project) throw new Error('Project not found')

    // Calculate updated amounts with new amount_received
    const updatedAmounts = getUpdatedProjectAmounts(
      project.schedules || [],
      project.client,
      totalReceived
    )

    // Update project with new amounts
    const { error: updateError } = await (supabase as any)
      .from('projects')
      .update(updatedAmounts)
      .eq('id', projectId)

    if (updateError) throw updateError
  },

  async update(id: string, updates: Partial<Omit<Payment, 'id' | 'created_at' | 'updated_at'>>): Promise<Payment> {
    // Get the payment to find the project_id before updating
    const { data: existingPayment, error: getError } = await (supabase as any)
      .from('payments')
      .select('project_id')
      .eq('id', id)
      .single()

    if (getError) throw getError

    const { data, error } = await (supabase as any)
      .from('payments')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        project:projects(*)
      `)
      .single()

    if (error) throw error

    // Update project's amount_received and recalculate totals
    await paymentsApi.updateProjectAmountReceived(existingPayment.project_id)

    // If project_id changed, also update the new project
    if (updates.project_id && updates.project_id !== existingPayment.project_id) {
      await paymentsApi.updateProjectAmountReceived(updates.project_id)
    }

    return data
  },

  async delete(id: string): Promise<void> {
    // Get the payment to find the project_id before deleting
    const { data: payment, error: getError } = await (supabase as any)
      .from('payments')
      .select('project_id')
      .eq('id', id)
      .single()

    if (getError) throw getError

    const { error } = await (supabase as any)
      .from('payments')
      .delete()
      .eq('id', id)

    if (error) throw error

    // Update project's amount_received and recalculate totals
    if (payment) {
      await paymentsApi.updateProjectAmountReceived(payment.project_id)
    }
  }
}

// Dashboard API
export const dashboardApi = {
  async getStats(): Promise<DashboardStats> {
    // Check cache first
    const cacheKey = cacheKeys.dashboardStats()
    const cached = apiCache.get<DashboardStats>(cacheKey)
    if (cached) return cached

    // Use optimized database queries with aggregations
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    
    // Get aggregated stats using PostgreSQL functions
    const { data: stats, error } = await supabase.rpc('get_dashboard_stats')

    if (error) {
      console.error('Error fetching dashboard stats:', error)
      throw error
    }
    
    // The function returns an array with a single row, so we need to get the first element
    const statsRow = stats ? (Array.isArray(stats) ? stats[0] : stats) : null
    
    const dashboardStats: DashboardStats = {
      totalProjects: (statsRow?.active_projects || 0) + (statsRow?.completed_projects || 0),
      activeProjects: statsRow?.active_projects || 0,
      completedSchedules: 0, // This would need to be calculated separately if needed
      pendingPayments: 0, // This would need to be calculated separately if needed
      totalRevenue: 0, // This would need to be calculated separately if needed
      monthlyRevenue: statsRow?.monthly_revenue || 0,
      upcomingSchedules: statsRow?.upcoming_schedules || 0,
      overdueTasks: statsRow?.overdue_tasks || 0,
      vendorPayments: {
        totalPending: 0, // This would need to be calculated separately if needed
        totalOverdue: 0, // This would need to be calculated separately if needed
        pendingCount: 0, // This would need to be calculated separately if needed
        overdueCount: 0 // This would need to be calculated separately if needed
      }
    }

    // Cache for 2 minutes (increased from 1 minute)
    apiCache.set(cacheKey, dashboardStats, 2 * 60 * 1000)
    return dashboardStats
  }
}

// Activity Logs API
export const activityLogsApi = {
  async getAll(limit: number = 50): Promise<ActivityLog[]> {
    // Check cache first
    const cacheKey = `activity_logs_${limit}`
    const cached = apiCache.get<ActivityLog[]>(cacheKey)
    if (cached) return cached

    const { data, error } = await supabase
      .from('activity_logs')
      .select(`
        *,
        users!inner(name, email)
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching activity logs:', error)
      throw error
    }

    // Cache for 1 minute
    apiCache.set(cacheKey, data || [], 60 * 1000)
    return data || []
  },

  async getByUserId(userId: string, limit: number = 20): Promise<ActivityLog[]> {
    const { data, error } = await supabase
      .from('activity_logs')
      .select(`
        *,
        users!inner(name, email)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching user activity logs:', error)
      throw error
    }

    return data || []
  },

  async create(activityLog: Omit<ActivityLog, 'id' | 'created_at'>): Promise<ActivityLog> {
    const { data, error } = await supabase
      .from('activity_logs')
      .insert(activityLog)
      .select()
      .single()

    if (error) {
      console.error('Error creating activity log:', error)
      throw error
    }

    // Invalidate cache
    invalidateCache('activity_logs')
    return data
  }
}
