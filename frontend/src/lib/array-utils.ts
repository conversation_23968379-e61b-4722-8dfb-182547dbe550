/**
 * Utility functions for safe array operations
 */

/**
 * Safely checks if a value is an array and performs operations on it
 */
export function safeArrayOperation<T, R>(
  value: T[] | null | undefined,
  operation: (arr: T[]) => R,
  fallback: R
): R {
  return Array.isArray(value) ? operation(value) : fallback
}

/**
 * Safely gets array length
 */
export function safeArrayLength<T>(value: T[] | null | undefined): number {
  return Array.isArray(value) ? value.length : 0
}

/**
 * Safely filters an array
 */
export function safeArrayFilter<T>(
  value: T[] | null | undefined,
  predicate: (item: T) => boolean
): T[] {
  return Array.isArray(value) ? value.filter(predicate) : []
}

/**
 * Safely maps an array
 */
export function safeArrayMap<T, R>(
  value: T[] | null | undefined,
  mapper: (item: T, index: number) => R
): R[] {
  return Array.isArray(value) ? value.map(mapper) : []
}

/**
 * Safely reduces an array
 */
export function safeArrayReduce<T, R>(
  value: T[] | null | undefined,
  reducer: (acc: R, item: T) => R,
  initialValue: R
): R {
  return Array.isArray(value) ? value.reduce(reducer, initialValue) : initialValue
}

/**
 * Safely slices an array
 */
export function safeArraySlice<T>(
  value: T[] | null | undefined,
  start?: number,
  end?: number
): T[] {
  return Array.isArray(value) ? value.slice(start, end) : []
}