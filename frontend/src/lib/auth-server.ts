import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import type { User, UserRole } from '@/types'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Server-side Supabase client
export async function createServerSupabaseClient() {
  const cookieStore = await cookies()

  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll()
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set(name, value, options)
          )
        } catch {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    },
  })
}

// Middleware helper for auth
export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return request.cookies.getAll()
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))
        supabaseResponse = NextResponse.next({
          request,
        })
        cookiesToSet.forEach(({ name, value, options }) =>
          supabaseResponse.cookies.set(name, value, options)
        )
      },
    },
  })

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const {
    data: { user },
    error
  } = await supabase.auth.getUser()

  console.log('🔍 Auth middleware: getUser result:', { 
    user: user ? `User found: ${user.id}` : 'No user', 
    error: error?.message,
    cookies: request.cookies.getAll().map(c => c.name).join(', ')
  })

  // List of paths that don't require authentication
  const unauthenticatedPaths = [
    '/login',
    '/auth',
    '/api/auth/callback',
    '/api/auth/signout',
    '/api/auth/signin',
    '/api/auth/session',
    '/api/auth/providers',
    '/api/auth/csrf',
    '/api/public-preview',
    '/api/ensure-sharepoint-folders',
    '/api/ensure-sharepoint-folder',
    '/api/background-jobs',
    '/api/test-schedule-bg-jobs',
    '/api/test-sharepoint',
    '/api/init',
    '/api/test-createwithvendors',
    '/api/test-db-function',
    '/api/test-background-jobs',
    '/api/test-task-creation-fix'
  ]

  const isUnauthenticatedPath = unauthenticatedPaths.some(path => 
    request.nextUrl.pathname.startsWith(path)
  )

  if (!user && !isUnauthenticatedPath) {
    console.log('🚫 Auth middleware: User not authenticated, redirecting to login, path:', request.nextUrl.pathname)

    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('redirect', request.nextUrl.pathname)
    return NextResponse.redirect(loginUrl)
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object here instead of the supabaseResponse object

  return supabaseResponse
}

// Get current user with profile data
export async function getCurrentUser(): Promise<User | null> {
  const supabase = await createServerSupabaseClient()
  
  const {
    data: { user: authUser },
    error: authError,
  } = await supabase.auth.getUser()

  if (authError || !authUser) {
    return null
  }

  // Get user profile data
  const { data: profile, error: profileError } = await supabase
    .from('users')
    .select('*')
    .eq('id', authUser.id)
    .single()

  if (profileError || !profile) {
    return null
  }

  return {
    id: profile.id,
    email: profile.email,
    name: profile.name,
    role: profile.role as UserRole,
    avatar_url: profile.avatar_url,
    created_at: profile.created_at,
    updated_at: profile.updated_at,
  }
}
