/**
 * Background job system for handling asynchronous tasks like SharePoint folder creation
 */

interface BackgroundJob {
  id: string
  type: 'sharepoint_folder_creation' | 'heatmap_automation' | 'notification_triggers'
  entityType: 'client' | 'project' | 'schedule' | 'notification'
  entityId: string
  payload: any
  status: 'pending' | 'processing' | 'completed' | 'failed'
  attempts: number
  maxAttempts: number
  createdAt: Date
  updatedAt: Date
  error?: string
}

class BackgroundJobQueue {
  private jobs: Map<string, BackgroundJob> = new Map()
  private isProcessing = false
  private processingInterval: NodeJS.Timeout | null = null

  constructor() {
    console.log('🏗️ BackgroundJobQueue constructor called')
    // Start processing jobs every 2 seconds
    this.startProcessing()
    console.log('✅ BackgroundJobQueue initialized and processing started')
  }

  /**
   * Add a new job to the queue
   */
  addJob(type: BackgroundJob['type'], entityType: BackgroundJob['entityType'], entityId: string, payload: any): string {
    const jobId = `${type}_${entityType}_${entityId}_${Date.now()}`

    const job: BackgroundJob = {
      id: jobId,
      type,
      entityType,
      entityId,
      payload,
      status: 'pending',
      attempts: 0,
      maxAttempts: 3,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.jobs.set(jobId, job)
    console.log(`📋 Background job added: ${jobId} (${type} for ${entityType}:${entityId})`)
    console.log(`📊 Total jobs in queue: ${this.jobs.size}`)

    return jobId
  }

  /**
   * Start processing jobs in the background
   */
  private startProcessing() {
    if (this.processingInterval) return

    console.log('🚀 Starting background job processing...')
    // Re-enable background job processing with optimized interval
    this.processingInterval = setInterval(async () => {
      if (this.isProcessing) return
      await this.processNextJob()
    }, 10000) // Process every 10 seconds to reduce CPU load
  }

  /**
   * Stop processing jobs
   */
  stopProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = null
    }
  }

  /**
   * Process the next pending job
   */
  private async processNextJob() {
    const pendingJob = Array.from(this.jobs.values())
      .find(job => job.status === 'pending' && job.attempts < job.maxAttempts)

    if (!pendingJob) return

    this.isProcessing = true
    pendingJob.status = 'processing'
    pendingJob.attempts++
    pendingJob.updatedAt = new Date()

    console.log(`🔄 Processing background job: ${pendingJob.id} (attempt ${pendingJob.attempts}/${pendingJob.maxAttempts})`)

    try {
      await this.executeJob(pendingJob)
      pendingJob.status = 'completed'
      pendingJob.updatedAt = new Date()
      console.log(`✅ Background job completed: ${pendingJob.id}`)
    } catch (error) {
      console.error(`❌ Background job failed: ${pendingJob.id}`, error)
      pendingJob.error = error instanceof Error ? error.message : 'Unknown error'
      pendingJob.updatedAt = new Date()

      if (pendingJob.attempts >= pendingJob.maxAttempts) {
        pendingJob.status = 'failed'
        console.error(`💀 Background job permanently failed after ${pendingJob.maxAttempts} attempts: ${pendingJob.id}`)
      } else {
        pendingJob.status = 'pending' // Retry
        console.log(`🔄 Background job will retry: ${pendingJob.id}`)
      }
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Execute a specific job based on its type
   */
  private async executeJob(job: BackgroundJob) {
    switch (job.type) {
      case 'sharepoint_folder_creation':
        await this.executeSharePointFolderCreation(job)
        break
      case 'heatmap_automation':
        await this.executeHeatmapAutomation(job)
        break
      case 'notification_triggers':
        await this.executeNotificationTriggers(job)
        break
      default:
        throw new Error(`Unknown job type: ${job.type}`)
    }
  }

  /**
   * Execute SharePoint folder creation job
   */
  private async executeSharePointFolderCreation(job: BackgroundJob) {
    const { entityType, entityId, payload } = job

    switch (entityType) {
      case 'client':
        await this.createClientFolder(entityId, payload)
        break
      case 'project':
        await this.createProjectFolder(entityId, payload)
        break
      case 'schedule':
        await this.createScheduleFolder(entityId, payload)
        break
      default:
        throw new Error(`Unknown entity type: ${entityType}`)
    }
  }

  /**
   * Create SharePoint folder for client
   */
  private async createClientFolder(clientId: string, payload: any) {
    const { createClientFolder } = await import('@/lib/microsoft-graph')
    await createClientFolder(clientId, payload.customId, payload.name)
  }

  /**
   * Create SharePoint folder for project
   */
  private async createProjectFolder(projectId: string, payload: any) {
    const { createProjectFolder } = await import('@/lib/microsoft-graph')
    await createProjectFolder(projectId, payload.customId, payload.name, payload.clientId)
  }

  /**
   * Create SharePoint folder for schedule
   */
  private async createScheduleFolder(scheduleId: string, payload: any) {
    console.log(`⚡️ Dynamically importing SharePointService for schedule: ${scheduleId}`);
    const { SharePointService } = await import('@/lib/sharepoint-service')
    console.log(`📞 Calling SharePointService.ensureScheduleFolder for schedule: ${scheduleId}`);
    const result = await SharePointService.ensureScheduleFolder(scheduleId)
    console.log(`✔️ SharePointService.ensureScheduleFolder result for schedule ${scheduleId}:`, result);
  }

  /**
   * Execute heatmap automation job
   */
  private async executeHeatmapAutomation(job: BackgroundJob) {
    const { entityId } = job
    console.log(`🗺️ Executing heatmap automation for schedule: ${entityId}`)

    const { processHeatmapAutomation } = await import('@/lib/heatmap-automation')
    const result = await processHeatmapAutomation(entityId)

    if (!result.success) {
      throw new Error(result.message)
    }

    console.log(`✅ Heatmap automation completed for schedule: ${entityId}`)
  }

  /**
   * Execute notification triggers job
   */
  private async executeNotificationTriggers(job: BackgroundJob) {
    const { payload } = job
    console.log(`🔔 Executing notification triggers: ${payload.triggerType}`)

    const { processNotificationTriggers } = await import('@/lib/notification-triggers')
    const result = await processNotificationTriggers(payload.triggerType, payload.options)

    if (!result.success) {
      throw new Error(result.message)
    }

    console.log(`✅ Notification triggers completed: ${payload.triggerType}, created ${result.count} notifications`)
  }

  /**
   * Get job status
   */
  getJobStatus(jobId: string): BackgroundJob | undefined {
    return this.jobs.get(jobId)
  }

  /**
   * Get all jobs for debugging
   */
  getAllJobs(): BackgroundJob[] {
    return Array.from(this.jobs.values())
  }

  /**
   * Clean up completed and failed jobs older than 1 hour
   */
  cleanup() {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    
    for (const [jobId, job] of this.jobs.entries()) {
      if ((job.status === 'completed' || job.status === 'failed') && job.updatedAt < oneHourAgo) {
        this.jobs.delete(jobId)
        console.log(`🧹 Cleaned up old job: ${jobId}`)
      }
    }
  }
}

// Global instance
let backgroundJobQueue: BackgroundJobQueue | null = null

/**
 * Get the global background job queue instance
 */
export function getBackgroundJobQueue(): BackgroundJobQueue {
  if (!backgroundJobQueue) {
    console.log('🚀 Creating new BackgroundJobQueue instance')
    backgroundJobQueue = new BackgroundJobQueue()
    
    // Clean up old jobs every 30 minutes
    setInterval(() => {
      backgroundJobQueue?.cleanup()
    }, 30 * 60 * 1000)
    
    console.log('🎯 BackgroundJobQueue instance created and cleanup scheduled')
  }
  
  return backgroundJobQueue
}

/**
 * Add a SharePoint folder creation job to the background queue
 * @param entityType - Type of entity (client, project, schedule)
 * @param entityId - ID of the entity
 * @param payload - Job payload
 * @param options - Additional options
 * @returns Job ID or 'sync' if executed synchronously
 */
export async function queueSharePointFolderCreation(
  entityType: 'client' | 'project' | 'schedule',
  entityId: string,
  payload: any,
  options: {
    executeSync?: boolean // Execute synchronously instead of queuing
    fallbackToSync?: boolean // Fall back to sync if queue fails
  } = {}
): Promise<string> {
  const { executeSync = false, fallbackToSync = true } = options

  // If sync execution is requested, execute immediately
  if (executeSync) {
    console.log(`🔄 Executing SharePoint folder creation synchronously for ${entityType}:${entityId}`)
    try {
      await executeSharePointFolderCreationDirect(entityType, entityId, payload)
      console.log(`✅ Synchronous SharePoint folder creation completed for ${entityType}:${entityId}`)
      return 'sync'
    } catch (error) {
      console.error(`❌ Synchronous SharePoint folder creation failed for ${entityType}:${entityId}:`, error)
      throw error
    }
  }

  // Try to queue the job
  try {
    const queue = getBackgroundJobQueue()
    const jobId = queue.addJob('sharepoint_folder_creation', entityType, entityId, payload)
    console.log(`📋 SharePoint folder creation queued for ${entityType}:${entityId} with job ID: ${jobId}`)
    return jobId
  } catch (queueError) {
    console.error(`❌ Failed to queue SharePoint folder creation for ${entityType}:${entityId}:`, queueError)
    
    // Fall back to synchronous execution if queuing fails and fallback is enabled
    if (fallbackToSync) {
      console.log(`🔄 Falling back to synchronous execution for ${entityType}:${entityId}`)
      try {
        await executeSharePointFolderCreationDirect(entityType, entityId, payload)
        console.log(`✅ Fallback synchronous SharePoint folder creation completed for ${entityType}:${entityId}`)
        return 'sync-fallback'
      } catch (syncError) {
        console.error(`❌ Fallback synchronous SharePoint folder creation failed for ${entityType}:${entityId}:`, syncError)
        throw syncError
      }
    }
    
    throw queueError
  }
}

/**
 * Execute SharePoint folder creation directly (synchronously)
 */
async function executeSharePointFolderCreationDirect(
  entityType: 'client' | 'project' | 'schedule',
  entityId: string,
  payload: any
): Promise<void> {
  switch (entityType) {
    case 'client':
      const { createClientFolder } = await import('@/lib/microsoft-graph')
      await createClientFolder(entityId, payload.customId, payload.name)
      break
    case 'project':
      const { createProjectFolder } = await import('@/lib/microsoft-graph')
      await createProjectFolder(entityId, payload.customId, payload.name, payload.clientId)
      break
    case 'schedule':
      const { SharePointService } = await import('@/lib/sharepoint-service')
      await SharePointService.ensureScheduleFolder(entityId)
      break
    default:
      throw new Error(`Unknown entity type: ${entityType}`)
  }
}

/**
 * Get the status of a background job
 */
export function getJobStatus(jobId: string) {
  const queue = getBackgroundJobQueue()
  return queue.getJobStatus(jobId)
}

/**
 * Get all background jobs (for debugging)
 */
export function getAllJobs() {
  const queue = getBackgroundJobQueue()
  return queue.getAllJobs()
}

/**
 * Queue a heatmap automation job for a schedule
 * @param scheduleId - ID of the schedule to process
 * @returns Job ID
 */
export function queueBackgroundJob(type: 'heatmap_automation', entityType: 'schedule', entityId: string, payload: any): string {
  const queue = getBackgroundJobQueue()
  const jobId = queue.addJob(type, entityType, entityId, payload)
  console.log(`📋 Heatmap automation queued for schedule:${entityId} with job ID: ${jobId}`)
  return jobId
}