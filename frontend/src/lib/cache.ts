// Simple in-memory cache for API responses
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

class SimpleCache {
  private cache = new Map<string, CacheEntry<any>>()

  set<T>(key: string, data: T, ttlMs: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    })
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data as T
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

export const apiCache = new SimpleCache()

// Clean up expired entries every 15 minutes to reduce CPU usage
if (typeof window !== 'undefined') {
  setInterval(() => {
    apiCache.cleanup()
  }, 15 * 60 * 1000)
}

// Cache key generators with optimized TTLs
export const cacheKeys = {
  projects: () => 'projects:all', // 10 minutes for projects list
  project: (id: string) => `project:${id}`, // 5 minutes for individual projects
  clients: () => 'clients:all', // 30 minutes for clients list
  client: (id: string) => `client:${id}`, // 10 minutes for individual clients
  users: () => 'users:all', // 30 minutes for users list
  tasks: (filters?: Record<string, any>) => `tasks:${JSON.stringify(filters || {})}`, // 5 minutes for tasks
  dashboardStats: () => 'dashboard:stats', // 2 minutes for dashboard stats
  schedules: (projectId?: string) => projectId ? `schedules:project:${projectId}` : 'schedules:all' // 5 minutes for schedules
}

// Cache invalidation helpers
export const invalidateCache = {
  projects: () => {
    apiCache.delete(cacheKeys.projects())
    apiCache.delete(cacheKeys.dashboardStats())
  },
  project: (id: string) => {
    apiCache.delete(cacheKeys.project(id))
    apiCache.delete(cacheKeys.projects())
    apiCache.delete(cacheKeys.dashboardStats())
  },
  clients: () => {
    apiCache.delete(cacheKeys.clients())
  },
  client: (id: string) => {
    apiCache.delete(cacheKeys.client(id))
    apiCache.delete(cacheKeys.clients())
  },
  tasks: () => {
    // Clear all task-related cache entries
    for (const key of apiCache['cache'].keys()) {
      if (key.startsWith('tasks:')) {
        apiCache.delete(key)
      }
    }
    apiCache.delete(cacheKeys.dashboardStats())
  },
  schedules: (projectId?: string) => {
    if (projectId) {
      apiCache.delete(cacheKeys.schedules(projectId))
      apiCache.delete(cacheKeys.project(projectId))
    }
    apiCache.delete(cacheKeys.schedules())
    apiCache.delete(cacheKeys.dashboardStats())
  },
  all: () => {
    apiCache.clear()
  }
}
