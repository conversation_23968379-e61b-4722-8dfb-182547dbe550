import { getMicrosoftGraphAccessToken } from '@/lib/microsoft-graph'

// Constants for SharePoint configuration
const SHAREPOINT_SITE_URL = 'https://zn6bn.sharepoint.com/sites/files'
const DRIVE_ID = 'b!6BliBHB7rkqGeijn571F56igGQK5UQ9GkI_AIFbTSvvooGrFp94hQr6gPCGK1yYV'

// File type mappings
const FILE_TYPE_MAPPINGS = {
  photos: ['.jpg', '.jpeg', '.png'],
  videos: ['.mp4', '.mov'],
  srt: ['.srt'],
  lrf: ['.lrf']
}

interface FileItem {
  id: string
  name: string
  size: number
  downloadUrl?: string
  webUrl: string
  folder?: {
    childCount: number
  }
}

interface GeoMetadata {
  type: 'photo' | 'video'
  filename: string
  lat?: number
  lng?: number
  timestamp?: string
  start?: { lat: number; lng: number }
  end?: { lat: number; lng: number }
}

interface FolderStructure {
  photos: string
  videos: string
  srt: string
  lrf: string
}

/**
 * Get the Site ID from SharePoint site URL
 */
async function getSiteIdInternal(accessToken: string): Promise<string> {
  try {
    const url = new URL(SHAREPOINT_SITE_URL)
    const hostname = url.hostname
    const sitePath = url.pathname
    const siteIdentifier = `${hostname}:${sitePath}:`
    
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteIdentifier}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      }
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data.id
  } catch (error) {
    console.error('Error getting Site ID:', error)
    throw new Error('Failed to get SharePoint Site ID')
  }
}

/**
 * List all files in a SharePoint folder
 */
async function listFilesInFolder(accessToken: string, folderId: string): Promise<FileItem[]> {
  try {
    const siteId = await getSiteIdInternal(accessToken)
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${folderId}/children`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      }
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data.value || []
  } catch (error) {
    console.error('Error listing files in folder:', error)
    throw error
  }
}

/**
 * Create a folder in SharePoint
 */
async function createFolder(accessToken: string, parentFolderId: string, folderName: string): Promise<string> {
  try {
    const siteId = await getSiteIdInternal(accessToken)
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${parentFolderId}/children`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: folderName,
          folder: {}
        })
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Create folder error:', response.status, errorText)
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data.id
  } catch (error) {
    console.error(`Error creating folder "${folderName}":`, error)
    throw error
  }
}

/**
 * Move a file to a different folder
 */
async function moveFile(accessToken: string, fileId: string, targetFolderId: string, newName?: string): Promise<void> {
  try {
    const siteId = await getSiteIdInternal(accessToken)
    const updateData: any = {
      parentReference: {
        id: targetFolderId
      }
    }

    if (newName) {
      updateData.name = newName
    }

    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${fileId}`,
      {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Move file error:', response.status, errorText)
      throw new Error(`HTTP error! status: ${response.status}`)
    }
  } catch (error) {
    console.error('Error moving file:', error)
    throw error
  }
}

/**
 * Get file type based on extension
 */
function getFileType(filename: string): keyof typeof FILE_TYPE_MAPPINGS | null {
  const extension = '.' + filename.split('.').pop()?.toLowerCase()
  
  for (const [type, extensions] of Object.entries(FILE_TYPE_MAPPINGS)) {
    if (extensions.includes(extension)) {
      return type as keyof typeof FILE_TYPE_MAPPINGS
    }
  }
  
  return null
}

/**
 * Generate a unique filename if duplicate exists
 */
function generateUniqueFilename(originalName: string, existingFiles: string[]): string {
  if (!existingFiles.includes(originalName)) {
    return originalName
  }

  const nameParts = originalName.split('.')
  const extension = nameParts.pop()
  const baseName = nameParts.join('.')
  
  let counter = 1
  let newName: string
  
  do {
    newName = `${baseName}_(${counter}).${extension}`
    counter++
  } while (existingFiles.includes(newName))
  
  return newName
}

/**
 * Create or get folder structure for file organization
 */
async function ensureFolderStructure(accessToken: string, scheduleFolderId: string): Promise<FolderStructure> {
  try {
    // List existing folders
    const existingItems = await listFilesInFolder(accessToken, scheduleFolderId)
    const existingFolders = existingItems.filter(item => item.folder).map(item => item.name)
    
    const folderStructure: FolderStructure = {
      photos: '',
      videos: '',
      srt: '',
      lrf: ''
    }

    // Create Raw folder if it doesn't exist
    let rawFolderId = existingItems.find(item => item.folder && item.name === 'Raw')?.id
    if (!rawFolderId) {
      rawFolderId = await createFolder(accessToken, scheduleFolderId, 'Raw')
    }

    // Create subfolders under Raw
    const rawItems = await listFilesInFolder(accessToken, rawFolderId)
    const rawFolders = rawItems.filter(item => item.folder).map(item => item.name)

    const subfolders = ['Photos', 'Videos', 'SRT', 'LRF']
    for (const subfolder of subfolders) {
      let subfolderId = rawItems.find(item => item.folder && item.name === subfolder)?.id
      if (!subfolderId) {
        subfolderId = await createFolder(accessToken, rawFolderId, subfolder)
      }
      
      const key = subfolder.toLowerCase() as keyof FolderStructure
      folderStructure[key] = subfolderId
    }

    return folderStructure
  } catch (error) {
    console.error('Error ensuring folder structure:', error)
    throw error
  }
}

/**
 * Download file content as buffer for metadata extraction
 */
async function downloadFileContent(accessToken: string, fileId: string): Promise<Buffer> {
  try {
    const siteId = await getSiteIdInternal(accessToken)
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${fileId}/content`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      }
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    return Buffer.from(arrayBuffer)
  } catch (error) {
    console.error('Error downloading file content:', error)
    throw error
  }
}

/**
 * Extract EXIF GPS data from image buffer
 */
async function extractImageGPS(buffer: Buffer): Promise<{ lat: number; lng: number; timestamp?: string } | null> {
  try {
    // Use exifr library for EXIF extraction
    const exifr = await import('exifr')
    const gpsData = await exifr.gps(buffer)
    
    if (gpsData && gpsData.latitude && gpsData.longitude) {
      return {
        lat: gpsData.latitude,
        lng: gpsData.longitude,
        timestamp: (gpsData as any).DateTimeOriginal?.toISOString()
      }
    }
    
    return null
  } catch (error) {
    console.error('Error extracting GPS from image:', error)
    return null
  }
}

/**
 * Parse SRT file for GPS coordinates
 */
async function parseSRTFile(buffer: Buffer): Promise<{ start: { lat: number; lng: number }; end: { lat: number; lng: number } } | null> {
  try {
    const srtContent = buffer.toString('utf-8')
    const parser = await import('srt-parser-2')
    const srtParser = new parser.default()
    const subtitles = srtParser.fromSrt(srtContent)
    
    const gpsCoordinates: Array<{ lat: number; lng: number }> = []
    
    // Extract GPS coordinates from SRT content
    for (const subtitle of subtitles) {
      const text = subtitle.text
      
      // Multiple regex patterns for different GPS coordinate formats in drone SRT files
      const gpsPatterns = [
        // Standard GPS format: GPS(lat,lng) or GPS: lat, lng
        /GPS[\s:]*\(?(-?\d+\.?\d*)[,\s]+(-?\d+\.?\d*)\)?/i,
        // Latitude/Longitude format: LAT: xx.xxx LON: yy.yyy
        /LAT(?:ITUDE)?[\s:]*(-?\d+\.?\d*)[,\s]*LON(?:GITUDE)?[\s:]*(-?\d+\.?\d*)/i,
        // DJI format: [GPS] lat,lng
        /\[GPS\][\s:]*(-?\d+\.?\d*)[,\s]+(-?\d+\.?\d*)/i,
        // Coordinate format: lat,lng (simple comma-separated)
        /^(-?\d{1,3}\.\d+)[,\s]+(-?\d{1,3}\.\d+)$/,
        // Home format: HOME(lat,lng)
        /HOME[\s:]*\(?(-?\d+\.?\d*)[,\s]+(-?\d+\.?\d*)\)?/i,
        // Position format: POS: lat lng
        /POS(?:ITION)?[\s:]*(-?\d+\.?\d*)[,\s]+(-?\d+\.?\d*)/i
      ]
      
      for (const pattern of gpsPatterns) {
        const match = text.match(pattern)
        if (match) {
          const lat = parseFloat(match[1])
          const lng = parseFloat(match[2])
          
          if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
            gpsCoordinates.push({ lat, lng })
            break // Found valid coordinates, move to next subtitle
          }
        }
      }
    }
    
    if (gpsCoordinates.length > 0) {
      return {
        start: gpsCoordinates[0],
        end: gpsCoordinates[gpsCoordinates.length - 1]
      }
    }
    
    return null
  } catch (error) {
    console.error('Error parsing SRT file:', error)
    return null
  }
}

/**
 * Create a public share link for a file
 */
async function createPublicShareLink(accessToken: string, fileId: string): Promise<string> {
  try {
    const siteId = await getSiteIdInternal(accessToken)
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${fileId}/createLink`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'view',
          scope: 'anonymous'
        })
      }
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data.link.webUrl
  } catch (error) {
    console.error('Error creating public share link:', error)
    throw error
  }
}

/**
 * Upload geo metadata JSON file to SharePoint
 */
async function uploadGeoMetadataFile(accessToken: string, scheduleFolderId: string, metadata: GeoMetadata[]): Promise<string> {
  try {
    const siteId = await getSiteIdInternal(accessToken)
    const jsonContent = JSON.stringify(metadata, null, 2)
    
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${scheduleFolderId}:/geo_metadata.json:/content`,
      {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: jsonContent
      }
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data.id
  } catch (error) {
    console.error('Error uploading geo metadata file:', error)
    throw error
  }
}

/**
 * Main function to process heatmap automation for a schedule
 */
export async function processHeatmapAutomation(scheduleId: string): Promise<{ success: boolean; message: string; shareLink?: string }> {
  try {
    console.log('🗺️ Starting heatmap automation for schedule:', scheduleId)
    
    // Get access token
    const accessToken = await getMicrosoftGraphAccessToken()
    
    // Get schedule folder information from database
    const { createClient } = await import('@supabase/supabase-js')
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    const { data: schedule, error } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        sharepoint_folder_id,
        projects!inner (
          id,
          custom_id,
          name,
          clients!inner (
            id,
            custom_id,
            name
          )
        )
      `)
      .eq('id', scheduleId)
      .single()

    if (error || !schedule || !schedule.sharepoint_folder_id) {
      throw new Error('Schedule not found or SharePoint folder not created')
    }

    const scheduleFolderId = schedule.sharepoint_folder_id
    
    // 1. List all files in the schedule folder
    console.log('📂 Listing files in schedule folder...')
    const allFiles = await listFilesInFolder(accessToken, scheduleFolderId)
    const files = allFiles.filter(item => !item.folder) // Only files, not folders
    
    if (files.length === 0) {
      return { success: true, message: 'No files found to process' }
    }

    // 2. Create folder structure
    console.log('📁 Creating folder structure...')
    const folderStructure = await ensureFolderStructure(accessToken, scheduleFolderId)
    
    // 3. Organize files by type
    console.log('🗂️ Organizing files by type...')
    const organizedFiles: Record<string, FileItem[]> = {
      photos: [],
      videos: [],
      srt: [],
      lrf: [],
      other: []
    }

    for (const file of files) {
      const fileType = getFileType(file.name)
      if (fileType) {
        organizedFiles[fileType].push(file)
      } else {
        organizedFiles.other.push(file)
      }
    }

    // Move files to appropriate folders
    for (const [type, typeFiles] of Object.entries(organizedFiles)) {
      if (type === 'other' || typeFiles.length === 0) continue
      
      const targetFolderId = folderStructure[type as keyof FolderStructure]
      if (!targetFolderId) continue

      // Get existing files in target folder to handle duplicates
      const existingFiles = await listFilesInFolder(accessToken, targetFolderId)
      const existingFilenames = existingFiles.map(f => f.name)

      for (const file of typeFiles) {
        const uniqueName = generateUniqueFilename(file.name, existingFilenames)
        await moveFile(accessToken, file.id, targetFolderId, uniqueName !== file.name ? uniqueName : undefined)
        existingFilenames.push(uniqueName)
        console.log(`📦 Moved ${file.name} to ${type} folder`)
      }
    }

    // 4. Extract metadata from organized files (with parallel processing)
    console.log('🌍 Extracting geolocation metadata...')
    const geoMetadata: GeoMetadata[] = []

    // Process photos in parallel (batches of 10 for performance)
    const photoBatches = []
    for (let i = 0; i < organizedFiles.photos.length; i += 10) {
      photoBatches.push(organizedFiles.photos.slice(i, i + 10))
    }

    for (const batch of photoBatches) {
      const photoPromises = batch.map(async (photoFile) => {
        try {
          const buffer = await downloadFileContent(accessToken, photoFile.id)
          const gpsData = await extractImageGPS(buffer)
          
          if (gpsData) {
            console.log(`📸 Extracted GPS from photo: ${photoFile.name}`)
            return {
              type: 'photo' as const,
              filename: photoFile.name,
              lat: gpsData.lat,
              lng: gpsData.lng,
              timestamp: gpsData.timestamp
            }
          }
        } catch (error) {
          console.warn(`⚠️ Failed to extract GPS from photo ${photoFile.name}:`, error)
        }
        return null
      })

      const batchResults = await Promise.all(photoPromises)
      geoMetadata.push(...batchResults.filter(result => result !== null))
    }

    // Process videos with SRT files in parallel
    const videoPromises = organizedFiles.videos.map(async (videoFile) => {
      try {
        // Find matching SRT file
        const videoBaseName = videoFile.name.replace(/\.[^/.]+$/, '')
        const matchingSRT = organizedFiles.srt.find(srtFile => 
          srtFile.name.toLowerCase().includes(videoBaseName.toLowerCase())
        )

        if (matchingSRT) {
          const buffer = await downloadFileContent(accessToken, matchingSRT.id)
          const gpsData = await parseSRTFile(buffer)
          
          if (gpsData) {
            console.log(`🎥 Extracted GPS from video: ${videoFile.name}`)
            return {
              type: 'video' as const,
              filename: videoFile.name,
              start: gpsData.start,
              end: gpsData.end
            }
          }
        }
      } catch (error) {
        console.warn(`⚠️ Failed to extract GPS from video ${videoFile.name}:`, error)
      }
      return null
    })

    const videoResults = await Promise.all(videoPromises)
    geoMetadata.push(...videoResults.filter(result => result !== null))

    // 5. Generate and upload geo_metadata.json
    console.log('📄 Generating geo_metadata.json...')
    const metadataFileId = await uploadGeoMetadataFile(accessToken, scheduleFolderId, geoMetadata)
    
    // 6. Create public share link
    console.log('🔗 Creating public share link...')
    const shareLink = await createPublicShareLink(accessToken, metadataFileId)
    
    // 7. Store share link in database
    await supabase
      .from('schedules')
      .update({ 
        geo_metadata_link: shareLink,
        updated_at: new Date().toISOString()
      })
      .eq('id', scheduleId)

    console.log('✅ Heatmap automation completed successfully')
    
    return {
      success: true,
      message: `Processed ${geoMetadata.length} geotagged files and created metadata file`,
      shareLink
    }

  } catch (error) {
    console.error('❌ Heatmap automation failed:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

/**
 * Queue heatmap automation as a background job
 */
export function queueHeatmapAutomation(scheduleId: string): string {
  const { queueBackgroundJob } = require('@/lib/background-jobs')
  return queueBackgroundJob('heatmap_automation', 'schedule', scheduleId, { scheduleId })
}