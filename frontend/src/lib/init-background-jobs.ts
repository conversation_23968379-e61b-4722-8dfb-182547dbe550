/**
 * Background Jobs Initialization
 * 
 * This module initializes the background job queue when the application starts.
 * The queue is automatically started when the first instance is created.
 */

import { getBackgroundJobQueue } from './background-jobs'

let isInitialized = false

/**
 * Initialize the background job queue
 * This should be called once when the application starts
 */
export function initializeBackgroundJobs() {
  if (isInitialized) {
    console.log('Background job queue already initialized')
    return
  }

  try {
    // Get the background job queue instance (this starts it automatically)
    const queue = getBackgroundJobQueue()
    isInitialized = true
    console.log('✅ Background job queue initialized successfully')
    return queue
  } catch (error) {
    console.error('❌ Failed to initialize background job queue:', error)
  }
}

/**
 * Cleanup function to stop the background job queue
 * This should be called when the application shuts down
 */
export function cleanupBackgroundJobs() {
  if (!isInitialized) {
    return
  }

  try {
    const queue = getBackgroundJobQueue()
    queue.stopProcessing()
    isInitialized = false
    console.log('✅ Background job queue stopped successfully')
  } catch (error) {
    console.error('❌ Failed to stop background job queue:', error)
  }
}

// Auto-initialize in server environments
if (typeof window === 'undefined') {
  console.log('🚀 Server environment detected, initializing background jobs...')
  initializeBackgroundJobs()
}