/**
 * Utility functions for extracting location information from Google Maps links
 */

export interface LocationInfo {
  placeName: string
  city: string
  formattedLocation: string
  coordinates?: {
    lat: number
    lng: number
  }
}

/**
 * Extract location information from Google Maps URL
 */
export async function extractLocationFromMapsUrl(url: string): Promise<LocationInfo | null> {
  try {
    // Validate if it's a Google Maps URL
    if (!isValidGoogleMapsUrl(url)) {
      throw new Error('Invalid Google Maps URL')
    }

    let finalUrl = url

    // If it's a shortened URL, try to resolve it first
    if (isShortenedGoogleMapsUrl(url)) {
      const resolvedUrl = await resolveShortenedUrl(url)
      if (resolvedUrl && resolvedUrl !== url) {
        finalUrl = resolvedUrl
      } else {
        // If resolution failed, try to use Google Maps search as fallback
        return await searchLocationFallback(url)
      }
    }

    // First try to extract location from URL path (no API required)
    const pathLocation = extractLocationFromUrlPath(finalUrl)
    if (pathLocation) {
      return pathLocation
    }

    // If API key is available, try geocoding
    if (process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
      const coordinates = extractCoordinatesFromUrl(finalUrl)
      const placeId = extractPlaceIdFromUrl(finalUrl)

      if (coordinates) {
        return await getLocationFromCoordinates(coordinates.lat, coordinates.lng)
      } else if (placeId) {
        return await getLocationFromPlaceId(placeId)
      }
    }

    return null
  } catch (error) {
    console.error('Error extracting location from Maps URL:', error)
    return null
  }
}

/**
 * Check if URL is a valid Google Maps URL
 */
function isValidGoogleMapsUrl(url: string): boolean {
  const googleMapsPatterns = [
    /^https?:\/\/(www\.)?google\.(com|co\.in|[a-z]{2})\/maps/,
    /^https?:\/\/maps\.google\.(com|co\.in|[a-z]{2})/,
    /^https?:\/\/goo\.gl\/maps/,
    /^https?:\/\/maps\.app\.goo\.gl/
  ]

  return googleMapsPatterns.some(pattern => pattern.test(url))
}

/**
 * Check if URL is a shortened Google Maps URL
 */
function isShortenedGoogleMapsUrl(url: string): boolean {
  const shortenedPatterns = [
    /^https?:\/\/goo\.gl\/maps/,
    /^https?:\/\/maps\.app\.goo\.gl/,
    /^https?:\/\/g\.page\//
  ]

  return shortenedPatterns.some(pattern => pattern.test(url))
}

/**
 * Resolve shortened URL to get the full Google Maps URL
 */
async function resolveShortenedUrl(url: string): Promise<string | null> {
  try {
    // Use our API endpoint to resolve the URL server-side
    const response = await fetch('/api/resolve-maps-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url }),
    })

    if (response.ok) {
      const data = await response.json()
      return data.resolvedUrl || null
    } else {
      const errorData = await response.json()
      console.error('Error resolving URL:', errorData.error)
      return null
    }
  } catch (error) {
    console.error('Error resolving shortened URL:', error)
    return null
  }
}

/**
 * Extract coordinates from Google Maps URL
 */
function extractCoordinatesFromUrl(url: string): { lat: number; lng: number } | null {
  // Pattern for coordinates in URL: @lat,lng,zoom
  const coordPattern = /@(-?\d+\.?\d*),(-?\d+\.?\d*),/
  const match = url.match(coordPattern)
  
  if (match) {
    return {
      lat: parseFloat(match[1]),
      lng: parseFloat(match[2])
    }
  }
  
  // Alternative pattern: !3d<lat>!4d<lng>
  const altPattern = /!3d(-?\d+\.?\d*)!4d(-?\d+\.?\d*)/
  const altMatch = url.match(altPattern)
  
  if (altMatch) {
    return {
      lat: parseFloat(altMatch[1]),
      lng: parseFloat(altMatch[2])
    }
  }
  
  return null
}

/**
 * Extract place ID from Google Maps URL
 */
function extractPlaceIdFromUrl(url: string): string | null {
  const placeIdPattern = /place_id:([a-zA-Z0-9_-]+)/
  const match = url.match(placeIdPattern)
  return match ? match[1] : null
}

/**
 * Get location information from coordinates using reverse geocoding
 */
async function getLocationFromCoordinates(lat: number, lng: number): Promise<LocationInfo | null> {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
  if (!apiKey) {
    console.warn('Google Maps API key not found')
    return null
  }

  try {
    // First try with result_type filter to get more specific results
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}&result_type=establishment|point_of_interest|premise`
    )

    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.status === 'OK' && data.results.length > 0) {
      // Try to find the most specific result (establishment, POI, or premise)
      const specificResult = data.results.find(result =>
        result.types.includes('establishment') ||
        result.types.includes('point_of_interest') ||
        result.types.includes('premise')
      ) || data.results[0]

      return parseGeocodingResult(specificResult)
    } else if (data.status === 'ZERO_RESULTS') {
      // Try again without result_type filter for broader results
      const fallbackResponse = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`
      )

      if (fallbackResponse.ok) {
        const fallbackData = await fallbackResponse.json()
        if (fallbackData.status === 'OK' && fallbackData.results.length > 0) {
          return parseGeocodingResult(fallbackData.results[0])
        }
      }
    }

    return null
  } catch (error) {
    console.error('Error in reverse geocoding:', error)
    return null
  }
}

/**
 * Get location information from place ID
 */
async function getLocationFromPlaceId(placeId: string): Promise<LocationInfo | null> {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
  if (!apiKey) {
    console.warn('Google Maps API key not found')
    return null
  }

  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=name,formatted_address,address_components,types&key=${apiKey}`
    )

    if (!response.ok) {
      throw new Error(`Places API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.status === 'OK' && data.result) {
      return parsePlaceResult(data.result)
    } else {
      console.warn(`Places API returned status: ${data.status}`)
      return null
    }
  } catch (error) {
    console.error('Error fetching place details:', error)
    return null
  }
}

/**
 * Parse geocoding API result to extract place name and city
 */
function parseGeocodingResult(result: any): LocationInfo {
  const addressComponents = result.address_components || []
  
  let placeName = ''
  let city = ''
  
  // Extract place name (establishment, point_of_interest, or premise)
  const establishment = addressComponents.find((comp: any) => 
    comp.types.includes('establishment') || 
    comp.types.includes('point_of_interest') ||
    comp.types.includes('premise')
  )
  
  if (establishment) {
    placeName = establishment.long_name
  }
  
  // Extract city (locality or administrative_area_level_2)
  const locality = addressComponents.find((comp: any) => 
    comp.types.includes('locality') || 
    comp.types.includes('administrative_area_level_2')
  )
  
  if (locality) {
    city = locality.long_name
  }
  
  // If no specific place name found, use the first part of formatted address
  if (!placeName && result.formatted_address) {
    const addressParts = result.formatted_address.split(',')
    placeName = addressParts[0].trim()
  }
  
  const formattedLocation = placeName && city ? `${placeName}, ${city}` : placeName || city || result.formatted_address
  
  return {
    placeName,
    city,
    formattedLocation
  }
}

/**
 * Parse Places API result to extract place name and city
 */
function parsePlaceResult(result: any): LocationInfo {
  const placeName = result.name || ''
  const addressComponents = result.address_components || []
  
  let city = ''
  
  // Extract city from address components
  const locality = addressComponents.find((comp: any) => 
    comp.types.includes('locality') || 
    comp.types.includes('administrative_area_level_2')
  )
  
  if (locality) {
    city = locality.long_name
  }
  
  const formattedLocation = placeName && city ? `${placeName}, ${city}` : placeName || city
  
  return {
    placeName,
    city,
    formattedLocation
  }
}

/**
 * Extract location from URL path as fallback
 */
function extractLocationFromUrlPath(url: string): LocationInfo | null {
  try {
    const urlObj = new URL(url)
    const pathParts = urlObj.pathname.split('/')
    const searchParams = urlObj.searchParams

    // Special handling for shortened URLs
    if (isShortenedGoogleMapsUrl(url)) {
      return handleShortenedUrl(url)
    }

    // Method 1: Look for location in path like /maps/place/Location+Name/
    const placeIndex = pathParts.findIndex(part => part === 'place')
    if (placeIndex !== -1 && pathParts[placeIndex + 1]) {
      const locationPart = decodeURIComponent(pathParts[placeIndex + 1])
      const cleanLocation = locationPart.replace(/\+/g, ' ')
      return parseLocationString(cleanLocation)
    }

    // Method 2: Look for 'q' parameter in search
    const qParam = searchParams.get('q')
    if (qParam) {
      const cleanLocation = decodeURIComponent(qParam).replace(/\+/g, ' ')
      return parseLocationString(cleanLocation)
    }

    // Method 3: Look for 'query' parameter
    const queryParam = searchParams.get('query')
    if (queryParam) {
      const cleanLocation = decodeURIComponent(queryParam).replace(/\+/g, ' ')
      return parseLocationString(cleanLocation)
    }

    // Method 4: Extract from data parameter (for some Google Maps URLs)
    const dataParam = searchParams.get('data')
    if (dataParam) {
      // Try to extract location from data parameter
      const locationMatch = dataParam.match(/!1s([^!]+)/)
      if (locationMatch) {
        const cleanLocation = decodeURIComponent(locationMatch[1]).replace(/\+/g, ' ')
        return parseLocationString(cleanLocation)
      }
    }

    // Method 5: Look in URL fragment for location
    if (urlObj.hash) {
      const hashParts = urlObj.hash.split('/')
      for (const part of hashParts) {
        if (part.includes(',') && part.length > 5) {
          // Might be a location string
          const cleanLocation = decodeURIComponent(part).replace(/\+/g, ' ')
          if (!cleanLocation.match(/^[\d\.,\-]+$/)) { // Not just coordinates
            return parseLocationString(cleanLocation)
          }
        }
      }
    }

    return null
  } catch (error) {
    console.error('Error extracting location from URL path:', error)
    return null
  }
}

/**
 * Handle shortened URLs by trying to resolve them or providing guidance
 */
function handleShortenedUrl(url: string): LocationInfo | null {
  // This function is called when we detect a shortened URL
  // The main extraction function will try to resolve it first
  // If we reach here, resolution failed, so return null
  return null
}

/**
 * Fallback search when URL resolution fails
 */
async function searchLocationFallback(url: string): Promise<LocationInfo | null> {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
  if (!apiKey) {
    return null
  }

  try {
    // For shortened URLs, we can try to extract any text that might be a location
    // This is a last resort when URL resolution fails

    // Extract the short code from the URL (e.g., "v6dC7M7DmDXF2PoN8" from "https://maps.app.goo.gl/v6dC7M7DmDXF2PoN8")
    const shortCode = url.split('/').pop()

    if (shortCode && shortCode.length > 5) {
      // We can't really search by short code, so return null
      // The user will need to enter location manually
      return null
    }

    return null
  } catch (error) {
    console.error('Error in search fallback:', error)
    return null
  }
}

/**
 * Parse a location string into place name and city
 */
function parseLocationString(locationString: string): LocationInfo {
  // Clean up the location string
  let cleanLocation = locationString
    .replace(/\+/g, ' ')
    .replace(/@.*$/, '') // Remove coordinates at the end
    .trim()

  // Try to split into place name and city
  const parts = cleanLocation.split(',').map(part => part.trim()).filter(part => part.length > 0)

  if (parts.length >= 2) {
    // If we have multiple parts, assume first is place name, second is city
    const placeName = parts[0]
    const city = parts[1]
    return {
      placeName,
      city,
      formattedLocation: `${placeName}, ${city}`
    }
  } else if (parts.length === 1) {
    // Single part - could be just place name or just city
    const singlePart = parts[0]

    // If it looks like a business name (contains common business words), treat as place name
    const businessKeywords = ['restaurant', 'hotel', 'mall', 'center', 'centre', 'store', 'shop', 'cafe', 'bar', 'hospital', 'school', 'university', 'park', 'temple', 'church', 'mosque', 'station', 'airport']
    const isLikelyBusiness = businessKeywords.some(keyword =>
      singlePart.toLowerCase().includes(keyword)
    )

    if (isLikelyBusiness) {
      return {
        placeName: singlePart,
        city: '',
        formattedLocation: singlePart
      }
    } else {
      // Treat as city
      return {
        placeName: '',
        city: singlePart,
        formattedLocation: singlePart
      }
    }
  }

  return {
    placeName: '',
    city: '',
    formattedLocation: cleanLocation
  }
}

/**
 * Search for places using our API route
 */
export async function searchPlaces(query: string): Promise<PlaceSearchResult[]> {
  if (!query || query.trim().length < 2) {
    return []
  }

  try {
    console.log('Searching places for query:', query)
    const response = await fetch(`/api/places/search?query=${encodeURIComponent(query)}`)

    console.log('Places search response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
      console.error('Places API error:', response.status, errorData)
      throw new Error(`Places API error: ${response.status} - ${errorData.error || 'Unknown error'}`)
    }

    const data = await response.json()
    console.log('Places search results:', data.predictions?.length || 0, 'predictions')

    if (data.predictions) {
      return data.predictions
    }

    return []
  } catch (error) {
    console.error('Error searching places:', error)
    // Re-throw the error so the component can handle it
    throw error
  }
}

/**
 * Get place details by place ID using our API route
 */
export async function getPlaceDetails(placeId: string): Promise<LocationInfo | null> {
  try {
    const response = await fetch(`/api/places/details?place_id=${encodeURIComponent(placeId)}`)

    if (!response.ok) {
      throw new Error(`Places API error: ${response.status}`)
    }

    const data = await response.json()

    return {
      placeName: data.placeName,
      city: data.city,
      formattedLocation: data.formattedLocation,
      coordinates: data.coordinates
    }
  } catch (error) {
    console.error('Error fetching place details:', error)
    return null
  }
}

/**
 * Interface for place search results
 */
export interface PlaceSearchResult {
  place_id: string
  description: string
  structured_formatting: {
    main_text: string
    secondary_text: string
  }
  types: string[]
}

/**
 * Debounced function to extract location (to avoid too many API calls)
 */
export function createDebouncedLocationExtractor(delay: number = 1000) {
  let timeoutId: NodeJS.Timeout | null = null

  return (url: string, callback: (location: LocationInfo | null) => void) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(async () => {
      const location = await extractLocationFromMapsUrl(url)
      callback(location)
    }, delay)
  }
}

/**
 * Debounced function to search places (to avoid too many API calls)
 */
export function createDebouncedPlaceSearch(delay: number = 300) {
  let timeoutId: NodeJS.Timeout | null = null

  return (query: string, callback: (results: PlaceSearchResult[], error?: string) => void) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(async () => {
      try {
        const results = await searchPlaces(query)
        callback(results)
      } catch (error) {
        console.error('Debounced search error:', error)
        callback([], error instanceof Error ? error.message : 'Search failed')
      }
    }, delay)
  }
}

/**
 * Geocode a location string to get coordinates
 */
export async function geocodeLocation(locationString: string): Promise<{ lat: number; lng: number } | null> {
  try {
    console.log(`🌍 Geocoding location: "${locationString}"`);
    
    // Use the existing searchPlaces function to find the location
    const places = await searchPlaces(locationString);
    
    if (places.length === 0) {
      console.log(`❌ No places found for: "${locationString}"`);
      return null;
    }
    
    // Get details for the first (most relevant) place
    const firstPlace = places[0];
    console.log(`📍 Found place: ${firstPlace.description}`);
    
    const placeDetails = await getPlaceDetails(firstPlace.place_id);
    
    if (placeDetails?.coordinates) {
      console.log(`✅ Coordinates found: ${placeDetails.coordinates.lat}, ${placeDetails.coordinates.lng}`);
      return placeDetails.coordinates;
    }
    
    console.log(`❌ No coordinates found for place: ${firstPlace.description}`);
    return null;
  } catch (error) {
    console.error(`❌ Error geocoding location "${locationString}":`, error);
    return null;
  }
}
