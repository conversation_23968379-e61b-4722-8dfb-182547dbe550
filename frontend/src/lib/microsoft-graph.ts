// Function to load environment variables from .env.local if not already loaded
function loadEnvIfNeeded() {
  // Only run on server side
  if (typeof window !== 'undefined') {
    return; // Skip on client side
  }

  // Check if Microsoft Graph credentials are already loaded
  if (process.env.MICROSOFT_CLIENT_ID && process.env.MICROSOFT_CLIENT_SECRET && process.env.MICROSOFT_TENANT_ID) {
    return;
  }

  // Temporarily disabled to fix chunk loading issues
  console.log('Microsoft Graph environment loading temporarily disabled');
}

// Constants for SharePoint configuration
const SHAREPOINT_SITE_URL = 'https://zn6bn.sharepoint.com/sites/files';
const DRIVE_ID = 'b!6BliBHB7rkqGeijn571F56igGQK5UQ9GkI_AIFbTSvvooGrFp94hQr6gPCGK1yYV';

// Cache for site ID and access tokens to avoid repeated API calls
let cachedSiteId: string | null = null;
let cachedAccessToken: string | null = null;
let tokenExpiry: number | null = null;

// Token refresh threshold (5 minutes before expiry)
const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000;

/**
 * Get the Site ID from SharePoint site URL
 * @param accessToken Microsoft Graph API access token
 * @returns Site ID
 */
async function getSiteId(accessToken: string): Promise<string> {
  if (cachedSiteId) {
    return cachedSiteId;
  }

  try {
    // Extract hostname and site path from the SharePoint URL
    const url = new URL(SHAREPOINT_SITE_URL);
    const hostname = url.hostname;
    const sitePath = url.pathname; // This will be '/sites/files'
    
    // Format for Microsoft Graph API: hostname:/sites/sitename:
    const siteIdentifier = `${hostname}:${sitePath}:`;
    
    console.log('Getting Site ID for:', siteIdentifier);
    
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteIdentifier}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Site ID request failed:', response.status, errorText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Site ID retrieved successfully:', data.id);
    cachedSiteId = data.id;
    return data.id;
  } catch (error) {
    console.error('Error getting Site ID:', error);
    throw new Error('Failed to get SharePoint Site ID');
  }
}

interface FolderInfo {
  id: string;
  webUrl: string;
  shareLink?: string;
}

interface ClientFolderResult {
  folder: FolderInfo;
}

interface ProjectFolderResult {
  folder: FolderInfo;
}

/**
 * Get Microsoft Graph API access token using client credentials flow
 * @returns Access token for Microsoft Graph API
 */
async function getAccessToken(): Promise<string> {
  // Check if we have a cached token that's still valid
  const now = Date.now();
  if (cachedAccessToken && tokenExpiry && (now < tokenExpiry - TOKEN_REFRESH_THRESHOLD)) {
    return cachedAccessToken;
  }

  // Ensure environment variables are loaded
  loadEnvIfNeeded();
  
  const clientId = process.env.MICROSOFT_CLIENT_ID;
  const clientSecret = process.env.MICROSOFT_CLIENT_SECRET;
  const tenantId = process.env.MICROSOFT_TENANT_ID;

  if (!clientId || !clientSecret || !tenantId) {
    throw new Error('Missing Microsoft Graph API credentials in environment variables');
  }

  try {
    const response = await fetch(
      `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          client_id: clientId,
          scope: 'https://graph.microsoft.com/.default',
          client_secret: clientSecret,
          grant_type: 'client_credentials'
        })
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    // Cache the token and its expiry time (typically 1 hour)
    cachedAccessToken = data.access_token;
    tokenExpiry = now + (data.expires_in * 1000);
    
    return data.access_token;
  } catch (error) {
    console.error('Error getting Microsoft Graph API access token:', error);
    throw new Error('Failed to authenticate with Microsoft Graph API');
  }
}

/**
 * Check if a folder exists in SharePoint
 * @param accessToken Microsoft Graph API access token
 * @param folderPath Path to check
 * @returns Folder information if exists, null otherwise
 */
async function folderExists(accessToken: string, folderPath: string): Promise<FolderInfo | null> {
  try {
    const siteId = await getSiteId(accessToken);
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/root:${folderPath}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      }
    );

    if (!response.ok) {
      // If folder doesn't exist, Graph API returns 404
      if (response.status === 404) {
        return null;
      }
      // Re-throw other errors
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      id: data.id,
      webUrl: data.webUrl
    };
  } catch (error) {
    console.error(`Error checking if folder exists at "${folderPath}":`, error);
    throw error;
  }
}

/**
 * Create a folder in SharePoint with retry logic
 * @param accessToken Microsoft Graph API access token
 * @param parentFolderId ID of parent folder
 * @param folderName Name of folder to create
 * @returns Created folder information
 */
async function createFolder(accessToken: string, parentFolderId: string, folderName: string): Promise<FolderInfo> {
  const maxRetries = 3;
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const siteId = await getSiteId(accessToken);
      const response = await fetch(
        `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${parentFolderId}/children`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: folderName,
            folder: {}
          })
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        id: data.id,
        webUrl: data.webUrl
      };
    } catch (error) {
      console.error(`Error creating folder "${folderName}" (attempt ${attempt}/${maxRetries}):`, error);
      lastError = error;
      
      // Wait before retrying (exponential backoff)
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }
  
  throw new Error(`Failed to create folder "${folderName}" after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : 'Unknown error'}`);
}

/**
 * Create a share link for a folder with "anyone with the link" permissions
 * @param accessToken Microsoft Graph API access token
 * @param folderId ID of the folder to create share link for
 * @returns Share link URL
 */
async function createShareLink(accessToken: string, folderId: string): Promise<string> {
  try {
    const siteId = await getSiteId(accessToken);
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${folderId}/createLink`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'view',
          scope: 'anonymous'
        })
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.link.webUrl;
  } catch (error) {
    console.error(`Error creating share link for folder ID "${folderId}":`, error);
    throw new Error(`Failed to create share link for folder`);
  }
}

/**
 * Get the ID of the root drive folder
 * @param accessToken Microsoft Graph API access token
 * @returns ID of the root folder
 */
async function getRootFolderId(accessToken: string): Promise<string> {
  try {
    const siteId = await getSiteId(accessToken);
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/root`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.id;
  } catch (error) {
    console.error('Error getting root folder ID:', error);
    throw new Error('Failed to get root folder ID');
  }
}

/**
 * Create a client folder in SharePoint root directory
 * @param clientId ID of the client to associate with the folder
 * @param clientCustomId Custom ID of the client (e.g., CYMCL-25-001)
 * @param clientName Name of the client
 * @returns Object containing folder information
 */
async function createClientFolder(
  clientId: string,
  clientCustomId: string,
  clientName: string
): Promise<ClientFolderResult> {
  try {
    // Get access token
    const accessToken = await getAccessToken();
    
    // Get the root folder ID
    const rootFolderId = await getRootFolderId(accessToken);
    
    // Create folder name: {ClientCustomID} {ClientName}
    const folderName = `${clientCustomId} ${clientName}`;
    
    // Check if folder already exists
    const existingFolder = await folderExists(accessToken, `/${folderName}`);
    
    let clientFolder: FolderInfo;
    if (existingFolder) {
      console.log(`Client folder "${folderName}" already exists`);
      clientFolder = existingFolder;
    } else {
      console.log(`Creating client folder "${folderName}"`);
      clientFolder = await createFolder(accessToken, rootFolderId, folderName);
    }
    
    // Create share link for the client folder
    let shareLink: string | undefined;
    try {
      shareLink = await createShareLink(accessToken, clientFolder.id);
      console.log(`Created share link for client folder: ${shareLink}`);
    } catch (error) {
      console.warn('Failed to create share link for client folder:', error);
      // Continue without share link
    }
    
    // Store folder information in the database
    await storeClientFolderInDatabase(clientId, clientFolder.id, clientFolder.webUrl, shareLink);
    
    return {
      folder: {
        ...clientFolder,
        shareLink
      }
    };
  } catch (error) {
    console.error('Error creating client folder:', error);
    throw new Error(`Failed to create client folder: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create a project folder in SharePoint
 * @param projectId ID of the project
 * @param projectCustomId Custom ID of the project
 * @param projectName Name of the project
 * @param clientId ID of the client (parent folder)
 * @returns Result containing folder information
 */
async function createProjectFolder(
  projectId: string,
  projectCustomId: string,
  projectName: string,
  clientId: string
): Promise<ProjectFolderResult> {
  try {
    // Get access token
    const accessToken = await getAccessToken();
    
    // Get client information from database using server-side client
    const { createClient } = await import('@supabase/supabase-js');
    
    // Validate environment variables with better error context
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    console.log('🔍 Environment variable check in storeProjectFolderInDatabase:', {
      supabaseUrl: supabaseUrl ? 'Present' : 'Missing',
      supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',
      nodeEnv: process.env.NODE_ENV,
      context: 'background-job'
    });
    
    if (!supabaseUrl) {
      console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');
      throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
    }
    
    if (!supabaseServiceKey) {
      console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');
      throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .select('custom_id, name')
      .eq('id', clientId)
      .single();
    
    if (clientError || !client) {
      throw new Error(`Failed to get client information: ${clientError?.message || 'Client not found'}`);
    }
    
    // Create client folder name and project folder name
    const clientFolderName = `${client.custom_id} ${client.name}`;
    const projectFolderName = `${projectCustomId} ${projectName}`;
    
    // Check if client folder exists, create if not
    const clientFolderPath = `/${clientFolderName}`;
    let clientFolder = await folderExists(accessToken, clientFolderPath);
    
    if (!clientFolder) {
      const rootFolderId = await getRootFolderId(accessToken);
      clientFolder = await createFolder(accessToken, rootFolderId, clientFolderName);
    }
    
    // Check if project folder already exists
    const projectFolderPath = `/${clientFolderName}/${projectFolderName}`;
    const existingProjectFolder = await folderExists(accessToken, projectFolderPath);
    
    let projectFolder: FolderInfo;
    if (existingProjectFolder) {
      console.log(`Project folder "${projectFolderName}" already exists`);
      projectFolder = existingProjectFolder;
    } else {
      console.log(`Creating project folder "${projectFolderName}"`);
      projectFolder = await createFolder(accessToken, clientFolder.id, projectFolderName);
    }
    
    // Create share link for the project folder
    let shareLink: string | undefined;
    try {
      shareLink = await createShareLink(accessToken, projectFolder.id);
      console.log(`Created share link for project folder: ${shareLink}`);
    } catch (error) {
      console.warn('Failed to create share link for project folder:', error);
      // Continue without share link
    }
    
    // Store folder information in the database
    await storeProjectFolderInDatabase(projectId, projectFolder.id, projectFolder.webUrl, shareLink);
    
    return {
      folder: {
        ...projectFolder,
        shareLink
      }
    };
  } catch (error) {
    console.error('Error creating project folder:', error);
    throw new Error(`Failed to create project folder: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create a schedule folder inside the project folder
 * @param scheduleId ID of the schedule to associate with the folder
 * @param scheduleCustomId Custom ID of the schedule (e.g., CYM-25-005)
 * @param scheduleDate Date of the schedule (YYYY-MM-DD format)
 * @param projectCustomId Custom ID of the project
 * @param projectName Name of the project
 * @param clientCustomId Custom ID of the client
 * @param clientName Name of the client
 * @returns Object containing folder information for the main schedule folder
 */
async function createScheduleFolder(
  scheduleId: string,
  scheduleCustomId: string,
  scheduleDate: string,
  projectCustomId: string,
  projectName: string,
  clientCustomId: string,
  clientName: string
): Promise<{ folder: FolderInfo }> {
  try {
    // Get access token
    const accessToken = await getAccessToken();
    
    // Create folder names
    const clientFolderName = `${clientCustomId} ${clientName}`;
    const projectFolderName = `${projectCustomId} ${projectName}`;
    const scheduleFolderName = `${scheduleCustomId} ${scheduleDate}`;
    
    // Ensure client folder exists
    const clientFolderPath = `/${clientFolderName}`;
    let clientFolder = await folderExists(accessToken, clientFolderPath);
    
    if (!clientFolder) {
      const rootFolderId = await getRootFolderId(accessToken);
      clientFolder = await createFolder(accessToken, rootFolderId, clientFolderName);
    }
    
    // Ensure project folder exists
    const projectFolderPath = `/${clientFolderName}/${projectFolderName}`;
    let projectFolder = await folderExists(accessToken, projectFolderPath);
    
    if (!projectFolder) {
      projectFolder = await createFolder(accessToken, clientFolder.id, projectFolderName);
    }
    
    // Check if schedule folder already exists
    const scheduleFolderPath = `/${clientFolderName}/${projectFolderName}/${scheduleFolderName}`;
    const existingScheduleFolder = await folderExists(accessToken, scheduleFolderPath);
    
    let scheduleFolder: FolderInfo;
    if (existingScheduleFolder) {
      console.log(`Schedule folder "${scheduleFolderName}" already exists`);
      scheduleFolder = existingScheduleFolder;
    } else {
      console.log(`Creating schedule folder "${scheduleFolderName}"`);
      scheduleFolder = await createFolder(accessToken, projectFolder.id, scheduleFolderName);
    }
    
    // Create Raw and Output subfolders inside the schedule folder
    try {
      console.log(`Creating "Raw" subfolder in schedule folder "${scheduleFolderName}"`);
      await createFolder(accessToken, scheduleFolder.id, 'Raw');
      console.log(`Successfully created "Raw" subfolder`);
      
      console.log(`Creating "Output" subfolder in schedule folder "${scheduleFolderName}"`);
      await createFolder(accessToken, scheduleFolder.id, 'Output');
      console.log(`Successfully created "Output" subfolder`);
    } catch (subfolderError) {
      console.warn('Failed to create subfolders (Raw/Output) in schedule folder:', subfolderError);
      // Don't fail the entire operation if subfolder creation fails
    }
    
    // Create share link for the schedule folder
    let scheduleShareLink: string | undefined;
    
    try {
      scheduleShareLink = await createShareLink(accessToken, scheduleFolder.id);
      console.log(`Created share link for schedule folder: ${scheduleShareLink}`);
    } catch (error) {
      console.warn('Failed to create share link for schedule folder:', error);
    }
    
    // Store folder information in the database (only main schedule folder, not subfolders)
    await storeScheduleFolderLinksInDatabase(
      scheduleId,
      scheduleFolder.id,
      scheduleFolder.webUrl,
      scheduleShareLink
    );
    
    return {
      folder: {
        ...scheduleFolder,
        shareLink: scheduleShareLink
      }
    };
  } catch (error) {
    console.error('Error creating schedule folder:', error);
    throw new Error(`Failed to create schedule folder: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create folder structure in SharePoint for a project and store links in database
 * @param scheduleId ID of the schedule to associate with the folders
 * @param clientName Name of the client
 * @param projectName Name of the project
 * @param date Date of the project (YYYY-MM-DD format)
 * @returns Object containing folder information for the main schedule folder
 */
async function createProjectFolderStructure(
  scheduleId: string,
  clientName: string,
  projectName: string,
  date: string
): Promise<FolderInfo> {
  try {
    // Get access token
    const accessToken = await getAccessToken();
    
    // Get the Clients folder ID (root for our structure)
    let currentFolderId = await getClientsFolderId(accessToken);
    
    // Build the folder path: /Clients/<clientName>/<projectName>/<date>/
    const folderPath = `/${clientName}/${projectName}/${date}`;
    const fullPath = `/Clients${folderPath}`;
    
    // Create each folder in the path if it doesn't exist
    const pathParts = [clientName, projectName, date];
    for (const part of pathParts) {
      const currentPath = `/Clients/${pathParts.slice(0, pathParts.indexOf(part) + 1).join('/')}`;
      const existingFolder = await folderExists(accessToken, currentPath);
      
      if (existingFolder) {
        console.log(`Folder "${part}" already exists at ${currentPath}`);
        currentFolderId = existingFolder.id;
      } else {
        console.log(`Creating folder "${part}" at ${currentPath}`);
        const newFolder = await createFolder(accessToken, currentFolderId, part);
        currentFolderId = newFolder.id;
      }
    }
    
    // Create share link for the main folder
    const shareLink = await createShareLink(accessToken, currentFolderId);
    
    // Get the main folder information
    const mainFolder: FolderInfo = {
      id: currentFolderId,
      webUrl: fullPath, // This will be updated with actual webUrl from API
      shareLink: shareLink
    };
    
    // Store folder information in the database
    await storeScheduleFolderLinksInDatabase(
      scheduleId,
      mainFolder.id,
      mainFolder.webUrl,
      mainFolder.shareLink
    );
    
    return mainFolder;
  } catch (error) {
    console.error('Error creating project folder structure:', error);
    throw new Error(`Failed to create folder structure: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create a subfolder if it doesn't already exist
 * @param accessToken Microsoft Graph API access token
 * @param parentFolderId ID of parent folder
 * @param folderName Name of folder to create
 * @returns Folder information
 */
async function createSubfolderIfNotExists(
  accessToken: string,
  parentFolderId: string,
  folderName: string
): Promise<FolderInfo> {
  try {
    // Try to get the folder first
    const siteId = await getSiteId(accessToken);
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${DRIVE_ID}/items/${parentFolderId}/children?$filter=name eq '${folderName}'`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      }
    );

    if (!response.ok) {
      // If we get an error other than 404, re-throw it
      if (response.status !== 404) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      // If 404, continue to create the folder
    } else {
      const data = await response.json();
      
      // Check if folder exists in response
      if (data.value && data.value.length > 0) {
        const existingFolder = data.value[0];
        if (existingFolder.folder) {
          console.log(`Folder "${folderName}" already exists`);
          return {
            id: existingFolder.id,
            webUrl: existingFolder.webUrl
          };
        }
      }
    }

    // Folder doesn't exist, create it
    console.log(`Creating folder "${folderName}"`);
    return await createFolder(accessToken, parentFolderId, folderName);
  } catch (error) {
    console.error(`Error checking/creating folder "${folderName}":`, error);
    // Try to create the folder directly
    return await createFolder(accessToken, parentFolderId, folderName);
  }
}

/**
 * Store client folder information in the database
 * @param clientId ID of the client to update
 * @param folderId SharePoint ID of the client folder
 * @param folderUrl SharePoint URL of the client folder
 * @param shareLink Public share link for the client folder (optional)
 */
async function storeClientFolderInDatabase(
  clientId: string,
  folderId: string,
  folderUrl: string,
  shareLink?: string
): Promise<void> {
  try {
    // Use server-side Supabase client
    const { createClient } = await import('@supabase/supabase-js');
    
    // Validate environment variables with better error context
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    console.log('🔍 Environment variable check in storeClientFolderInDatabase:', {
      supabaseUrl: supabaseUrl ? 'Present' : 'Missing',
      supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',
      nodeEnv: process.env.NODE_ENV,
      context: 'background-job'
    });
    
    if (!supabaseUrl) {
      console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');
      throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
    }
    
    if (!supabaseServiceKey) {
      console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');
      throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    const updateData: any = {
      sharepoint_folder_id: folderId,
      sharepoint_folder_url: folderUrl
    };
    
    if (shareLink) {
      updateData.sharepoint_share_link = shareLink;
    }
    
    const { error } = await supabase
      .from('clients')
      .update(updateData)
      .eq('id', clientId);
    
    if (error) {
      console.error('Error storing client folder in database:', error);
      throw new Error('Failed to store client folder in database');
    }
    
    console.log('Successfully stored client folder in database for client:', clientId);
  } catch (error) {
    console.error('Error in storeClientFolderInDatabase:', error);
    throw error;
  }
}

/**
 * Store project folder information in the database
 * @param projectId ID of the project to update
 * @param folderId SharePoint ID of the project folder
 * @param folderUrl SharePoint URL of the project folder
 * @param shareLink Public share link for the project folder (optional)
 */
async function storeProjectFolderInDatabase(
  projectId: string,
  folderId: string,
  folderUrl: string,
  shareLink?: string
): Promise<void> {
  try {
    // Use server-side Supabase client
    const { createClient } = await import('@supabase/supabase-js');
    
    // Validate environment variables with better error context
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    console.log('🔍 Environment variable check in getScheduleFolderLinks:', {
      supabaseUrl: supabaseUrl ? 'Present' : 'Missing',
      supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',
      nodeEnv: process.env.NODE_ENV,
      context: 'background-job'
    });
    
    if (!supabaseUrl) {
      console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');
      throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
    }
    
    if (!supabaseServiceKey) {
      console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');
      throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    const updateData: any = {
      sharepoint_folder_id: folderId,
      sharepoint_folder_url: folderUrl
    };
    
    if (shareLink) {
      updateData.sharepoint_share_link = shareLink;
    }
    
    const { error } = await supabase
      .from('projects')
      .update(updateData)
      .eq('id', projectId);
    
    if (error) {
      console.error('Error storing project folder in database:', error);
      throw new Error('Failed to store project folder in database');
    }
    
    console.log('Successfully stored project folder in database for project:', projectId);
  } catch (error) {
    console.error('Error in storeProjectFolderInDatabase:', error);
    throw error;
  }
}

/**
 * Store schedule folder links in the schedules table
 * @param scheduleId ID of the schedule
 * @param scheduleFolderId SharePoint ID of the schedule folder
 * @param scheduleFolderUrl SharePoint URL of the schedule folder
 * @param scheduleShareLink Public share link for the schedule folder
 */
async function storeScheduleFolderLinksInDatabase(
  scheduleId: string,
  scheduleFolderId: string,
  scheduleFolderUrl: string,
  scheduleShareLink?: string
): Promise<void> {
  try {
    // Use server-side Supabase client
    const { createClient } = await import('@supabase/supabase-js');
    
    // Validate environment variables with better error context
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    console.log('🔍 Environment variable check in storeScheduleFolderLinksInDatabase:', {
      supabaseUrl: supabaseUrl ? 'Present' : 'Missing',
      supabaseServiceKey: supabaseServiceKey ? 'Present' : 'Missing',
      nodeEnv: process.env.NODE_ENV,
      context: 'background-job'
    });
    
    if (!supabaseUrl) {
      console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in background job context');
      throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
    }
    
    if (!supabaseServiceKey) {
      console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in background job context');
      throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Prepare update data for schedules table
    const updateData: any = {
      sharepoint_folder_id: scheduleFolderId,
      sharepoint_folder_url: scheduleFolderUrl
    };
    
    if (scheduleShareLink) {
      updateData.sharepoint_share_link = scheduleShareLink;
    }
    
    // Update the schedules table
    const { error } = await supabase
      .from('schedules')
      .update(updateData)
      .eq('id', scheduleId);
    
    if (error) {
      console.error('Error storing schedule folder links in database:', error);
      throw new Error('Failed to store schedule folder links in database');
    }
    
    console.log('Successfully stored schedule folder links in schedules table for schedule:', scheduleId);
  } catch (error) {
    console.error('Error in storeScheduleFolderLinksInDatabase:', error);
    throw error;
  }
}



/**
 * Get schedule folder links from the database
 * @param scheduleId ID of the schedule
 * @returns Object containing folder information for the main schedule folder, or null if not found
 */
export async function getScheduleFolderLinks(scheduleId: string): Promise<FolderInfo | null> {
  // Skip on client side
  if (typeof window !== 'undefined') {
    console.log('Microsoft Graph functions disabled on client side');
    return null;
  }

  try {
    // Use server-side Supabase client
    const { createClient } = await import('@supabase/supabase-js');
    
    // Validate environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl) {
      throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
    }
    
    if (!supabaseServiceKey) {
      throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    const { data, error } = await supabase
      .from('schedules')
      .select(`
        sharepoint_folder_id,
        sharepoint_folder_url,
        sharepoint_share_link
      `)
      .eq('id', scheduleId)
      .single();
    
    if (error) {
      console.error('Error getting schedule folder links from database:', error);
      return null;
    }
    
    if (!data || !data.sharepoint_folder_id || !data.sharepoint_folder_url) {
      console.log('No schedule folder links found for schedule:', scheduleId);
      return null;
    }
    
    const result: FolderInfo = {
      id: data.sharepoint_folder_id,
      webUrl: data.sharepoint_folder_url,
      shareLink: data.sharepoint_share_link
    };
    
    return result;
  } catch (error) {
    console.error('Error in getScheduleFolderLinks:', error);
    return null;
  }
}



/**
 * Get the ID of the Clients folder (legacy function for compatibility)
 * @param accessToken Microsoft Graph API access token
 * @returns ID of the Clients folder
 */
async function getClientsFolderId(accessToken: string): Promise<string> {
  // For backward compatibility, return the root folder ID
  return await getRootFolderId(accessToken);
}

export {
  getAccessToken as getMicrosoftGraphAccessToken,
  folderExists as checkIfFolderExists,
  createFolder,
  getClientsFolderId,
  getRootFolderId,
  createProjectFolderStructure,
  createSubfolderIfNotExists,
  storeClientFolderInDatabase,
  storeProjectFolderInDatabase,
  createClientFolder,
  createProjectFolder,
  createScheduleFolder
};