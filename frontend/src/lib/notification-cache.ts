/**
 * Offline Notification Caching System
 * Uses IndexedDB to cache notifications for offline access
 */

import type { Notification } from '@/types/notifications'

const DB_NAME = 'CymaticsNotifications'
const DB_VERSION = 1
const STORE_NAME = 'notifications'
const MAX_CACHED_NOTIFICATIONS = 20

interface CachedNotification extends Notification {
  cached_at: string
}

class NotificationCache {
  private db: IDBDatabase | null = null
  private initPromise: Promise<void> | null = null

  /**
   * Initialize the IndexedDB database
   */
  private async init(): Promise<void> {
    if (this.db) return

    if (this.initPromise) {
      await this.initPromise
      return
    }

    this.initPromise = new Promise((resolve, reject) => {
      if (typeof window === 'undefined') {
        // Server-side rendering - skip initialization
        resolve()
        return
      }

      const request = indexedDB.open(DB_NAME, DB_VERSION)

      request.onerror = () => {
        console.error('Failed to open IndexedDB:', request.error)
        reject(request.error)
      }

      request.onsuccess = () => {
        this.db = request.result
        console.log('✅ Notification cache initialized')
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        // Create notifications store
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' })
          store.createIndex('created_at', 'created_at', { unique: false })
          store.createIndex('cached_at', 'cached_at', { unique: false })
          store.createIndex('user_id', 'user_id', { unique: false })
          store.createIndex('read', 'read', { unique: false })
          console.log('📦 Created notifications store in IndexedDB')
        }
      }
    })

    await this.initPromise
  }

  /**
   * Cache notifications (keep only the latest 20)
   */
  async cacheNotifications(notifications: Notification[]): Promise<void> {
    try {
      await this.init()
      if (!this.db) return

      const transaction = this.db.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)

      // Add cached_at timestamp to notifications
      const cachedNotifications: CachedNotification[] = notifications.map(notification => ({
        ...notification,
        cached_at: new Date().toISOString()
      }))

      // Clear old notifications first
      await new Promise<void>((resolve, reject) => {
        const clearRequest = store.clear()
        clearRequest.onsuccess = () => resolve()
        clearRequest.onerror = () => reject(clearRequest.error)
      })

      // Add new notifications (limit to MAX_CACHED_NOTIFICATIONS)
      const notificationsToCache = cachedNotifications
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, MAX_CACHED_NOTIFICATIONS)

      for (const notification of notificationsToCache) {
        store.put(notification)
      }

      await new Promise<void>((resolve, reject) => {
        transaction.oncomplete = () => {
          console.log(`📦 Cached ${notificationsToCache.length} notifications`)
          resolve()
        }
        transaction.onerror = () => reject(transaction.error)
      })

    } catch (error) {
      console.error('❌ Error caching notifications:', error)
    }
  }

  /**
   * Get cached notifications
   */
  async getCachedNotifications(): Promise<Notification[]> {
    try {
      await this.init()
      if (!this.db) return []

      const transaction = this.db.transaction([STORE_NAME], 'readonly')
      const store = transaction.objectStore(STORE_NAME)
      const index = store.index('created_at')

      return new Promise<Notification[]>((resolve, reject) => {
        const request = index.getAll()
        
        request.onsuccess = () => {
          const notifications = request.result
            .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
            .map(({ cached_at, ...notification }) => notification) // Remove cached_at field
          
          console.log(`📦 Retrieved ${notifications.length} cached notifications`)
          resolve(notifications)
        }
        
        request.onerror = () => {
          console.error('❌ Error retrieving cached notifications:', request.error)
          reject(request.error)
        }
      })

    } catch (error) {
      console.error('❌ Error getting cached notifications:', error)
      return []
    }
  }

  /**
   * Update a specific notification in cache
   */
  async updateCachedNotification(notificationId: string, updates: Partial<Notification>): Promise<void> {
    try {
      await this.init()
      if (!this.db) return

      const transaction = this.db.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)

      // Get the existing notification
      const getRequest = store.get(notificationId)
      
      await new Promise<void>((resolve, reject) => {
        getRequest.onsuccess = () => {
          const existingNotification = getRequest.result
          if (existingNotification) {
            // Update the notification
            const updatedNotification = {
              ...existingNotification,
              ...updates,
              cached_at: new Date().toISOString()
            }
            
            const putRequest = store.put(updatedNotification)
            putRequest.onsuccess = () => {
              console.log(`📦 Updated cached notification: ${notificationId}`)
              resolve()
            }
            putRequest.onerror = () => reject(putRequest.error)
          } else {
            resolve() // Notification not found in cache, that's okay
          }
        }
        
        getRequest.onerror = () => reject(getRequest.error)
      })

    } catch (error) {
      console.error('❌ Error updating cached notification:', error)
    }
  }

  /**
   * Remove a notification from cache
   */
  async removeCachedNotification(notificationId: string): Promise<void> {
    try {
      await this.init()
      if (!this.db) return

      const transaction = this.db.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)

      await new Promise<void>((resolve, reject) => {
        const request = store.delete(notificationId)
        request.onsuccess = () => {
          console.log(`📦 Removed cached notification: ${notificationId}`)
          resolve()
        }
        request.onerror = () => reject(request.error)
      })

    } catch (error) {
      console.error('❌ Error removing cached notification:', error)
    }
  }

  /**
   * Clear all cached notifications
   */
  async clearCache(): Promise<void> {
    try {
      await this.init()
      if (!this.db) return

      const transaction = this.db.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)

      await new Promise<void>((resolve, reject) => {
        const request = store.clear()
        request.onsuccess = () => {
          console.log('📦 Cleared notification cache')
          resolve()
        }
        request.onerror = () => reject(request.error)
      })

    } catch (error) {
      console.error('❌ Error clearing notification cache:', error)
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{ count: number; lastUpdated?: string }> {
    try {
      await this.init()
      if (!this.db) return { count: 0 }

      const transaction = this.db.transaction([STORE_NAME], 'readonly')
      const store = transaction.objectStore(STORE_NAME)

      return new Promise((resolve, reject) => {
        const countRequest = store.count()
        const getAllRequest = store.getAll()

        let count = 0
        let lastUpdated: string | undefined

        countRequest.onsuccess = () => {
          count = countRequest.result
        }

        getAllRequest.onsuccess = () => {
          const notifications = getAllRequest.result as CachedNotification[]
          if (notifications.length > 0) {
            lastUpdated = notifications
              .sort((a, b) => new Date(b.cached_at).getTime() - new Date(a.cached_at).getTime())[0]
              .cached_at
          }
          
          resolve({ count, lastUpdated })
        }

        getAllRequest.onerror = () => reject(getAllRequest.error)
      })

    } catch (error) {
      console.error('❌ Error getting cache stats:', error)
      return { count: 0 }
    }
  }
}

// Export singleton instance
export const notificationCache = new NotificationCache()

// Utility functions for easier usage
export const cacheNotifications = (notifications: Notification[]) => 
  notificationCache.cacheNotifications(notifications)

export const getCachedNotifications = () => 
  notificationCache.getCachedNotifications()

export const updateCachedNotification = (id: string, updates: Partial<Notification>) => 
  notificationCache.updateCachedNotification(id, updates)

export const removeCachedNotification = (id: string) => 
  notificationCache.removeCachedNotification(id)

export const clearNotificationCache = () => 
  notificationCache.clearCache()

export const getNotificationCacheStats = () => 
  notificationCache.getCacheStats()
