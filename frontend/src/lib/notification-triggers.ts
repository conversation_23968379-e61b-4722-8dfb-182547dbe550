/**
 * Notification Triggers System
 * Handles automated notification generation for payments, schedules, and tasks
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Use service role key for server-side operations
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export interface NotificationTriggerResult {
  success: boolean
  message: string
  count: number
  errors?: string[]
}

export interface NotificationTriggerOptions {
  dryRun?: boolean
  limit?: number
}

import type { Task } from '@/types'

/**
 * Main function to process notification triggers
 */
export async function processNotificationTriggers(
  triggerType: 'payment_overdue' | 'schedule_reminders' | 'task_updates' | 'all',
  options: NotificationTriggerOptions = {}
): Promise<NotificationTriggerResult> {
  console.log(`🔔 Processing notification triggers: ${triggerType}`)
  
  try {
    let totalCount = 0
    const errors: string[] = []

    switch (triggerType) {
      case 'payment_overdue':
        const paymentResult = await processPaymentOverdueNotifications(options)
        totalCount += paymentResult.count
        if (paymentResult.errors) errors.push(...paymentResult.errors)
        break

      case 'schedule_reminders':
        const scheduleResult = await processScheduleReminderNotifications(options)
        totalCount += scheduleResult.count
        if (scheduleResult.errors) errors.push(...scheduleResult.errors)
        break

      case 'task_updates':
        // Task updates are handled in real-time via triggers, not batch processing
        console.log('ℹ️ Task updates are handled in real-time, skipping batch processing')
        break

      case 'all':
        const allResults = await Promise.allSettled([
          processPaymentOverdueNotifications(options),
          processScheduleReminderNotifications(options)
        ])
        
        allResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            totalCount += result.value.count
            if (result.value.errors) errors.push(...result.value.errors)
          } else {
            errors.push(`Trigger ${index} failed: ${result.reason}`)
          }
        })
        break

      default:
        throw new Error(`Unknown trigger type: ${triggerType}`)
    }

    return {
      success: errors.length === 0,
      message: `Processed ${triggerType} triggers, created ${totalCount} notifications`,
      count: totalCount,
      errors: errors.length > 0 ? errors : undefined
    }

  } catch (error) {
    console.error('❌ Error processing notification triggers:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      count: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    }
  }
}

/**
 * Process payment overdue notifications
 */
async function processPaymentOverdueNotifications(
  options: NotificationTriggerOptions = {}
): Promise<NotificationTriggerResult> {
  try {
    console.log('💰 Processing payment overdue notifications...')

    if (options.dryRun) {
      console.log('🔍 Dry run mode - no notifications will be created')
    }

    // Call the database function to create payment overdue notifications
    const { data, error } = await supabase.rpc('create_payment_overdue_notifications')

    if (error) {
      throw error
    }

    const count = data || 0
    console.log(`✅ Created ${count} payment overdue notifications`)

    return {
      success: true,
      message: `Created ${count} payment overdue notifications`,
      count
    }

  } catch (error) {
    console.error('❌ Error processing payment overdue notifications:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      count: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    }
  }
}

/**
 * Process schedule reminder notifications
 */
async function processScheduleReminderNotifications(
  options: NotificationTriggerOptions = {}
): Promise<NotificationTriggerResult> {
  try {
    console.log('📅 Processing schedule reminder notifications...')

    if (options.dryRun) {
      console.log('🔍 Dry run mode - no notifications will be created')
    }

    // Call the database function to create schedule reminder notifications
    const { data, error } = await supabase.rpc('create_schedule_reminder_notifications')

    if (error) {
      throw error
    }

    const count = data || 0
    console.log(`✅ Created ${count} schedule reminder notifications`)

    return {
      success: true,
      message: `Created ${count} schedule reminder notifications`,
      count
    }

  } catch (error) {
    console.error('❌ Error processing schedule reminder notifications:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      count: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    }
  }
}

/**
 * Create task update notification (called from task update triggers)
 */
export async function createTaskUpdateNotification(
  taskId: string,
  oldStatus: Task['status'],
  newStatus: Task['status'],
  updatedByUserId: string
): Promise<NotificationTriggerResult> {
  try {
    console.log(`📋 Creating task update notification for task: ${taskId}`)

    // Call the database function to create task update notification
    const { error } = await supabase.rpc('create_task_update_notification', {
      task_id_param: taskId,
      old_status: oldStatus,
      new_status: newStatus,
      updated_by_user_id: updatedByUserId
    })

    if (error) {
      throw error
    }

    console.log(`✅ Created task update notification for task: ${taskId}`)

    return {
      success: true,
      message: `Created task update notification`,
      count: 1
    }

  } catch (error) {
    console.error('❌ Error creating task update notification:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      count: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    }
  }
}

/**
 * Queue notification triggers as background job
 */
export function queueNotificationTriggers(
  triggerType: 'payment_overdue' | 'schedule_reminders' | 'all',
  options: NotificationTriggerOptions = {}
): string {
  const { getBackgroundJobQueue } = require('./background-jobs')
  const queue = getBackgroundJobQueue()
  
  const jobId = queue.addJob('notification_triggers', 'notification', 'system', {
    triggerType,
    options
  })
  
  console.log(`📋 Notification triggers queued: ${triggerType} with job ID: ${jobId}`)
  return jobId
}

/**
 * Schedule periodic notification triggers
 * This should be called from a cron job or scheduled task
 */
export function scheduleNotificationTriggers() {
  console.log('⏰ Scheduling periodic notification triggers...')
  
  // Schedule payment overdue checks (daily at 9 AM)
  const paymentJobId = queueNotificationTriggers('payment_overdue')
  console.log(`📅 Scheduled payment overdue check: ${paymentJobId}`)
  
  // Schedule schedule reminder checks (every 30 minutes)
  const scheduleJobId = queueNotificationTriggers('schedule_reminders')
  console.log(`📅 Scheduled schedule reminder check: ${scheduleJobId}`)
  
  return {
    paymentJobId,
    scheduleJobId
  }
}
