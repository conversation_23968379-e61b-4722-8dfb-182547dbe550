import type { Task } from '@/types'

interface TaskUpdatePayload {
  status?: Task['status']
  started_at?: string
  completed_at?: string
  priority?: Task['priority']
  due_date?: string | null
  assigned_to?: string | null
  title?: string
  description?: string
}

interface FastTaskResponse {
  id: string
  status: Task['status']
  title: string
  updated_at: string
  project_id?: string
  shoot_id?: string
}

class OptimizedTaskAPI {
  private baseUrl = '/api/tasks'
  private requestCache = new Map<string, Promise<FastTaskResponse>>()
  private cacheTimeout = 1000 // 1 second cache to prevent duplicate requests

  /**
   * Ultra-fast task status update with aggressive optimizations
   */
  async updateStatus(
    taskId: string,
    status: Task['status'],
    additionalUpdates: Partial<TaskUpdatePayload> = {}
  ): Promise<FastTaskResponse> {
    // Check if identical request is already in flight
    const existingRequest = this.requestCache.get(taskId)
    if (existingRequest) {
      console.log('🚀 Reusing in-flight request for task:', taskId)
      return existingRequest
    }

    const payload: TaskUpdatePayload = {
      status,
      ...additionalUpdates
    }

    // Add automatic timestamps for status transitions
    const now = new Date().toISOString()
    if (status === 'in_progress') {
      payload.started_at = now
    } else if (status === 'completed') {
      payload.completed_at = now
    }

    // Use the ultra-fast status endpoint for better performance
    const requestPromise = this.performStatusUpdate(taskId, payload)

    // Cache the request to prevent duplicates
    this.requestCache.set(taskId, requestPromise)

    // Clear cache after timeout
    setTimeout(() => {
      this.requestCache.delete(taskId)
    }, this.cacheTimeout)

    try {
      const result = await requestPromise
      return result
    } catch (error) {
      // Remove failed request from cache immediately
      this.requestCache.delete(taskId)
      throw error
    }
  }

  /**
   * Ultra-fast status update using dedicated endpoint
   */
  private async performStatusUpdate(
    taskId: string,
    payload: TaskUpdatePayload
  ): Promise<FastTaskResponse> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 3000) // 3s timeout for status updates

    try {
      const response = await fetch(`${this.baseUrl}/${taskId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
        cache: 'no-store'
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return result as FastTaskResponse

    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Status update timeout - please try again')
        }
        throw error
      }

      throw new Error('Network error - please check your connection')
    }
  }

  /**
   * Perform the actual HTTP request with optimizations (fallback)
   */
  private async performUpdate(
    taskId: string,
    payload: TaskUpdatePayload
  ): Promise<FastTaskResponse> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5s timeout

    try {
      const response = await fetch(`${this.baseUrl}/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          // Add cache control to prevent browser caching of updates
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
        // Optimize for speed over caching
        cache: 'no-store'
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return result as FastTaskResponse

    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout - please try again')
        }
        throw error
      }
      
      throw new Error('Network error - please check your connection')
    }
  }

  /**
   * Batch update multiple tasks in parallel
   */
  async updateMultiple(
    updates: Array<{
      taskId: string
      status: Task['status']
      additionalUpdates?: Partial<TaskUpdatePayload>
    }>
  ): Promise<FastTaskResponse[]> {
    console.log('🚀 Batch updating', updates.length, 'tasks')

    const promises = updates.map(({ taskId, status, additionalUpdates = {} }) =>
      this.updateStatus(taskId, status, additionalUpdates)
    )

    // Use Promise.allSettled to handle partial failures gracefully
    const results = await Promise.allSettled(promises)
    
    const successful: FastTaskResponse[] = []
    const failed: Array<{ taskId: string; error: Error }> = []

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successful.push(result.value)
      } else {
        failed.push({
          taskId: updates[index].taskId,
          error: result.reason
        })
      }
    })

    if (failed.length > 0) {
      console.warn('⚠️ Some batch updates failed:', failed)
    }

    console.log(`✅ Batch update completed: ${successful.length} successful, ${failed.length} failed`)
    
    return successful
  }

  /**
   * Quick status check without full task data
   */
  async getStatus(taskId: string): Promise<{ status: Task['status']; updated_at: string }> {
    const response = await fetch(`${this.baseUrl}/${taskId}/status`, {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to get task status: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Preload task data for faster updates
   */
  async preloadTask(taskId: string): Promise<void> {
    try {
      // Prefetch task data to warm up the connection
      await fetch(`${this.baseUrl}/${taskId}/status`, {
        method: 'HEAD' // Just check if task exists
      })
    } catch (error) {
      // Ignore preload errors
      console.debug('Preload failed for task:', taskId)
    }
  }

  /**
   * Clear all cached requests (useful for cleanup)
   */
  clearCache(): void {
    this.requestCache.clear()
  }

  /**
   * Get cache statistics for debugging
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.requestCache.size,
      keys: Array.from(this.requestCache.keys())
    }
  }
}

// Export singleton instance
export const optimizedTaskAPI = new OptimizedTaskAPI()

// Export class for testing
export { OptimizedTaskAPI }
