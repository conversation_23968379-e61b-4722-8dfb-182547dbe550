// Performance monitoring utilities

interface PerformanceMetric {
  name: string
  duration: number
  timestamp: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private timers: Map<string, number> = new Map()

  // Start timing an operation
  startTimer(name: string): void {
    this.timers.set(name, performance.now())
  }

  // End timing and record the metric
  endTimer(name: string): number {
    const startTime = this.timers.get(name)
    if (!startTime) {
      console.warn(`Timer '${name}' was not started`)
      return 0
    }

    const duration = performance.now() - startTime
    this.timers.delete(name)

    this.metrics.push({
      name,
      duration,
      timestamp: Date.now()
    })

    // Log slow operations (> 100ms)
    if (duration > 100) {
      console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`)
    }

    return duration
  }

  // Measure a function execution time
  async measure<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.startTimer(name)
    try {
      const result = await fn()
      this.endTimer(name)
      return result
    } catch (error) {
      this.endTimer(name)
      throw error
    }
  }

  // Get performance metrics
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  // Get metrics for a specific operation
  getMetricsFor(name: string): PerformanceMetric[] {
    return this.metrics.filter(m => m.name === name)
  }

  // Get average duration for an operation
  getAverageDuration(name: string): number {
    const operationMetrics = this.getMetricsFor(name)
    if (operationMetrics.length === 0) return 0

    const total = operationMetrics.reduce((sum, m) => sum + m.duration, 0)
    return total / operationMetrics.length
  }

  // Clear old metrics (keep only last 100)
  cleanup(): void {
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100)
    }
  }

  // Get performance summary
  getSummary(): Record<string, { count: number; avgDuration: number; maxDuration: number }> {
    const summary: Record<string, { count: number; avgDuration: number; maxDuration: number }> = {}

    this.metrics.forEach(metric => {
      if (!summary[metric.name]) {
        summary[metric.name] = { count: 0, avgDuration: 0, maxDuration: 0 }
      }

      summary[metric.name].count++
      summary[metric.name].maxDuration = Math.max(summary[metric.name].maxDuration, metric.duration)
    })

    // Calculate averages
    Object.keys(summary).forEach(name => {
      summary[name].avgDuration = this.getAverageDuration(name)
    })

    return summary
  }
}

export const performanceMonitor = new PerformanceMonitor()

// Clean up metrics every 15 minutes to reduce CPU usage
if (typeof window !== 'undefined') {
  setInterval(() => {
    performanceMonitor.cleanup()
  }, 15 * 60 * 1000)
}

// Utility function to measure API calls
export async function measureApiCall<T>(name: string, apiCall: () => Promise<T>): Promise<T> {
  return performanceMonitor.measure(`api:${name}`, apiCall)
}

// Utility function to measure component renders
export function measureRender(componentName: string): () => void {
  const startTime = performance.now()
  return () => {
    const duration = performance.now() - startTime
    if (duration > 16) { // More than one frame (60fps)
      console.warn(`Slow render: ${componentName} took ${duration.toFixed(2)}ms`)
    }
  }
}

// Web Vitals monitoring
export function initWebVitals(): void {
  if (typeof window === 'undefined') return

  // Monitor Largest Contentful Paint (LCP)
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        
        if (lastEntry && lastEntry.startTime > 2500) {
          console.warn(`Poor LCP: ${lastEntry.startTime.toFixed(2)}ms`)
        }
      })
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (e) {
      // Ignore errors in older browsers
    }
  }

  // Monitor First Input Delay (FID)
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.processingStart - entry.startTime > 100) {
            console.warn(`Poor FID: ${(entry.processingStart - entry.startTime).toFixed(2)}ms`)
          }
        })
      })
      
      observer.observe({ entryTypes: ['first-input'] })
    } catch (e) {
      // Ignore errors in older browsers
    }
  }
}

// Initialize web vitals monitoring
if (typeof window !== 'undefined') {
  initWebVitals()
}
