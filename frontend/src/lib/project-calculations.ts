import type { Project, Shoot, Client } from '@/types'

// GST rate (18% in India)
const GST_RATE = 0.18

export interface ProjectCalculation {
  subtotal: number
  gstAmount: number
  total: number
  hasGst: boolean
  expenses: number
  outsourcing: number
  profit: number
}

/**
 * Calculate project totals based on shoots, client GST status, expenses, and outsourcing costs
 *
 * IMPORTANT: Outsourcing costs are handled in two ways:
 * 1. If vendor payments are recorded as expenses (recommended), they are included in the expenses parameter
 * 2. If vendor payments are not yet recorded, they are calculated from shoot.outsourcing_cost
 *
 * To avoid double-counting, we only subtract outsourcing costs that haven't been recorded as expenses yet.
 */
export function calculateProjectTotal(
  shoots: Shoot[],
  client: Client,
  expenses: number = 0,
  outsourcingExpenses: number = 0 // Expenses specifically categorized as "outsourcing"
): ProjectCalculation {
  // Calculate subtotal from all shoots
  const subtotal = shoots.reduce((sum, shoot) => sum + (shoot.amount || 0), 0)

  // Calculate total outsourcing costs from all shoots
  const totalOutsourcingFromShoots = shoots.reduce((sum, shoot) => {
    // For legacy single vendor shoots
    if (shoot.outsourcing_cost) {
      return sum + shoot.outsourcing_cost
    }
    // For multiple vendor shoots, sum all vendor costs
    if (shoot.vendors && shoot.vendors.length > 0) {
      return sum + shoot.vendors.reduce((vendorSum, vendor) => vendorSum + (vendor.cost || 0), 0)
    }
    return sum
  }, 0)

  // Calculate unpaid outsourcing costs (total outsourcing - already paid as expenses)
  // This prevents double-counting when vendor payments are recorded as expenses
  const unpaidOutsourcing = Math.max(0, totalOutsourcingFromShoots - outsourcingExpenses)

  // Calculate GST if client has GST registration
  const hasGst = client.has_gst
  const gstAmount = hasGst ? subtotal * GST_RATE : 0
  const total = subtotal + gstAmount

  // Calculate profit (total revenue - all expenses - unpaid outsourcing costs)
  // Note: expenses already include paid outsourcing costs, so we only subtract unpaid ones
  const profit = total - expenses - unpaidOutsourcing

  return {
    subtotal,
    gstAmount,
    total,
    hasGst,
    expenses,
    outsourcing: totalOutsourcingFromShoots, // Total outsourcing costs (for display)
    profit
  }
}

/**
 * Update project amounts in the database
 */
export function getUpdatedProjectAmounts(
  shoots: Shoot[],
  client: Client,
  amountReceived: number = 0
): {
  total_amount: number
  gst_inclusive: boolean
  amount_received: number
  amount_pending: number
} {
  // For project amount calculation, we don't include expenses since this is just for billing
  const calculation = calculateProjectTotal(shoots, client, 0, 0)

  return {
    total_amount: calculation.total,
    gst_inclusive: calculation.hasGst,
    amount_received: amountReceived,
    amount_pending: calculation.total - amountReceived
  }
}

/**
 * Calculate outsourcing expenses from a list of expenses
 */
export function calculateOutsourcingExpenses(expenses: Array<{ category: string; amount: number }>): number {
  return expenses
    .filter(expense => expense.category === 'outsourcing')
    .reduce((sum, expense) => sum + expense.amount, 0)
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

/**
 * Calculate GST breakdown for display
 */
export function getGstBreakdown(
  subtotal: number,
  hasGst: boolean
): {
  subtotal: number
  gstRate: number
  gstAmount: number
  total: number
  formattedSubtotal: string
  formattedGstAmount: string
  formattedTotal: string
} {
  const gstAmount = hasGst ? subtotal * GST_RATE : 0
  const total = subtotal + gstAmount

  return {
    subtotal,
    gstRate: hasGst ? GST_RATE * 100 : 0, // Convert to percentage
    gstAmount,
    total,
    formattedSubtotal: formatCurrency(subtotal),
    formattedGstAmount: formatCurrency(gstAmount),
    formattedTotal: formatCurrency(total)
  }
}

/**
 * Calculate vendor payment status for a project
 */
export function calculateVendorPaymentStatus(
  shoots: Shoot[],
  outsourcingExpenses: number
): {
  totalOwed: number
  totalPaid: number
  status: 'pending' | 'paid' | 'partial'
  paymentProgress: number
} {
  // Calculate total outsourcing costs from all shoots
  const totalOwed = shoots.reduce((sum, shoot) => {
    // For legacy single vendor shoots
    if (shoot.outsourcing_cost) {
      return sum + shoot.outsourcing_cost
    }
    // For multiple vendor shoots, sum all vendor costs
    if (shoot.vendors && shoot.vendors.length > 0) {
      return sum + shoot.vendors.reduce((vendorSum, vendor) => vendorSum + (vendor.cost || 0), 0)
    }
    return sum
  }, 0)

  const totalPaid = outsourcingExpenses
  const paymentProgress = totalOwed > 0 ? (totalPaid / totalOwed) * 100 : 0

  let status: 'pending' | 'paid' | 'partial' = 'pending'
  if (totalPaid >= totalOwed && totalOwed > 0) {
    status = 'paid'
  } else if (totalPaid > 0) {
    status = 'partial'
  }

  return {
    totalOwed,
    totalPaid,
    status,
    paymentProgress
  }
}
