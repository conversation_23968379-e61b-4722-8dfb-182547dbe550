import type { Client } from '@/types'

// GST rate (18% in India)
const GST_RATE = 0.18

export interface ScheduleGSTCalculation {
  baseAmount: number
  hasGst: boolean
  gstRate: number
  gstAmount: number
  totalAmount: number
  formattedBaseAmount: string
  formattedGstAmount: string
  formattedTotalAmount: string
}

/**
 * Calculate GST for a schedule based on base amount and GST status
 */
export function calculateScheduleGST(
  baseAmount: number,
  hasGst: boolean
): ScheduleGSTCalculation {
  const gstAmount = hasGst ? baseAmount * GST_RATE : 0
  const totalAmount = baseAmount + gstAmount

  return {
    baseAmount,
    hasGst,
    gstRate: hasGst ? GST_RATE * 100 : 0, // Convert to percentage
    gstAmount,
    totalAmount,
    formattedBaseAmount: formatCurrency(baseAmount),
    formattedGstAmount: formatCurrency(gstAmount),
    formattedTotalAmount: formatCurrency(totalAmount)
  }
}

/**
 * Determine default GST status based on client's GST registration
 */
export function getDefaultGSTStatus(client?: Client): boolean {
  return client?.has_gst || false
}

/**
 * Calculate GST amounts from base amount and GST status
 * Returns the values needed for database storage
 */
export function getGSTAmountsForStorage(
  baseAmount: number,
  hasGst: boolean
): {
  has_gst: boolean
  gst_amount: number
  total_amount: number
} {
  const calculation = calculateScheduleGST(baseAmount, hasGst)
  
  return {
    has_gst: hasGst,
    gst_amount: calculation.gstAmount,
    total_amount: calculation.totalAmount
  }
}

/**
 * Format currency for display
 */
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount)
}

/**
 * Validate GST calculation inputs
 */
export function validateGSTInputs(baseAmount: number): {
  isValid: boolean
  error?: string
} {
  if (isNaN(baseAmount) || baseAmount < 0) {
    return {
      isValid: false,
      error: 'Base amount must be a valid positive number'
    }
  }

  return { isValid: true }
}

/**
 * Get GST breakdown for display in UI components
 */
export function getGSTBreakdownForDisplay(
  baseAmount: number,
  hasGst: boolean
): {
  showBreakdown: boolean
  baseLabel: string
  gstLabel: string
  totalLabel: string
  baseAmount: string
  gstAmount: string
  totalAmount: string
} {
  const calculation = calculateScheduleGST(baseAmount, hasGst)
  
  return {
    showBreakdown: hasGst && baseAmount > 0,
    baseLabel: 'Base Amount',
    gstLabel: `GST (${calculation.gstRate}%)`,
    totalLabel: 'Total Amount',
    baseAmount: calculation.formattedBaseAmount,
    gstAmount: calculation.formattedGstAmount,
    totalAmount: calculation.formattedTotalAmount
  }
}
