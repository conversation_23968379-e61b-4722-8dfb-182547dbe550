import { createClient } from '@supabase/supabase-js'
import { getDefaultTasksForClientType, getDefaultTasksForScheduleType, createTasksFromTemplates } from '@/lib/default-tasks'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Server-side task creation function using service role client
export async function createDefaultTasksServerSide(
  projectId: string,
  clientType: string,
  shootDate?: string,
  shootId?: string,
  scheduleType?: string
): Promise<void> {
  console.log('=== createDefaultTasksServerSide START ===')
  console.log('Parameters:', { projectId, clientType, scheduleType, shootDate, shootId })
  console.log('Task type:', shootId ? 'SHOOT-BASED TASKS' : 'PROJECT-LEVEL TASKS')

  try {
    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get templates based on task type
    let templates
    if (shootId && scheduleType) {
      // For schedule-based tasks, use schedule type
      templates = getDefaultTasksForScheduleType(scheduleType)
      console.log(`Templates found for schedule type "${scheduleType}":`, templates.length, 'templates')
    } else {
      // For project-level tasks, use client type
      templates = getDefaultTasksForClientType(clientType)
      console.log(`Templates found for client type "${clientType}":`, templates.length, 'templates')
    }

    if (templates.length === 0) {
      console.log('No templates found for type:', shootId ? scheduleType : clientType)
      return
    }

    // Check if tasks already exist to avoid duplicates
    let existingTasksQuery = supabase
      .from('tasks')
      .select('id, title, shoot_id')
      .eq('project_id', projectId)

    if (shootId) {
      // For shoot-based tasks, check if tasks exist for this specific shoot
      existingTasksQuery = existingTasksQuery.eq('shoot_id', shootId)
    } else {
      // For project-level tasks, check if project tasks exist (shoot_id is null)
      existingTasksQuery = existingTasksQuery.is('shoot_id', null)
    }

    const { data: existingTasks, error: existingTasksError } = await existingTasksQuery

    if (existingTasksError) {
      console.error('Error checking existing tasks:', existingTasksError)
      throw existingTasksError
    }

    if (existingTasks && existingTasks.length > 0) {
      console.log(`Tasks already exist for ${shootId ? 'shoot ' + shootId : 'project ' + projectId}:`)
      existingTasks.forEach(task => {
        console.log(`  - ${task.title} (${task.shoot_id ? 'shoot task' : 'project task'})`)
      })
      console.log('=== createDefaultTasksServerSide END (SKIPPED - DUPLICATES) ===')
      return // Don't create duplicate tasks
    }

    // Get users by role for task assignment
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, role')

    if (usersError) {
      console.error('Error fetching users:', usersError)
      throw usersError
    }

    // Group users by role
    const usersByRole: Record<string, string> = {}
    const usersGroupedByRole = users?.reduce((acc: Record<string, any[]>, user) => {
      if (!acc[user.role]) acc[user.role] = []
      acc[user.role].push(user)
      return acc
    }, {}) || {}

    console.log('Users by role:', Object.keys(usersGroupedByRole).reduce((acc, role) => {
      acc[role] = usersGroupedByRole[role].length
      return acc
    }, {} as Record<string, number>))

    // Assign first user of each role (you might want to implement better logic)
    Object.keys(usersGroupedByRole).forEach(role => {
      if (usersGroupedByRole[role].length > 0) {
        usersByRole[role] = usersGroupedByRole[role][0].id
      }
    })

    console.log('Role to user mapping:', usersByRole)

    // Separate shoot-based and project-level tasks
    const shootBasedTemplates = templates.filter(t => !t.isProjectTask)
    const projectLevelTemplates = templates.filter(t => t.isProjectTask)

    let createdCount = 0
    let skippedCount = 0

    // Create shoot-based tasks if shootId is provided
    if (shootId && shootBasedTemplates.length > 0) {
      console.log('Creating shoot-based tasks for shoot:', shootId)
      const shootTaskForms = createTasksFromTemplates(shootBasedTemplates, projectId, usersByRole, shootDate, shootId)

      for (const taskForm of shootTaskForms) {
        if (taskForm.assigned_to) {
          console.log('Creating shoot task:', {
            title: taskForm.title,
            assigned_to: taskForm.assigned_to,
            project_id: taskForm.project_id,
            shoot_id: taskForm.shoot_id
          })
          try {
            const { data: createdTask, error: createError } = await supabase
              .from('tasks')
              .insert(taskForm)
              .select('id, title')
              .single()

            if (createError) {
              console.error('Failed to create shoot task:', createError)
              throw createError
            }

            console.log('Shoot task created successfully:', createdTask.id, createdTask.title)
            createdCount++
          } catch (taskCreateError) {
            console.error('Failed to create shoot task:', taskCreateError)
            throw taskCreateError
          }
        } else {
          console.log('Skipping shoot task without assigned user:', taskForm.title)
          skippedCount++
        }
      }
    }

    // Create project-level tasks only if they don't already exist and only when called without shootId
    if (projectLevelTemplates.length > 0 && !shootId) {
      console.log('Creating project-level tasks for project:', projectId)
      const projectTaskForms = createTasksFromTemplates(projectLevelTemplates, projectId, usersByRole, shootDate, undefined)

      for (const taskForm of projectTaskForms) {
        if (taskForm.assigned_to) {
          console.log('Creating project task:', {
            title: taskForm.title,
            assigned_to: taskForm.assigned_to,
            project_id: taskForm.project_id,
            shoot_id: taskForm.shoot_id
          })
          try {
            const { data: createdTask, error: createError } = await supabase
              .from('tasks')
              .insert(taskForm)
              .select('id, title')
              .single()

            if (createError) {
              console.error('Failed to create project task:', createError)
              throw createError
            }

            console.log('Project task created successfully:', createdTask.id, createdTask.title)
            createdCount++
          } catch (taskCreateError) {
            console.error('Failed to create project task:', taskCreateError)
            throw taskCreateError
          }
        } else {
          console.log('Skipping project task without assigned user:', taskForm.title)
          skippedCount++
        }
      }
    }

    console.log(`=== createDefaultTasksServerSide END ===`)
    console.log(`Summary: ${createdCount} tasks created, ${skippedCount} tasks skipped`)
    console.log(`Task type: ${shootId ? 'SHOOT-BASED' : 'PROJECT-LEVEL'}`)
    console.log('=======================================')
  } catch (error) {
    console.error('❌ CRITICAL ERROR in createDefaultTasksServerSide:', error)
    console.error('Parameters that caused the error:', { projectId, clientType, shootDate, shootId })
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    console.log('=== createDefaultTasksServerSide END (ERROR) ===')
    throw error
  }
}
