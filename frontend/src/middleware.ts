import { updateSession } from '@/lib/auth-server'
import { NextRequest, NextResponse } from 'next/server'

export async function middleware(request: NextRequest) {
  // Authentication middleware enabled with proper session handling
  console.log('🔐 Middleware processing:', request.nextUrl.pathname)
  
  // Skip auth check for static files and API routes
  if (
    request.nextUrl.pathname.startsWith('/_next/') ||
    request.nextUrl.pathname.startsWith('/api/') ||
    request.nextUrl.pathname.includes('.')
  ) {
    return NextResponse.next()
  }
  
  const result = await updateSession(request)
  console.log('🔐 Middleware result:', result.status, result.headers.get('location'))
  return result
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files (including service worker)
     * - _rsc (React Server Components)
     * - sw.js (service worker)
     * - manifest.json (PWA manifest)
     */
    '/((?!_next/static|_next/image|favicon.ico|sw\.js|manifest\.json|.*\.(?:svg|png|jpg|jpeg|gif|webp)$|_rsc).*)',
  ],
}
