// Test geocoding functionality
const testLocations = [
  'Mumbai, Maharashtra, India',
  'Delhi, India', 
  'Bangalore, Karnataka, India',
  'Chennai, Tamil Nadu, India',
  'Pune, Maharashtra, India'
];

console.log('🧪 Testing geocoding with sample locations...');

// This would be run in browser console to test geocoding
testLocations.forEach(async (location, index) => {
  console.log(`\n📍 Testing location ${index + 1}: ${location}`);
  
  // Note: This requires Google Maps API to be loaded
  // Run this in browser console on the map page
  try {
    if (typeof google !== 'undefined' && google.maps) {
      const geocoder = new google.maps.Geocoder();
      
      geocoder.geocode(
        { 
          address: location,
          region: 'IN',
          componentRestrictions: { country: 'IN' }
        },
        (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results) {
            const { lat, lng } = results[0].geometry.location;
            console.log(`✅ ${location} -> ${lat()}, ${lng()}`);
          } else {
            console.log(`❌ Failed to geocode ${location}: ${status}`);
          }
        }
      );
    } else {
      console.log('❌ Google Maps API not loaded');
    }
  } catch (error) {
    console.error(`❌ Error geocoding ${location}:`, error);
  }
});

console.log('\n📋 Instructions:');
console.log('1. Open browser console on the map page');
console.log('2. Copy and paste the geocoding test code');
console.log('3. Check if coordinates are returned for test locations');