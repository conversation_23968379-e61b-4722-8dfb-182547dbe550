# Product Requirements Document: Cymatics (v2 - Detailed)

## 1. Introduction

### 1.1. Vision & Mission

To be the definitive project management and operational hub for drone service companies. Cymatics provides a modern, high-performance, and real-time system to streamline every aspect of the business, from client acquisition to final payment, enhancing data visibility and operational efficiency.

### 1.2. Target Audience

- **Admins:** System administrators managing users, roles, and system-wide settings.
- **Managers:** Project managers overseeing project lifecycles, schedules, finances, and resource allocation.
- **Pilots:** Drone operators executing assigned tasks and reporting field status.
- **Editors:** Staff managing client, project, and financial data.

## 2. Goals & Objectives

### 2.1. Business Goals

- **Increase Operational Efficiency:** Reduce time spent on manual data entry and status tracking.
- **Improve Financial Visibility:** Provide real-time insights into revenue, expenses, and project profitability.
- **Enhance Client Satisfaction:** Streamline project execution and communication.
- **Scalable Architecture:** Build a robust system that can grow with the company's needs.

### 2.2. User Goals

- **Managers:** Gain an immediate, comprehensive overview of all business operations (projects, schedules, finances, tasks) via a real-time dashboard to enable rapid, data-driven decisions.
- **All Users:** Interact with a fast, secure, and intuitive interface that simplifies their specific daily tasks.

## 3. Features & Functionality

### 3.1. Authentication & User Management
- **Secure Sign-in:** Users can sign in with email and password.
- **Role-Based Access Control (RBAC):** System access is restricted based on user roles (Admin, Manager, etc.). The database is secured with Row Level Security (RLS) policies.
- **User Profiles:** Users can manage their own profile information.

### 3.2. Real-time Dashboard
- **Consolidated Stats:** A central dashboard displays key metrics, including:
    - Active Projects
    - Upcoming Schedules
    - Overdue Tasks
    - Monthly Revenue
    - Total Clients
    - Completed Projects
- **Data Visualization:** Key metrics are presented in visually appealing cards and charts.
- **Financial Overview:** A dedicated section shows Total Revenue, Total Expenses, and Net Income.
- **Recent Activity Feed:** A live feed of important events (new projects, completed tasks, etc.).

### 3.3. Project Management
- **CRUD Operations:** Users can create, view, update, and delete projects.
- **Project Tracking:** Projects have statuses (e.g., `active`, `completed`, `on_hold`).
- **Search & Filter:** Users can search for projects and filter by status.
- **Financial Tracking:** Track total project value and pending payments.

### 3.4. Schedule Management
- **Scheduling:** Create and manage schedules for drone operations, linked to projects.
- **Calendar View:** A calendar displays all upcoming schedules.
- **Location Data:** Schedules include location information.

### 3.5. Task Management
- **Task Tracking:** Assign and track tasks with statuses (`pending`, `completed`) and due dates.
- **Priority Levels:** Tasks can be assigned a priority (`low`, `medium`, `high`, `urgent`).

### 3.6. Client Management
- **Client Database:** Maintain a central database of all clients.

### 3.7. Notifications
- **In-App Notifications:** A comprehensive notification system alerts users to important events (e.g., `task_assigned`, `payment_received`, `schedule_upcoming`).
- **User Preferences:** Users can manage their notification preferences (email, push, in-app).

## 4. Technical Requirements & Architecture

### 4.1. Technology Stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS, Radix UI, `react-hook-form`, `sonner` (for toasts).
- **Backend**: Node.js, Express.js, TypeScript, `cors`.
- **Database**: Supabase (PostgreSQL).
- **Authentication**: Supabase Auth (JWT-based), with frontend helpers (`@supabase/auth-helpers-nextjs`).

### 4.2. Architecture
The system uses a decoupled frontend/backend architecture.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (Next.js)     │◄──►│   (Node/Express)│◄──►│   (Supabase)    │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```
- **Frontend:** A server-side rendered (SSR) and client-side interactive application built with Next.js. It communicates with the backend via a RESTful API. State management is handled via React Context (`AuthContext`) and custom data-fetching hooks (`useRealTimeProjects`, etc.).
- **Backend:** An Express.js server provides RESTful API endpoints (e.g., `/api/auth`, `/api/dashboard`, `/api/projects`).
- **Database:** A PostgreSQL database managed by Supabase. It contains tables for `projects`, `schedules`, `tasks`, `clients`, `payments`, `notifications`, and more. RLS is enabled on critical tables to enforce data access policies.

### 4.3. Key Database Objects
- **`projects` table:** Stores project information, including name, status, and client links.
- **`schedules` table:** Stores information about scheduled drone shoots.
- **`notifications` table:** Stores user-specific notifications with types, priorities, and read status.
- **`get_dashboard_stats()` function:** A crucial performance optimization that consolidates 8+ different queries into a single database function call to efficiently fetch all statistics for the main dashboard.

## 5. Success Metrics (KPIs)

- **Dashboard Load Time:** Initial load of the dashboard page should be under 2 seconds.
- **API Response Time:** P95 latency for the `/api/dashboard/stats` endpoint should be under 200ms.
- **User Engagement:** High daily/weekly active users across all primary roles.
- **Task Completion Rate:** High percentage of tasks completed by their due date.