-- Migration: Add GST fields to schedules table
-- Date: 2025-01-18
-- Description: Adds GST-related columns to schedules table for GST calculation and tracking

-- Add GST fields to schedules table
ALTER TABLE schedules 
ADD COLUMN IF NOT EXISTS has_gst BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS gst_amount DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_amount DECIMAL(10,2) DEFAULT 0;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_schedules_has_gst ON schedules(has_gst);
CREATE INDEX IF NOT EXISTS idx_schedules_total_amount ON schedules(total_amount);

-- Add comments to document the new columns
COMMENT ON COLUMN schedules.has_gst IS 'Whether GST is applied to this schedule (18% in India)';
COMMENT ON COLUMN schedules.gst_amount IS 'GST amount calculated as 18% of base amount when has_gst is true';
COMMENT ON COLUMN schedules.total_amount IS 'Total amount including GST (base amount + gst_amount)';

-- Update existing schedules to set total_amount = amount for backward compatibility
UPDATE schedules 
SET total_amount = amount, 
    has_gst = FALSE, 
    gst_amount = 0 
WHERE total_amount IS NULL OR total_amount = 0;
