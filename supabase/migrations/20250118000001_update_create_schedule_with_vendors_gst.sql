-- Migration: Update create_schedule_with_vendors function to handle GST fields
-- Date: 2025-01-18
-- Description: Updates the create_schedule_with_vendors function to support GST calculation and storage

CREATE OR REPLACE FUNCTION public.create_schedule_with_vendors(
  p_project_id UUID,
  p_scheduled_date TIMESTAMPTZ,
  p_scheduled_end_date TIMESTAMPTZ,
  p_pilot_id UUID,
  p_amount DECIMAL,
  p_location TEXT,
  p_google_maps_link TEXT,
  p_notes TEXT,
  p_is_recurring BOOLEAN,
  p_recurring_pattern TEXT,
  p_is_outsourced BOOLEAN,
  p_schedule_type TEXT,
  p_has_gst BOOLEAN DEFAULT FALSE,
  p_gst_amount DECIMAL DEFAULT 0,
  p_total_amount DECIMAL DEFAULT 0,
  p_vendors JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  new_schedule_id UUID;
  new_schedule JSONB;
  vendor_record JSONB;
BEGIN
  INSERT INTO public.schedules (
    project_id, scheduled_date, scheduled_end_date, pilot_id, amount,
    location, google_maps_link, notes, is_recurring, recurring_pattern, 
    is_outsourced, schedule_type, has_gst, gst_amount, total_amount
  ) VALUES (
    p_project_id, p_scheduled_date, p_scheduled_end_date, p_pilot_id, p_amount,
    p_location, p_google_maps_link, p_notes, p_is_recurring, 
    p_recurring_pattern::recurring_pattern, p_is_outsourced, p_schedule_type,
    p_has_gst, p_gst_amount, p_total_amount
  ) RETURNING id INTO new_schedule_id;

  -- Insert vendors if provided
  IF p_is_outsourced AND jsonb_array_length(p_vendors) > 0 THEN
    FOR vendor_record IN SELECT * FROM jsonb_array_elements(p_vendors)
    LOOP
      INSERT INTO public.schedule_vendors (schedule_id, vendor_id, cost, notes)
      VALUES (
        new_schedule_id,
        (vendor_record->>'vendor_id')::UUID,
        (vendor_record->>'cost')::DECIMAL,
        vendor_record->>'notes'
      );
    END LOOP;
  END IF;

  -- Return the created schedule with custom_id
  SELECT to_jsonb(s) INTO new_schedule 
  FROM public.schedules s 
  WHERE s.id = new_schedule_id;
  
  RETURN new_schedule;
END;
$function$;
