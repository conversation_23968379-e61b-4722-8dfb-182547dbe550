-- Migration: Update update_schedule_with_vendors function to handle GST fields
-- Date: 2025-01-18
-- Description: Updates the update_schedule_with_vendors function to support GST calculation and storage

CREATE OR REPLACE FUNCTION public.update_schedule_with_vendors(
  p_schedule_id UUID,
  p_updates JSONB,
  p_vendors JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  updated_schedule JSONB;
  vendor_record JSONB;
BEGIN
  -- Update the schedule using JSONB updates
  UPDATE public.schedules SET
    scheduled_date = COALESCE((p_updates->>'scheduled_date')::TIMESTAMPTZ, scheduled_date),
    scheduled_end_date = CASE
      WHEN p_updates ? 'scheduled_end_date' THEN (p_updates->>'scheduled_end_date')::TIMESTAMPTZ
      ELSE scheduled_end_date
    END,
    pilot_id = CASE
      WHEN p_updates ? 'pilot_id' THEN (p_updates->>'pilot_id')::UUID
      ELSE pilot_id
    END,
    amount = COALESCE((p_updates->>'amount')::DECIMAL, amount),
    has_gst = COALESCE((p_updates->>'has_gst')::BOOLEAN, has_gst),
    gst_amount = COALESCE((p_updates->>'gst_amount')::DECIMAL, gst_amount),
    total_amount = COALESCE((p_updates->>'total_amount')::DECIMAL, total_amount),
    location = CASE
      WHEN p_updates ? 'location' THEN p_updates->>'location'
      ELSE location
    END,
    google_maps_link = CASE
      WHEN p_updates ? 'google_maps_link' THEN p_updates->>'google_maps_link'
      ELSE google_maps_link
    END,
    notes = CASE
      WHEN p_updates ? 'notes' THEN p_updates->>'notes'
      ELSE notes
    END,
    is_recurring = COALESCE((p_updates->>'is_recurring')::BOOLEAN, is_recurring),
    recurring_pattern = CASE
      WHEN p_updates ? 'recurring_pattern' THEN (p_updates->>'recurring_pattern')::recurring_pattern
      ELSE recurring_pattern
    END,
    is_outsourced = COALESCE((p_updates->>'is_outsourced')::BOOLEAN, is_outsourced),
    schedule_type = CASE
      WHEN p_updates ? 'schedule_type' THEN p_updates->>'schedule_type'
      ELSE schedule_type
    END,
    updated_at = NOW()
  WHERE id = p_schedule_id;

  -- Clear existing vendors and insert new ones
  DELETE FROM public.schedule_vendors WHERE schedule_id = p_schedule_id;
  IF p_vendors IS NOT NULL AND jsonb_typeof(p_vendors) = 'array' AND jsonb_array_length(p_vendors) > 0 THEN
    FOR vendor_record IN SELECT * FROM jsonb_array_elements(p_vendors)
    LOOP
      INSERT INTO public.schedule_vendors (schedule_id, vendor_id, cost, notes)
      VALUES (
        p_schedule_id,
        (vendor_record->>'vendor_id')::UUID,
        (vendor_record->>'cost')::DECIMAL,
        vendor_record->>'notes'
      );
    END LOOP;
  END IF;

  -- Return the updated schedule
  SELECT to_jsonb(s) INTO updated_schedule 
  FROM public.schedules s 
  WHERE s.id = p_schedule_id;
  
  RETURN updated_schedule;
END;
$function$;
