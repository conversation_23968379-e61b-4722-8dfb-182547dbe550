{"meta": {"project": "Cymatics Internal Web App", "date": "2025-09-13", "prepared_by": "Generated by TestSprite"}, "product_overview": "Cymatics Internal Web App streamlines drone-based service operations including project management, scheduling, client handling, billing, and operations with offline-first capabilities and role-based access control, integrating OneDrive for file management and supporting exports in multiple formats.", "core_goals": ["Centralize all business operations such as projects, clients, payments, scheduling, and tasks for efficient management.", "Implement role-based access control for <PERSON><PERSON>, Manager, Pilot, and Editor with tailored permissions.", "Support offline-first data entry with seamless sync to cloud backend using Next.js and Supabase.", "Ensure strong data security with role-based authentication and row-level security on the backend.", "Provide scalable and modular architecture for future client portal integration and feature expansion."], "key_features": ["Dashboard with metrics on active projects, upcoming shoots, pending payments, and overdue tasks along with notifications for key events.", "Client management module supporting detailed client and contact entries with export options to CSV, Excel, and PDF.", "Comprehensive project management with card-style UI, detailed project info, schedule support, and Google Maps visualization.", "Shoot and schedule management including recurring scheduling, pilot/editor assignment, and calendar integration with reminders.", "Automated file uploads and folder structure enforcement on OneDrive with role-based file access.", "Payments and billing system tracking invoices, payments, and alerts for pending dues, including export functionality.", "Task management for post-processing workflows with assignment, status tracking, and client update logs.", "Expense and budget tracking supporting offline mode with role restrictions and report generation.", "Productivity tracking excluding admin users with activity logs for uploads, edits, and project completion.", "In-app notification system for reminders and alerts without email integration in version 1.", "Export capabilities for clients, projects, payments, and expenses in CSV, Excel, and PDF formats."], "user_flow_summary": ["User authentication flow with secure login, signup, password reset, and role-based access controls.", "Dashboard overview presenting real-time analytics and notifications upon login.", "Client management flow allowing creation, editing, and exporting of client data.", "Project lifecycle from creation, scheduling (manual/recurring), assignment, to tracking with map integration for location visualization.", "Shoot scheduling and management flows with calendar reminders and pilot/editor assignments.", "File upload and management with automatic OneDrive folder structuring according to client/project/shoot date hierarchy.", "Payments and billing entry, tracking, alerts for overdue payments, and export workflows.", "Task creation, assignment, progress updates, and completion logging for post-processing tasks.", "Expense entry with offline support, role-based visibility, and report generation.", "In-app notification interactions for alerts on shoots, payments, and tasks for all relevant users."], "validation_criteria": ["Role-based access control enforced across all modules with thorough testing for permission boundaries.", "Offline-first functionality tested on multiple devices ensuring data sync consistency with Supabase backend.", "File uploads properly mapped and stored in OneDrive with correct folder structure and access permissions.", "All data exports generate correct and complete files in CSV, Excel, and PDF formats.", "Performance benchmarks met for responsiveness on typical user devices including low-end hardware.", "Security audits completed for authentication, authorization, and backend data access controls.", "Notification system delivers timely alerts within the app without fail.", "Scheduling supports both manual and recurring entries with calendar reminders firing appropriately.", "Comprehensive unit and integration tests for core modules such as projects, clients, payments, and tasks."], "code_summary": {"tech_stacks": [{"name": "Next.js", "version": "15", "category": "Frontend Framework", "description": "React framework with App Router for server-side rendering and static generation"}, {"name": "React", "version": "19", "category": "UI Library", "description": "Component-based UI library for building interactive user interfaces"}, {"name": "TypeScript", "version": "latest", "category": "Programming Language", "description": "Strongly typed JavaScript for better code quality and developer experience"}, {"name": "Tailwind CSS", "version": "latest", "category": "CSS Framework", "description": "Utility-first CSS framework for rapid UI development"}, {"name": "Supabase", "version": "latest", "category": "Backend as a Service", "description": "PostgreSQL database with authentication and real-time features"}, {"name": "Google Maps API", "version": "latest", "category": "Maps Service", "description": "Location services, geocoding, and interactive maps functionality"}, {"name": "Microsoft Graph API", "version": "latest", "category": "Cloud Integration", "description": "SharePoint integration for file management and collaboration"}, {"name": "React Hook Form", "version": "latest", "category": "Form Management", "description": "Performant forms with easy validation"}, {"name": "Lucide React", "version": "latest", "category": "Icon Library", "description": "Beautiful and customizable SVG icons"}, {"name": "React Hot Toast", "version": "latest", "category": "Notification System", "description": "Toast notifications for user feedback"}], "features": [{"name": "User Authentication", "description": "Secure login/logout system with Supabase Auth, including signup, forgot password, and reset password functionality", "pages": ["/login", "/signup", "/forgot-password", "/reset-password"], "components": ["AuthContext", "ProtectedRoute", "LogoutButton"]}, {"name": "Dashboard Analytics", "description": "Real-time project statistics and overview with performance monitoring and analytics", "pages": ["/dashboard"], "components": ["dashboard-analytics", "performance-monitor", "realtime-indicator"]}, {"name": "Project Management", "description": "Complete CRUD operations for projects with detailed project cards and forms", "pages": ["/projects", "/projects/[id]"], "components": ["ProjectForm", "project-card"]}, {"name": "Schedule Management", "description": "Drone operation scheduling system with calendar integration and completion tracking", "pages": ["/schedules", "/schedules/[id]", "/calendar"], "components": ["ScheduleForm", "schedule-card", "schedule-completion-form", "calendar"]}, {"name": "Task Management", "description": "Advanced task tracking with assignment, priority levels, status management, and optimized task cards", "pages": ["/tasks", "/admin/tasks"], "components": ["TaskForm", "task-card", "enhanced-task-card", "optimized-task-card", "redesigned-task-card", "task-assignment-modal", "task-completion-form", "admin-task-manager"]}, {"name": "Client Management", "description": "Comprehensive client database with contact person management and client selector", "pages": ["/clients", "/clients/[id]"], "components": ["ClientForm", "ContactPersonForm", "ContactPersonsList", "client-selector", "contact-person-selector"]}, {"name": "Vendor Management", "description": "Vendor database with payment tracking and vendor shoot management", "pages": ["/vendors", "/vendors/[id]"], "components": ["VendorForm", "VendorPaymentForm", "RecordVendorPaymentForm", "vendor-payment-card"]}, {"name": "Shoot Management", "description": "Drone shoot tracking with completion forms and vendor integration", "pages": ["/shoots", "/shoots/[id]"], "components": ["ShootForm", "shoot-completion-form"]}, {"name": "Financial Management", "description": "Expense tracking, payment management, and interactive spending analytics", "pages": ["/finances"], "components": ["ExpenseForm", "AddExpenseForm", "PaymentForm", "expense-card", "payment-card", "payment-progress-bar", "interactive-spending-chart"]}, {"name": "Interactive Maps", "description": "Google Maps integration with heatmap visualization, location search, and flight planning", "pages": ["/map"], "components": ["heatmap-viewer", "location-search", "location-analytics", "flight-planner"]}, {"name": "Notification System", "description": "Comprehensive in-app notifications with management interface and real-time updates", "pages": [], "components": ["notification-bell", "notification-management"]}, {"name": "Settings & Profile", "description": "User profile management and application settings", "pages": ["/profile", "/settings"], "components": []}, {"name": "Background Jobs", "description": "Automated background processing and job management system", "pages": [], "components": ["background-jobs-initializer"]}, {"name": "SharePoint Integration", "description": "Microsoft SharePoint integration for file management and folder synchronization", "pages": [], "components": []}, {"name": "Progressive Web App", "description": "PWA capabilities with service worker registration and install prompts", "pages": [], "components": ["pwa-install", "pwa-install-backup", "service-worker-registration"]}, {"name": "Performance Monitoring", "description": "Real-time performance monitoring and optimization tools", "pages": ["/debug-tasks", "/debug-default-tasks", "/test-tasks"], "components": ["PerformanceMonitor", "task-creation-debug"]}]}}