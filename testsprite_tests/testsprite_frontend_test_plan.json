[{"id": "TC001", "title": "User login with valid credentials", "description": "Verify that a user can successfully log in with valid username/email and password credentials.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the login page"}, {"type": "action", "description": "Enter a valid username/email and password"}, {"type": "action", "description": "Click the login button"}, {"type": "assertion", "description": "Verify that the user is successfully authenticated and redirected to the dashboard"}]}, {"id": "TC002", "title": "User login with invalid credentials", "description": "Verify that login attempt with incorrect username/email or password is denied and proper error message is shown.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the login page"}, {"type": "action", "description": "Enter invalid username/email or password"}, {"type": "action", "description": "Click the login button"}, {"type": "assertion", "description": "Verify that login is rejected and an appropriate error message is displayed"}]}, {"id": "TC003", "title": "Role-based access control enforcement", "description": "Verify users with <PERSON><PERSON>, Manager, Pilot, and Editor roles have correct access permissions per their role restrictions.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Login as an Admin user and verify access to all modules"}, {"type": "action", "description": "Login as a Manager user and verify only allowed modules are accessible"}, {"type": "action", "description": "Login as a Pilot user and verify access permissions are restricted accordingly"}, {"type": "action", "description": "Login as an Editor user and verify access is limited to permitted features"}, {"type": "assertion", "description": "Confirm that forbidden pages or actions are blocked and proper notification or redirect occurs"}]}, {"id": "TC004", "title": "Offline-first data entry and sync", "description": "Validate that data can be entered and saved offline and synchronizes correctly with the Supabase backend once connection is restored.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Activate offline mode on a supported device"}, {"type": "action", "description": "Create or update a project, client, payment, or task entry"}, {"type": "assertion", "description": "Verify that the data is saved locally and displayed in the UI"}, {"type": "action", "description": "Restore network connection"}, {"type": "assertion", "description": "Verify data syncs to the backend and persists across sessions"}]}, {"id": "TC005", "title": "Dashboard displays correct active metrics and notifications", "description": "Confirm the dashboard shows accurate real-time counts of active projects, upcoming shoots, pending payments, overdue tasks, and notifications.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Login and navigate to the dashboard"}, {"type": "assertion", "description": "Verify that active projects metric matches the backend data"}, {"type": "assertion", "description": "Verify upcoming shoots display with correct dates and status"}, {"type": "assertion", "description": "Verify pending payments count reflects outstanding invoices"}, {"type": "assertion", "description": "Verify overdue tasks count includes all tasks past due dates"}, {"type": "assertion", "description": "Verify that notifications for key events are listed and accessible"}]}, {"id": "TC006", "title": "Client creation, editing and export", "description": "Verify that clients can be added, updated, and exported correctly in CSV, Excel, and PDF formats.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the client management page"}, {"type": "action", "description": "Create a new client with required and optional fields"}, {"type": "assertion", "description": "Verify the client appears in the list with correct details"}, {"type": "action", "description": "Edit the existing client information"}, {"type": "assertion", "description": "Verify changes are saved and reflected immediately"}, {"type": "action", "description": "Export clients data to CSV, then Excel, then PDF"}, {"type": "assertion", "description": "Verify each exported file contains complete and correct client information in proper format"}]}, {"id": "TC007", "title": "Project lifecycle management with map visualization", "description": "Test creation, editing, scheduling (manual and recurring), assignment, and project details including Google Maps location visualization.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Create a new project with all mandatory details including location"}, {"type": "assertion", "description": "Verify project card with all info appears correctly"}, {"type": "action", "description": "Add schedules manually and set recurring schedules"}, {"type": "assertion", "description": "Verify schedules appear correctly in calendar with proper recurrence"}, {"type": "action", "description": "Assign pilots and editors to the project"}, {"type": "assertion", "description": "Verify assignments reflect accurately in project details"}, {"type": "action", "description": "View project location on Google Maps"}, {"type": "assertion", "description": "Verify project location pin and heatmap data display correctly"}]}, {"id": "TC008", "title": "Shoot management with calendar integration and reminders", "description": "Validate the creation, updating, and completion tracking of shoots including calendar reminders firing appropriately.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Create a new shoot and link to project and vendor"}, {"type": "assertion", "description": "Verify shoot appears in the shoot list and calendar"}, {"type": "action", "description": "Update shoot details and mark shoot as complete"}, {"type": "assertion", "description": "Confirm updates persist and shoot completion is logged"}, {"type": "action", "description": "Verify that calendar reminders for the shoot trigger at configured times"}, {"type": "assertion", "description": "Ensure reminder notification appears in-app"}]}, {"id": "TC009", "title": "File uploads and OneDrive folder structure validation", "description": "Test uploading files with automatic folder creation and enforcement on OneDrive with correct hierarchical folder structure and role-based access.", "category": "integration", "priority": "High", "steps": [{"type": "action", "description": "Upload files for a specific client/project/shoot"}, {"type": "assertion", "description": "Verify files are stored on OneDrive in client/project/shoot date folder hierarchy"}, {"type": "assertion", "description": "Confirm that file access respects user roles and permissions"}]}, {"id": "TC010", "title": "Payments and billing system with alerts and export", "description": "Verify invoice entry, payment tracking, alert generation for pending dues, and exporting of payments data.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Create invoice and record payments for a client"}, {"type": "assertion", "description": "Verify payment status updates and overdue alerts appear correctly"}, {"type": "action", "description": "Export payments and billing data in CSV, Excel, and PDF formats"}, {"type": "assertion", "description": "Verify exports contain complete and accurate data"}]}, {"id": "TC011", "title": "Task management lifecycle including assignment and status updates", "description": "Test creation, assignment, progress updating, and completion of post-processing tasks with client update logs.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Create post-processing tasks and assign to users"}, {"type": "assertion", "description": "Verify tasks appear in lists with correct assignment details"}, {"type": "action", "description": "Update task status through various stages"}, {"type": "assertion", "description": "Confirm status updates persist and trigger appropriate UI changes"}, {"type": "action", "description": "Complete the task and verify client update logs are recorded"}, {"type": "assertion", "description": "Verify completion logs are accurate and visible to appropriate roles"}]}, {"id": "TC012", "title": "Expense and budget tracking with offline mode and reports", "description": "Validate expense entries with offline support, role-based visibility restrictions, and generation of expense reports.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Enter expense data while offline"}, {"type": "assertion", "description": "Confirm expense is saved locally"}, {"type": "action", "description": "Reconnect to network and sync data"}, {"type": "assertion", "description": "Verify expense data is synchronized to backend"}, {"type": "action", "description": "Attempt to view expense data with users in disallowed roles"}, {"type": "assertion", "description": "Verify access is denied accordingly"}, {"type": "action", "description": "Generate expense and budget reports"}, {"type": "assertion", "description": "Verify reports contain accurate aggregated data"}]}, {"id": "TC013", "title": "In-app notification delivery and handling", "description": "Verify that notifications for reminders and alerts on shoots, payments, and tasks are delivered timely within the app and can be interacted with.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Trigger notification events such as shooting reminders, payment overdue alerts, and task updates"}, {"type": "assertion", "description": "Verify notifications appear in the notification bell component in real time"}, {"type": "action", "description": "Interact with notifications (e.g., dismiss, click to navigate)"}, {"type": "assertion", "description": "Verify respective actions are performed correctly and UI updates accordingly"}]}, {"id": "TC014", "title": "Export functionality for clients, projects, payments, and expenses", "description": "Ensure exports produce accurate, complete data files in CSV, Excel, and PDF formats for all supported modules.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to clients, projects, payments, expenses sections respectively"}, {"type": "action", "description": "Trigger export in CSV, Excel, and PDF formats for each module"}, {"type": "assertion", "description": "Verify each generated file contains the complete dataset matching current view/filter with correct formatting"}]}, {"id": "TC015", "title": "Password reset and forgot password flows", "description": "Verify user can request password reset, receives reset functionality, and successfully resets password.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to forgot-password page"}, {"type": "action", "description": "Submit registered email for password reset"}, {"type": "assertion", "description": "Verify confirmation message is shown for reset email sent"}, {"type": "action", "description": "Open received reset email and follow reset link"}, {"type": "action", "description": "Enter a new valid password and confirm"}, {"type": "assertion", "description": "Verify password is updated successfully and user can login with new password"}]}, {"id": "TC016", "title": "Performance under low-end hardware scenarios", "description": "Test responsiveness and load times of core pages and actions on low-end hardware devices simulating typical user conditions.", "category": "performance", "priority": "Medium", "steps": [{"type": "action", "description": "Access dashboard, client management, project management, and scheduling pages on test devices with limited resources"}, {"type": "assertion", "description": "Verify UI responsiveness remains acceptable and no errors or timeouts occur"}, {"type": "action", "description": "Perform data entry and navigation under load"}, {"type": "assertion", "description": "Confirm operations complete within acceptable performance benchmarks"}]}, {"id": "TC017", "title": "Row-level security on backend data access", "description": "Validate that data queries respect row-level security policies enforcing that users only retrieve data they are permitted to see.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Login as different user roles and attempt to access restricted data sets"}, {"type": "assertion", "description": "Verify unauthorized data is not accessible or returned in queries"}]}, {"id": "TC018", "title": "Notification system resilience without email integration", "description": "Check that all in-app notifications work reliably without email integration in version 1, ensuring timely delivery and handling.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Trigger various system alerts and notifications"}, {"type": "assertion", "description": "Verify notifications appear only in app and are not dependent on email"}]}, {"id": "TC019", "title": "Scheduling - manual entries and recurring events with reminders", "description": "Test creation of manual and recurring schedule entries with proper firing of calendar reminders.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Create manual schedule entry with date/time"}, {"type": "assertion", "description": "Verify schedule appears on calendar correctly"}, {"type": "action", "description": "Create a recurring schedule with defined repeat intervals"}, {"type": "assertion", "description": "Confirm recurring schedule replicates correctly on calendar"}, {"type": "action", "description": "Check that reminders trigger at scheduled times for both manual and recurring entries"}, {"type": "assertion", "description": "Verify reminder notifications appear timely in-app"}]}, {"id": "TC020", "title": "Security audit for authentication and authorization", "description": "Run comprehensive tests verifying secure authentication, authorization mechanisms, token handling, and backend access controls.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Test authentication flows for vulnerabilities such as brute-force, session hijacking, or token forgery"}, {"type": "action", "description": "Attempt unauthorized API or database access"}, {"type": "assertion", "description": "Confirm all unauthorized access attempts are blocked and logged"}]}]