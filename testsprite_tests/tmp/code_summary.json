{"tech_stacks": [{"name": "Next.js", "version": "15", "category": "Frontend Framework", "description": "React framework with App Router for server-side rendering and static generation"}, {"name": "React", "version": "19", "category": "UI Library", "description": "Component-based UI library for building interactive user interfaces"}, {"name": "TypeScript", "version": "latest", "category": "Programming Language", "description": "Strongly typed JavaScript for better code quality and developer experience"}, {"name": "Tailwind CSS", "version": "latest", "category": "CSS Framework", "description": "Utility-first CSS framework for rapid UI development"}, {"name": "Supabase", "version": "latest", "category": "Backend as a Service", "description": "PostgreSQL database with authentication and real-time features"}, {"name": "Google Maps API", "version": "latest", "category": "Maps Service", "description": "Location services, geocoding, and interactive maps functionality"}, {"name": "Microsoft Graph API", "version": "latest", "category": "Cloud Integration", "description": "SharePoint integration for file management and collaboration"}, {"name": "React Hook Form", "version": "latest", "category": "Form Management", "description": "Performant forms with easy validation"}, {"name": "Lucide React", "version": "latest", "category": "Icon Library", "description": "Beautiful and customizable SVG icons"}, {"name": "React Hot Toast", "version": "latest", "category": "Notification System", "description": "Toast notifications for user feedback"}], "features": [{"name": "User Authentication", "description": "Secure login/logout system with Supabase Auth, including signup, forgot password, and reset password functionality", "pages": ["/login", "/signup", "/forgot-password", "/reset-password"], "components": ["AuthContext", "ProtectedRoute", "LogoutButton"]}, {"name": "Dashboard Analytics", "description": "Real-time project statistics and overview with performance monitoring and analytics", "pages": ["/dashboard"], "components": ["dashboard-analytics", "performance-monitor", "realtime-indicator"]}, {"name": "Project Management", "description": "Complete CRUD operations for projects with detailed project cards and forms", "pages": ["/projects", "/projects/[id]"], "components": ["ProjectForm", "project-card"]}, {"name": "Schedule Management", "description": "Drone operation scheduling system with calendar integration and completion tracking", "pages": ["/schedules", "/schedules/[id]", "/calendar"], "components": ["ScheduleForm", "schedule-card", "schedule-completion-form", "calendar"]}, {"name": "Task Management", "description": "Advanced task tracking with assignment, priority levels, status management, and optimized task cards", "pages": ["/tasks", "/admin/tasks"], "components": ["TaskForm", "task-card", "enhanced-task-card", "optimized-task-card", "redesigned-task-card", "task-assignment-modal", "task-completion-form", "admin-task-manager"]}, {"name": "Client Management", "description": "Comprehensive client database with contact person management and client selector", "pages": ["/clients", "/clients/[id]"], "components": ["ClientForm", "ContactPersonForm", "ContactPersonsList", "client-selector", "contact-person-selector"]}, {"name": "Vendor Management", "description": "Vendor database with payment tracking and vendor shoot management", "pages": ["/vendors", "/vendors/[id]"], "components": ["VendorForm", "VendorPaymentForm", "RecordVendorPaymentForm", "vendor-payment-card"]}, {"name": "Shoot Management", "description": "Drone shoot tracking with completion forms and vendor integration", "pages": ["/shoots", "/shoots/[id]"], "components": ["ShootForm", "shoot-completion-form"]}, {"name": "Financial Management", "description": "Expense tracking, payment management, and interactive spending analytics", "pages": ["/finances"], "components": ["ExpenseForm", "AddExpenseForm", "PaymentForm", "expense-card", "payment-card", "payment-progress-bar", "interactive-spending-chart"]}, {"name": "Interactive Maps", "description": "Google Maps integration with heatmap visualization, location search, and flight planning", "pages": ["/map"], "components": ["heatmap-viewer", "location-search", "location-analytics", "flight-planner"]}, {"name": "Notification System", "description": "Comprehensive in-app notifications with management interface and real-time updates", "pages": [], "components": ["notification-bell", "notification-management"]}, {"name": "Settings & Profile", "description": "User profile management and application settings", "pages": ["/profile", "/settings"], "components": []}, {"name": "Background Jobs", "description": "Automated background processing and job management system", "pages": [], "components": ["background-jobs-initializer"]}, {"name": "SharePoint Integration", "description": "Microsoft SharePoint integration for file management and folder synchronization", "pages": [], "components": []}, {"name": "Progressive Web App", "description": "PWA capabilities with service worker registration and install prompts", "pages": [], "components": ["pwa-install", "pwa-install-backup", "service-worker-registration"]}, {"name": "Performance Monitoring", "description": "Real-time performance monitoring and optimization tools", "pages": ["/debug-tasks", "/debug-default-tasks", "/test-tasks"], "components": ["PerformanceMonitor", "task-creation-debug"]}]}