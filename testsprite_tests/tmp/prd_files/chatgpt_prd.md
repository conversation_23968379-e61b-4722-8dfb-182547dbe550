# Product Requirement Document (PRD)

**Project Name:** Cymatics Internal Web App

## 1. Introduction

### 1.1 Overview

Cymatics is a drone services company offering aerial survey, mapping, videography, and editing services. The internal web app will streamline **project management, scheduling, client handling, billing, and operations**, while maintaining offline-first capabilities and role-based access. The app will also integrate with **OneDrive** for file management and include export/reporting features.

### 1.2 Goals

* Centralize all business operations (projects, clients, payments, scheduling, and tasks).
* Enable **role-based access** for Admin, Manager, Pilot, and Editor.
* Support **offline-first data entry** with sync to cloud (Next.js + TypeScript frontend, Supabase/PG backend).
* Ensure data security and productivity tracking.
* Provide **scalable architecture** for future client portal integration.

---

## 2. Users & Roles

### 2.1 User Types

* **Admin**

  * Full access (users, financials, settings).
* **Manager**

  * Manage projects, clients, schedules, and files.
  * Access to financials.
* **Pilot**

  * Access only to assigned projects/schedules.
  * File uploads (Raw/Output).
* **Editor**

  * Access assigned projects.
  * Upload/edit outputs. No financial access.

### 2.2 Role Permissions Matrix

| Feature               | Admin | Manager | Pilot | Editor |
| --------------------- | ----- | ------- | ----- | ------ |
| Create/Edit Clients   | ✅     | ✅       | ❌     | ❌      |
| Create/Edit Projects  | ✅     | ✅       | ❌     | ❌      |
| Assign Pilots/Editors | ✅     | ✅       | ❌     | ❌      |
| Upload Files          | ✅     | ✅       | ✅     | ✅      |
| Access Financial Data | ✅     | ✅       | ❌     | ❌      |
| View/Edit Tasks       | ✅     | ✅       | ✅     | ✅      |
| Manage Users          | ✅     | ❌       | ❌     | ❌      |
| Export Data           | ✅     | ✅       | ❌     | ❌      |

---

## 3. Core Features

### 3.1 Dashboard

* Metrics: Active Projects, Upcoming Shoots, Pending Payments, Overdue Tasks.
* Notifications: Shoot reminders, pending payments, file upload confirmations.

### 3.2 Clients Module

* Add/Edit clients (company name, contact person, email, phone, GST number, address).
* Multiple projects per client.
* Export client data (CSV, Excel, PDF).

### 3.3 Projects Module

* **Project Details**: Name, client, type (survey, shoot, mapping, editing), location (with Google Maps link), total value, GST/Non-GST, status (planned/ongoing/completed).
* **Schedules**: Supports both recurring and manual scheduling.
* **File Uploads**: OneDrive integration → `/Client/Project/ShootDate/Raw` and `/Output`.
* **Card-Style UI** instead of tables.
* **Location Map View**: All projects plotted via Google Maps.

### 3.4 Shoots & Scheduling

* Manual and recurring scheduling.
* Assign pilots/editors.
* Calendar integration for reminders.

### 3.5 Files & Deliverables

* Upload to OneDrive automatically.
* Folder structure enforced: `/Client/Project/ShootDate/{Raw, Output}`.
* File access role-based (Pilots/Editors only see their projects).

### 3.6 Payments & Billing

* Track invoices (GST/Non-GST toggle).
* Record payments (full/partial/delayed).
* Pending payments alert.
* Export invoices & payment data.

### 3.7 Task Management (Post-Processing)

* Manual task creation for editing/mapping workflows.
* Assign tasks to editors.
* Status tracking (To Do → In Progress → Completed).
* Client update logs (manual).

### 3.8 Expense & Budget Tracking

* Add/Edit expenses (offline-first).
* Role restriction: Only Admin/Manager view; workers can add.
* Monthly and yearly reports.

### 3.9 Productivity Tracking

* Logs of activities (uploads, edits, project completion).
* Excludes Admins.

### 3.10 Notifications System

* In-app only (no emails for v1).
* Types: Shoot reminders, pending payments, delayed tasks.

### 3.11 Data Export

* Support export to **CSV, Excel, PDF** for clients, projects, payments, and expenses.

---

## 4. Non-Functional Requirements

### 4.1 Performance

* Offline-first capability (local storage → sync to Supabase when online).
* Smooth performance on low-end devices.

### 4.2 Security

* Role-based authentication and authorization.
* Supabase/PostgreSQL backend with RLS (Row-Level Security).

### 4.3 Scalability

* Designed for future **client portal extension**.
* Modular architecture for adding new services.

### 4.4 Backup & Storage

* OneDrive as primary storage.
* NAS backup (future phase).

---

## 5. Future Enhancements

* Client portal for project and invoice tracking.
* Automated billing and GST invoice generation.
* AI-based productivity insights.
* Mobile app companion (React Native).

---

## 6. Tech Stack

* **Frontend:** Next.js + TypeScript (PWA, offline-first).
* **Backend:** Supabase (PostgreSQL, Auth, Storage).
* **Storage:** OneDrive integration.
* **Maps API:** Google Maps.
* **Exports:** CSV, Excel, PDF.
