{"next_action": [{"type": "tool_use", "tool": "llm.generate", "input": {"prompt": "\nYou are a software collaborator with two roles:\n1. Assist in documenting testing outcomes.\n2. Support the engineering team by identifying what functionality needs fixing.\nThe test is already complete. You are provided with a test result JSON object named testResult.\nYour job is to **generate report files for user** based on the contents of testResult.\n---\nYou MUST perform the following:\n### Generate Markdown Report\n- Extract all the test cases from testCaseResults.\n- Use this data to generate a standardized **Markdown** test report.\n- Follow the structure of reportTemplate.\n- Use tool \"file.write\" to save this report as a file `testsprite_tests/testsprite-mcp-test-report.md` in the project directory.\n\n---\nYou must include every test case from testResult, list them one by one.\n---\n### Start generating the following file contents now:\n The full markdown report content (for `testsprite-mcp-test-report.md}`)\n---\n## Markdown Report Format:\n{{ Refer to schema }}\n\nAdditional Requirements:\n- The report must strictly follow the template style grouping (each ### Requirement: has multiple #### Test), each case must be classified under the appropriate requirement.\n- The Description under each Requirement can be automatically generated by combining the component and description of the test case.\n- Cases that cannot be classified should form a separate Requirement.\n\nYou must strictly follow these principles:\n- Field placeholders: use N/A if field does not exist  \n- **Project Name:** Use the project root directory name as the project name (e.g., voiceAgent-jiangzhang). If a .git repository name is available, use that instead.\n- **Version:** Manually check package.json in the project root. If the file exists, extract the version field; otherwise, use N/A.\n- **Code Repo:** Use the project root directory name as the project name (e.g., voiceAgent-jiangzhang). If a .git repository name is available, use that instead.\n- **Date:** 2025-09-13 (IMPORTANT: you must use the exact date string here.)\n- **Prepared by:** TestSprite AI Team\n- **Test Results:** testsprite-mcp-test-report.md\n- **Test Error:** Test cases that have passed do not contain the Test Error field or N/A.\n ", "schema": "\n# TestSprite AI Testing Report(MCP)\n\n---\n\n## 1️⃣ Document Metadata\n- **Project Name:** {project name}\n- **Version:** {MAJOR.MINOR.PATCH}\n- **Date:** {YYYY-MM-DD}\n- **Prepared by:** TestSprite AI Team\n\n---\n\n## 2️⃣ Requirement Validation Summary\n\n### Requirement: User Login\n- **Description:** Supports email/password login with validation.\n\n#### Test 1\n- **Test ID:** TC001\n- **Test Name:** Validate correct login with valid credentials.\n- **Test Code:** [code_file](./TC001_Validate_correct_login_with_valid_credentials.py)\n- **Test Error:** \n- **Test Visualization and Result:**\n- **Status:** ✅ Passed\n- **Severity:** LOW\n- **Analysis / Findings:** <PERSON><PERSON> works as expected for valid user credentials.\n---\n\n#### Test 2\n- **Test ID:** TC002\n- **Test Name:** Reject login with incorrect password.\n- **Test Code:** [code_file](./TC002_Reject_login_with_incorrect_password.py)\n- **Test Error:** \n- **Test Visualization and Result:**\n- **Status:** ✅ Passed\n- **Severity:** LOW\n- **Analysis / Findings:** Correct error message shown. No security issues found.\n\n---\n\n#### Test 3\n- **Test ID:** TC003\n- **Test Name:** Lock account after 5 failed attempts.\n- **Test Code:** [code_file](./TC003_Lock_account_after_5_failed_attempts.py)\n- **Test Error:** \n- **Test Visualization and Result:**\n- **Status:** ⚠️ Partial\n- **Severity:** LOW\n- **Analysis / Findings:** Lock occurs, but error message not displayed consistently. Suggest adding explicit UI feedback.\n\n---\n\n### Requirement: User Signup\n- **Description:** Allows signup, validates email format.\n\n#### Test 1\n- **Test ID:** TC004\n- **Test Name:** Successful signup with valid email and password.\n- **Test Code:** [code_file](./TC004_Successful_signup_with_valid_email_and_password.py)\n- **Test Error:** \n- **Test Visualization and Result:**\n- **Status:** ✅ Passed\n- **Severity:** LOW\n- **Analysis / Findings:** Signup works as expected. Welcome email sent.\n\n---\n\n#### Test 2\n- **Test ID:** TC005\n- **Test Name:** Reject signup with invalid email.\n- **Test Code:** [code_file](./TC005_Reject_signup_with_invalid_email.py)\n- **Test Error:** \n- **Test Visualization and Result:**\n- **Status:** ⚠️ Partial\n- **Severity:** LOW\n- **Analysis / Findings:** Invalid email accepted — regex validation missing in code. Suggest adding client-side and server-side validation.\n\n---\n\n### Requirement: Password Reset\n- **Description:** Allows password reset via email.\n- **Test:** N/A  \n- **Status:** ❌ Not Tested\n\n- **Analysis / Findings:** No test generated. Feature not implemented in codebase.\n\n---\n\n## 3️⃣ Coverage & Matching Metrics\n\n- 85% of product requirements tested** \n- 70% of tests passed** \n- **Key gaps / risks:**  \nExample:  \n> 85% of product requirements had at least one test generated.  \n> 70% of tests passed fully.  \n> Risks: No password reset implementation; signup form missing edge validation.\n\n| Requirement        | Total Tests | ✅ Passed | ⚠️ Partial | ❌ Failed |\n|--------------------|-------------|-----------|-------------|------------|\n| (e.g. User Login)  | (e.g. 3)    | (e.g. 1)  | (e.g. 0)    | (e.g. 2)   |\n| ...                | ...         | ...       | ...         | ...        |\n---\n", "testResult": [{"testCaseId": "TC001", "failureReason": "Test passed confirming that the login functionality correctly authenticates valid user credentials, allowing access and redirecting appropriately.", "component": "LoginForm", "recommendation": "No critical issues detected; consider adding UI feedback enhancements for accessibility and better user experience.", "severity": "Low", "testCode": "[TC001_User_login_with_valid_credentials.py](./TC001_User_login_with_valid_credentials.py)", "testTitle": "User login with valid credentials", "testStatus": "PASSED", "description": "Verify that a user can successfully log in with valid username/email and password credentials.", "testError": "", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/e54e41b6-297f-4d73-90b9-3ad3252fc0e0"}, {"testCaseId": "TC002", "failureReason": "Login functionality fails to block access on invalid credentials and incorrectly redirects to dashboard without showing error messages, causing a critical authentication security flaw.", "component": "LoginForm", "recommendation": "Fix authentication logic to verify credential correctness and display clear error messages on invalid login attempts. Ensure backend validation is enforced and frontend handles errors properly.", "severity": "High", "testCode": "[TC002_User_login_with_invalid_credentials.py](./TC002_User_login_with_invalid_credentials.py)", "testTitle": "User login with invalid credentials", "testStatus": "FAILED", "description": "Verify that login attempt with incorrect username/email or password is denied and proper error message is shown.", "testError": "The login attempt with incorrect username/email or password was not denied as expected. Instead, the system allowed login and redirected to the dashboard page without showing any error message. This is a critical authentication issue that needs to be addressed. Task is now complete.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 1131.80ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 38308.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/c85db7e6-8344-4006-99df-54f7d6f5a124"}, {"testCaseId": "TC003", "failureReason": "Testing halted due to missing logout functionality preventing role switching and thus full role-based access validation.", "component": "Dashboard / User Session Management", "recommendation": "Implement and make visible a functional logout option to enable role switching and complete role-based access control testing.", "severity": "High", "testCode": "[TC003_Role_based_access_control_enforcement.py](./TC003_Role_based_access_control_enforcement.py)", "testTitle": "Role-based access control enforcement", "testStatus": "FAILED", "description": "Verify users with <PERSON><PERSON>, Manager, Pilot, and Editor roles have correct access permissions per their role restrictions.", "testError": "Testing stopped due to missing logout functionality. Admin role access verified but unable to proceed with Manager, Pilot, and Editor roles. Logout option not found on dashboard or other pages, blocking further testing.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] [Fast Refresh] performing full reload\n\nFast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\nYou might have a file which exports a React component but also exports a value that is imported by a non-React component file.\nConsider migrating the non-React component export to a separate file and importing it into both files.\n\nIt is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\nFast Refresh requires at least one parent function component in your React tree. (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/hot-reloader/app/hot-reloader-app.js:111:24)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 1303.70ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 47768.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/f49d506d-c23a-4551-8648-be86355f1644"}, {"testCaseId": "TC004", "failureReason": "Offline mode activation cannot be tested because Settings link does not navigate or reveal required offline mode options, blocking offline data entry and sync validation.", "component": "Dashboard / Settings Page", "recommendation": "Fix navigation and rendering for the Settings link to expose offline mode activation controls; validate offline functionality with proper UI elements.", "severity": "High", "testCode": "[TC004_Offline_first_data_entry_and_sync.py](./TC004_Offline_first_data_entry_and_sync.py)", "testTitle": "Offline-first data entry and sync", "testStatus": "FAILED", "description": "Validate that data can be entered and saved offline and synchronizes correctly with the Supabase backend once connection is restored.", "testError": "Testing stopped due to inability to access offline mode activation settings. The Settings link on the dashboard does not navigate or reveal offline mode options, preventing further offline mode testing and data synchronization validation.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] Slow operation detected: api:projects.getAll took 801.90ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Poor LCP: 52380.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/4b2220d4-cbe5-4044-b435-93ec390e4190"}, {"testCaseId": "TC005", "failureReason": "Exported error logs show script fetching failure and warnings; test result implies failure possibly due to UI interaction or data fetching issues impacting dashboard metrics display.", "component": "Dashboard Metrics Display", "recommendation": "Investigate 404 errors on JavaScript assets, fix broken script references, and verify backend API responses to ensure correct real-time metrics display and notifications.", "severity": "High", "testCode": "[TC005_Dashboard_displays_correct_active_metrics_and_notifications.py](./TC005_Dashboard_displays_correct_active_metrics_and_notifications.py)", "testTitle": "Dashboard displays correct active metrics and notifications", "testStatus": "FAILED", "description": "Confirm the dashboard shows accurate real-time counts of active projects, upcoming shoots, pending payments, overdue tasks, and notifications.", "testError": "🖱️  Clicked button with index 5: \nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 1314.40ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 39668.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /projects. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /projects. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /schedules. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /schedules. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key. (at webpack-internal:///(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js:84:20)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/036e7ae6-5ea0-475a-b92a-e8f6077e4c12"}, {"testCaseId": "TC006", "failureReason": "Client data export functionality fails to trigger file downloads, blocking verification of export capabilities across supported formats.", "component": "Clients Management UI", "recommendation": "Fix the export feature to properly initiate file downloads for CSV, Excel, and PDF by resolving frontend event handling and related backend/export service dependencies.", "severity": "High", "testCode": "[TC006_Client_creation_editing_and_export.py](./TC006_Client_creation_editing_and_export.py)", "testTitle": "Client creation, editing and export", "testStatus": "FAILED", "description": "Verify that clients can be added, updated, and exported correctly in CSV, Excel, and PDF formats.", "testError": "Tested adding a new client, updating client details, and attempted to export clients data in CSV format. Export functionality did not work as expected; clicking the export button did not trigger any file download or export action. Stopping further testing due to this issue.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 1421.90ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] Failed to load resource: the server responded with a status of 404 () (at https://qxgfulribhhohmggyroq.supabase.co/rest/v1/rpc/peek_next_entity_id:0:0)\n[ERROR] Failed to load resource: the server responded with a status of 404 () (at https://qxgfulribhhohmggyroq.supabase.co/rest/v1/rpc/peek_next_entity_id:0:0)\n[WARNING] peekNextId RPC failed: {code: PGRST202, details: Searched for the function public.peek_next_entity_…r, but no matches were found in the schema cache., hint: Perhaps you meant to call the function public.peek_next_structured_id, message: Could not find the function public.peek_next_entity_id(entity_type) in the schema cache} (at webpack-internal:///(app-pages-browser)/./src/lib/api.ts:33:20)\n[WARNING] peekNextId RPC failed: {code: PGRST202, details: Searched for the function public.peek_next_entity_…r, but no matches were found in the schema cache., hint: Perhaps you meant to call the function public.peek_next_structured_id, message: Could not find the function public.peek_next_entity_id(entity_type) in the schema cache} (at webpack-internal:///(app-pages-browser)/./src/lib/api.ts:33:20)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/7a170572-440a-4225-9de5-97ff81185ed6"}, {"testCaseId": "TC007", "failureReason": "Project creation blocked due to Primary Contact dropdown failing to show selectable options, preventing completion of project lifecycle tests.", "component": "Project Creation Form", "recommendation": "Fix data loading or rendering logic for Primary Contact dropdown to load available contacts correctly to enable project creation flow.", "severity": "High", "testCode": "[TC007_Project_lifecycle_management_with_map_visualization.py](./TC007_Project_lifecycle_management_with_map_visualization.py)", "testTitle": "Project lifecycle management with map visualization", "testStatus": "FAILED", "description": "Test creation, editing, scheduling (manual and recurring), assignment, and project details including Google Maps location visualization.", "testError": "Stopped testing due to critical issue: Primary Contact dropdown does not show options, preventing project creation. Please fix this issue to continue testing.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 1228.40ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 36516.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /projects. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /projects. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/0dc810c3-6a55-4edc-834e-f015e9879a9e"}, {"testCaseId": "TC008", "failureReason": "Shoot management and calendar reminder testing blocked possibly due to UI or script loading errors as indicated by logged 404 script errors, causing test failure.", "component": "Shoot Management UI / Calendar Integration", "recommendation": "Resolve missing script resources and performance issues impacting interaction with shoot scheduling and calendar reminders to allow complete test execution.", "severity": "High", "testCode": "[TC008_Shoot_management_with_calendar_integration_and_reminders.py](./TC008_Shoot_management_with_calendar_integration_and_reminders.py)", "testTitle": "Shoot management with calendar integration and reminders", "testStatus": "FAILED", "description": "Validate the creation, updating, and completion tracking of shoots including calendar reminders firing appropriately.", "testError": "🖱️  Clicked button with index 5: \nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 1800.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 50772.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /projects. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /projects. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /vendors. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /vendors. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key. (at webpack-internal:///(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js:84:20)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/89c93b44-60b2-4dc5-a7c9-80b8da6cf1b9"}, {"testCaseId": "TC009", "failureReason": "Test execution timed out after 15 minutes indicating severe performance or blocking issues during file upload and OneDrive folder structure validation.", "component": "File Uploads / OneDrive Integration", "recommendation": "Investigate and fix backend or frontend bottlenecks causing timeout. Ensure folder creation and access control mechanisms complete within expected time limits.", "severity": "High", "testCode": "", "testTitle": "File uploads and OneDrive folder structure validation", "testStatus": "FAILED", "description": "Test uploading files with automatic folder creation and enforcement on OneDrive with correct hierarchical folder structure and role-based access.", "testError": "Test execution timed out after 15 minutes", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/328121ed-165c-4afe-ac2e-3fe290055793"}, {"testCaseId": "TC010", "failureReason": "Payments and billing system test execution timed out, blocking validation of invoice entry, payment tracking, alerts, and export features.", "component": "Payments and Billing UI / Backend", "recommendation": "Identify and resolve performance or blocking issues in payment processing UI and backend APIs to ensure responsiveness and complete test coverage.", "severity": "High", "testCode": "", "testTitle": "Payments and billing system with alerts and export", "testStatus": "FAILED", "description": "Verify invoice entry, payment tracking, alert generation for pending dues, and exporting of payments data.", "testError": "Test execution timed out after 15 minutes", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/1b40c80d-57a7-469b-b174-09c9f0be526c"}, {"testCaseId": "TC011", "failureReason": "Task management lifecycle test timed out preventing verification of task assignment, updates, and client update logging.", "component": "Task Management UI", "recommendation": "Diagnose backend or frontend causes of timeout. Optimize task update handling and ensure client logging is reliably processed during task lifecycle operations.", "severity": "High", "testCode": "", "testTitle": "Task management lifecycle including assignment and status updates", "testStatus": "FAILED", "description": "Test creation, assignment, progress updating, and completion of post-processing tasks with client update logs.", "testError": "Test execution timed out after 15 minutes", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/75a92015-cc41-404f-9d35-36f39edfa0f9"}, {"testCaseId": "TC012", "failureReason": "Expense and budget tracking tests timed out, inhibiting validation of offline mode support, role-based visibility, and reporting features.", "component": "Expense Tracking / Offline Support Module", "recommendation": "Fix offline mode activation and ensure data syncing components operate efficiently to avoid timeouts, also validate role restrictions properly enforce data visibility.", "severity": "High", "testCode": "", "testTitle": "Expense and budget tracking with offline mode and reports", "testStatus": "FAILED", "description": "Validate expense entries with offline support, role-based visibility restrictions, and generation of expense reports.", "testError": "Test execution timed out after 15 minutes", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/595c2a3c-1700-4871-926e-f4526be60f4b"}, {"testCaseId": "TC013", "failureReason": "In-app notification delivery and handling failed due to multiple script errors and loading issues preventing timely notification testing.", "component": "Notification System UI", "recommendation": "Resolve load failures and script errors to enable reliable notification triggering and user interaction capabilities.", "severity": "High", "testCode": "[TC013_In_app_notification_delivery_and_handling.py](./TC013_In_app_notification_delivery_and_handling.py)", "testTitle": "In-app notification delivery and handling", "testStatus": "FAILED", "description": "Verify that notifications for reminders and alerts on shoots, payments, and tasks are delivered timely within the app and can be interacted with.", "testError": "\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 1532.70ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 39896.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /schedules. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /schedules. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key. (at webpack-internal:///(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js:84:20)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/a74f2b16-e14d-4978-b4b4-638b13f3ffef"}, {"testCaseId": "TC014", "failureReason": "Export functionality on Clients page is missing or inaccessible, stopping tests for data exports across multiple modules.", "component": "Clients Page / Export Module", "recommendation": "Add or fix visibility and accessibility of export features with robust export implementations for all supported formats. Ensure UI elements function as expected.", "severity": "High", "testCode": "[TC014_Export_functionality_for_clients_projects_payments_and_expenses.py](./TC014_Export_functionality_for_clients_projects_payments_and_expenses.py)", "testTitle": "Export functionality for clients, projects, payments, and expenses", "testStatus": "FAILED", "description": "Ensure exports produce accurate, complete data files in CSV, Excel, and PDF formats for all supported modules.", "testError": "Export functionality on Clients page is not accessible or missing. Unable to complete export tests for Clients module. Stopping further actions as per instructions.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 953.80ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 37000.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/714c7063-149d-4049-9a69-8cd3f2a5f19a"}, {"testCaseId": "TC015", "failureReason": "Forgot-password link is missing on login page, preventing initiation and testing of password reset flows.", "component": "LoginForm / Password Reset Feature", "recommendation": "Add the 'forgot-password' link and ensure password reset workflow is fully accessible and functional from login page.", "severity": "High", "testCode": "[TC015_Password_reset_and_forgot_password_flows.py](./TC015_Password_reset_and_forgot_password_flows.py)", "testTitle": "Password reset and forgot password flows", "testStatus": "FAILED", "description": "Verify user can request password reset, receives reset functionality, and successfully resets password.", "testError": "The password reset functionality could not be fully tested because the 'forgot-password' link is missing on the login page, preventing initiation of the reset process. This issue has been reported. Task is now complete.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/ab408ddc-9a51-4e86-a045-2174f4051631"}, {"testCaseId": "TC016", "failureReason": "Performance under low-end hardware test timed out due to slow response and loading issues, impacting core page functionality verification.", "component": "Core Pages / Performance and Load Management", "recommendation": "Optimize rendering paths, script loading, and backend response times to improve performance on low-end devices and prevent timeout failures.", "severity": "High", "testCode": "[TC016_Performance_under_low_end_hardware_scenarios.py](./TC016_Performance_under_low_end_hardware_scenarios.py)", "testTitle": "Performance under low-end hardware scenarios", "testStatus": "FAILED", "description": "Test responsiveness and load times of core pages and actions on low-end hardware devices simulating typical user conditions.", "testError": "🖱️  Clicked button with index 5: \nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 929.60ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 39404.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /projects. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /projects. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /schedules. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /schedules. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key. (at webpack-internal:///(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js:84:20)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/5d8220d2-94c1-4024-bbbe-b8478ba51b4c"}, {"testCaseId": "TC017", "failureReason": "Row-level security tests stopped due to missing logout functionality blocking role switching necessary to validate data access restrictions.", "component": "Settings Page / User Session Management", "recommendation": "Restore and expose logout option to facilitate role switching and enable comprehensive row-level security validations.", "severity": "High", "testCode": "[TC017_Row_level_security_on_backend_data_access.py](./TC017_Row_level_security_on_backend_data_access.py)", "testTitle": "Row-level security on backend data access", "testStatus": "FAILED", "description": "Validate that data queries respect row-level security policies enforcing that users only retrieve data they are permitted to see.", "testError": "Testing stopped due to missing or non-functional logout option on the Settings page, preventing switching user roles to validate row-level security policies. Please fix logout functionality to continue testing.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 884.60ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 39076.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] Poor LCP: 42440.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /clients. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /settings. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /settings. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/30392330-690e-4dfe-9e77-6e1f044a7393"}, {"testCaseId": "TC018", "failureReason": "Partial in-app notification testing was successful but testing of 'Schedule Session' notification blocked by non-responsive button preventing full coverage.", "component": "Notification System UI", "recommendation": "Fix button responsiveness and related event handling to enable testing and proper functioning of all notification types.", "severity": "Medium", "testCode": "[TC018_Notification_system_resilience_without_email_integration.py](./TC018_Notification_system_resilience_without_email_integration.py)", "testTitle": "Notification system resilience without email integration", "testStatus": "FAILED", "description": "Check that all in-app notifications work reliably without email integration in version 1, ensuring timely delivery and handling.", "testError": "Testing of in-app notifications without email integration is partially complete. New project creation and payment received notifications triggered successfully. However, the 'Schedule Session' notification could not be tested due to a non-responsive button. Please fix this issue to enable full testing coverage.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 705.60ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 38284.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/28b7a0d7-2571-484b-a24c-ced982cde030"}, {"testCaseId": "TC019", "failureReason": "Scheduling tests for manual and recurring events timed out, preventing confirmation of reminder firing and event entry functionalities.", "component": "Scheduling Module / Calendar Integration", "recommendation": "Investigate timeout causes and optimize scheduling workflows and backend API responsiveness to complete testing cycles successfully.", "severity": "High", "testCode": "", "testTitle": "Scheduling - manual entries and recurring events with reminders", "testStatus": "FAILED", "description": "Test creation of manual and recurring schedule entries with proper firing of calendar reminders.", "testError": "Test execution timed out after 15 minutes", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/d8350e02-cc94-470e-bd42-2b1771fdbaa9"}, {"testCaseId": "TC020", "failureReason": "Overall security features show good enforcement, but incomplete token handling tests and UI navigation issues to project lists limited full backend access control verification.", "component": "Authentication and Authorization System", "recommendation": "Improve visibility and access to token storage/info for thorough token handling testing; resolve UI navigation issues to enable comprehensive backend access control audits.", "severity": "Medium", "testCode": "[TC020_Security_audit_for_authentication_and_authorization.py](./TC020_Security_audit_for_authentication_and_authorization.py)", "testTitle": "Security audit for authentication and authorization", "testStatus": "FAILED", "description": "Run comprehensive tests verifying secure authentication, authorization mechanisms, token handling, and backend access controls.", "testError": "Comprehensive security tests were performed on authentication, authorization, token handling, and backend access controls. Authentication flow was verified with successful login. Unauthorized access attempts, including adding users without permission and accessing restricted APIs, were blocked or returned errors, confirming access control enforcement. Token handling tests were limited by lack of accessible token info and storage data. Backend access control verification was incomplete due to UI navigation issues preventing access to project lists. Overall, the system shows good security enforcement in tested areas, but improvements are needed in token handling visibility and UI navigation for full backend access control verification.\nBrowser Console Logs:\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] Slow operation detected: api:projects.getAll took 986.30ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:29:20)\n[WARNING] Timer 'api:projects.getAll' was not started (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:17:20)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] 🔔 Notifications API disabled, using empty data (at webpack-internal:///(app-pages-browser)/./src/hooks/useNotifications.ts:23:32)\n[WARNING] Poor LCP: 46600.00ms (at webpack-internal:///(app-pages-browser)/./src/lib/performance.ts:124:28)\n[WARNING] API call failed, using fallback data: Failed to fetch (at webpack-internal:///(app-pages-browser)/./src/hooks/useApi.ts:72:20)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[ERROR] A bad HTTP response code (404) was received when fetching the script. (at :0:0)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)\n[WARNING] %c%s%c  \u001b[33m\u001b[1m⚠\u001b[22m\u001b[39m Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server   (at webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:3172:21)", "testVisualizationAndResult": "https://www.testsprite.com/dashboard/mcp/tests/92257c9f-e59a-4f1d-a12a-fac951a2aea9/44e8eb4c-c170-4e9f-83e9-e34f46c1013f"}]}}]}